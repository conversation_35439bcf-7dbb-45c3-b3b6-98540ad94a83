version: '3.7'
services:
  adp_fx_backend:
    image: ${IMG_ADP_FX_BACKEND}
    volumes:
    - /ms/logs/adp_fx_backend:/usr/local/athenadesigner/log4jDirKey_IS_UNDEFINED/logs
    ports:
    - 22621:8000
    env_file:
    - /ms/deployment/adp_fx_backend.env
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: '2'
          memory: 3G
        reservations:
          cpus: '2'
          memory: 3G
      update_config:
        parallelism: 1
        delay: 120s
        failure_action: rollback
        order: stop-first
      restart_policy:
        condition: on-failure
        delay: 60s
        window: 120s
      rollback_config:
        parallelism: 0
        failure_action: continue
        order: stop-first
    healthcheck:
      test:
      - CMD
      - curl
      - http://localhost:8000/athena-designer/healthcheck
      interval: 10s
      timeout: 2s
      retries: 3
      start_period: 120s
    networks:
    - ocss-net
  adp_fx_frontend:
    image: ${IMG_ADP_FX_FRONTEND}
    ports:
    - 22682:80
    env_file:
    - /ms/deployment/adp_fx_frontend.env
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 0s
        order: stop-first
      restart_policy:
        condition: on-failure
        delay: 0s
        window: 10s
      rollback_config:
        parallelism: 0
        failure_action: continue
        order: stop-first
    networks:
    - ocss-net
  backend:
    image: ${IMG_BACKEND}
    volumes:
    - /ms/logs:/DWApiGateway/WEB-INF/classes/log
    ports:
    - 22620:22622
    env_file:
    - backend-defaults.env
    - backend.env
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '4'
          memory: 7G
        reservations:
          cpus: '2'
          memory: 7G
      update_config:
        parallelism: 1
        delay: 120s
        failure_action: rollback
        order: stop-first
      restart_policy:
        condition: on-failure
        delay: 60s
        window: 120s
      rollback_config:
        parallelism: 0
        failure_action: continue
        order: stop-first
    healthcheck:
      test:
      - CMD
      - curl
      - http://localhost:22622/thirdparty/service/A/status/getOcssStatus
      interval: 10s
      timeout: 2s
      retries: 3
      start_period: 120s
    networks:
    - ocss-net
  redis:
    image: ${IMG_REDIS}
    volumes:
    - type: bind
      source: /ms/redis/redis.conf
      target: /usr/local/etc/redis/redis.conf
    deploy:
      replicas: 1
      update_config:
        parallelism: 1
        failure_action: rollback
        order: start-first
      restart_policy:
        condition: any
        delay: 0s
        window: 10s
      rollback_config:
        parallelism: 0
        failure_action: continue
        order: stop-first
    networks:
    - ocss-net
  eshop:
    image: ${IMG_ESHOP}
    ports:
    - 22690:80
    env_file:
    - frontend.env
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 0s
        order: stop-first
      restart_policy:
        condition: on-failure
        delay: 0s
        window: 10s
      rollback_config:
        parallelism: 0
        failure_action: continue
        order: stop-first
    networks:
    - ocss-net
  mobile:
    image: ${IMG_MOBILE}
    ports:
    - 22480:80
    env_file:
    - mobile.env
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 0s
        order: stop-first
      restart_policy:
        condition: on-failure
        delay: 0s
        window: 10s
      rollback_config:
        parallelism: 0
        failure_action: continue
        order: stop-first
    networks:
    - ocss-net
  frontend52:
    image: ${IMG_FRONTEND52}
    ports:
    - 22680:8000
    env_file:
    - frontend.env
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 0s
        order: stop-first
      restart_policy:
        condition: on-failure
        delay: 0s
        window: 10s
      rollback_config:
        parallelism: 0
        failure_action: continue
        order: stop-first
    networks:
    - ocss-net
  frontend:
    image: ${IMG_FRONTEND}
    ports:
    - 22681:8000
    env_file:
    - frontend.env
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 0s
        order: stop-first
      restart_policy:
        condition: on-failure
        delay: 0s
        window: 10s
      rollback_config:
        parallelism: 0
        failure_action: continue
        order: stop-first
    networks:
    - ocss-net
networks:
  ocss-net:
    driver: overlay
