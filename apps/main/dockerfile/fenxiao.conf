
upstream frontend52 {
  server 127.0.0.1:22680;
}
upstream frontend {
  server 127.0.0.1:22681;
}
upstream eshop {
  server 127.0.0.1:22690;
}
upstream backend {
  server 127.0.0.1:22620;
}
upstream mobile {
  server 127.0.0.1:22480;
}
upstream fdfs_group {
#    server **************:8888;
   server *************:8888;
}
upstream ueditor {
#    server **************:8080;
   server *************:8080;
}
upstream adp_fx_backend {
  server 127.0.0.1:22621;
}
upstream adp_fx_frontend {
  server 127.0.0.1:22682;
}
server {

  listen 80;

  include /etc/nginx/conf.d/fenxiao/*.conf;

   location / {
    index index.html index.htm;
    proxy_pass http://frontend52/;
    proxy_http_version 1.1;
    proxy_pass_header Server;
    proxy_set_header Host $http_host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto https;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Scheme $scheme;
    proxy_connect_timeout 10;
    proxy_read_timeout 300;
    client_max_body_size 20M;
    client_body_buffer_size 1024k;
  }

        location /CROSS/RESTful {
            proxy_pass http://************:9990;

            proxy_set_header digi-key $http_digi_key;
            proxy_set_header digi-protocol $http_digi_protocol;
            proxy_set_header digi-type $http_digi_type;
            proxy_set_header digi-host $http_digi_host;
            proxy_set_header digi-service $http_digi_service;
            proxy_set_header digi-datakey $http_digi_datakey;
        }

        location /athena-designer {
            proxy_pass https://adp-paas.apps.digiwincloud.com.cn;
        }

        # location / {
        #  root /ms/lowcode-frontend/dist; # 指定静态文件的根目录
        #  try_files $uri $uri/ /index.html; # 确保单页应用（SPA）能够正确加载
        #  index index.html index.htm; # 默认的首页文件
        # }
}



server {
   listen 9200;

   # include /etc/nginx/conf.d/fenxiao/*.conf;
   
   location / {
    index index.html index.htm;
    proxy_pass http://frontend/;
    proxy_http_version 1.1;
    proxy_pass_header Server;
    proxy_set_header Host $http_host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto https;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Scheme $scheme;
    proxy_connect_timeout 10;
    proxy_read_timeout 300;
    client_max_body_size 20M;
    client_body_buffer_size 1024k;
  }


}

server {
   listen 9300;

   # include /etc/nginx/conf.d/fenxiao/*.conf;

   location /athena-designer {
    index index.html index.htm;
    proxy_pass http://adp_fx_backend/athena-designer;
    proxy_http_version 1.1;
    proxy_pass_header Server;
    proxy_set_header Host $http_host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto https;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Scheme $scheme;
    proxy_connect_timeout 10;
    proxy_read_timeout 300;
    client_max_body_size 200M;
    client_body_buffer_size 1024k;
  }

   location / {
    index index.html index.htm;
    proxy_pass http://adp_fx_frontend/;
    proxy_http_version 1.1;
    proxy_pass_header Server;
    proxy_set_header Host $http_host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto https;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Scheme $scheme;
    proxy_connect_timeout 10;
    proxy_read_timeout 300;
    client_max_body_size 20M;
    client_body_buffer_size 1024k;
  }

   location /athena-designer-editor {
    index index.html index.htm;
    proxy_pass http://adp_fx_frontend/athena-designer-editor;
    proxy_http_version 1.1;
    proxy_pass_header Server;
    proxy_set_header Host $http_host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto https;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Scheme $scheme;
    proxy_connect_timeout 10;
    proxy_read_timeout 300;
    client_max_body_size 20M;
    client_body_buffer_size 1024k;
  }

  location /athena-designer-core {
    index index.html index.htm;
    proxy_pass http://adp_fx_frontend/athena-designer-core;
    proxy_http_version 1.1;
    proxy_pass_header Server;
    proxy_set_header Host $http_host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto https;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Scheme $scheme;
    proxy_connect_timeout 10;
    proxy_read_timeout 300;
    client_max_body_size 20M;
    client_body_buffer_size 1024k;
  }

  location /athena-designer-editor-components {
    index index.html index.htm;
    proxy_pass http://adp_fx_frontend/athena-designer-editor-components;
    proxy_http_version 1.1;
    proxy_pass_header Server;
    proxy_set_header Host $http_host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto https;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Scheme $scheme;
    proxy_connect_timeout 10;
    proxy_read_timeout 300;
    client_max_body_size 20M;
    client_body_buffer_size 1024k;
  }

  location /athena-mechanism-core {
    index index.html index.htm;
    proxy_pass http://adp_fx_frontend/athena-mechanism-core;
    proxy_http_version 1.1;
    proxy_pass_header Server;
    proxy_set_header Host $http_host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto https;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Scheme $scheme;
    proxy_connect_timeout 10;
    proxy_read_timeout 300;
    client_max_body_size 20M;
    client_body_buffer_size 1024k;
  }

  location /scheduler {
    index index.html index.htm;
    proxy_pass http://adp_fx_frontend/scheduler;
    proxy_http_version 1.1;
    proxy_pass_header Server;
    proxy_set_header Host $http_host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto https;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Scheme $scheme;
    proxy_connect_timeout 10;
    proxy_read_timeout 300;
    client_max_body_size 20M;
    client_body_buffer_size 1024k;
  }



}

#server {
#    listen 9200; # 监听80端口
#    server_name *************; # 你的域名或IP地址
 
#    location / {
#        root /ms/lowcode-frontend/dist; # 指定静态文件的根目录
#        try_files $uri $uri/ /index.html; # 确保单页应用（SPA）能够正确加载
#        index index.html index.htm; # 默认的首页文件
#    }
 
    # 可以添加额外的location块来处理特定的URI或路径
    # location /api {
    #     proxy_pass http://backend-server; # 例如代理到后端API服务器
    # }
# }
