# 镜像仓库地址
REGISTRY="registry.digiwincloud.com.cn"
# 镜像名称
IMAGE="athena/athena-designer-web-distribution"
# 从参数获取tag
TAG="$1"

# 完整镜像地址
FULL_IMAGE="${REGISTRY}/${IMAGE}:${TAG}"

echo "开始拉取镜像: ${FULL_IMAGE}"

# 拉取镜像
docker pull "${FULL_IMAGE}"

# 检查拉取是否成功
if [ $? -eq 0 ]; then
    echo "镜像拉取成功: ${FULL_IMAGE}"
else
    echo "镜像拉取失败: ${FULL_IMAGE}"
    exit 1
fi

# 修改docker-pull.sh
cd /ms/deployment
sed -i  "31s?.*?remote_images[adp_fx_frontend]=${FULL_IMAGE}?" /ms/deployment/docker-pull.sh
sh startup.sh
