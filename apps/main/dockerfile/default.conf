server {
    listen       80;
    server_name  localhost;

    location / {
        expires -1;
        add_header 'Access-Control-Allow-Origin' $http_origin;
        add_header 'Access-Control-Allow-Credentials' 'true';
	    root  /usr/share/nginx/html;
        index  index.html index.htm;
        if ($request_filename ~ .*\.(html|json)$){
          add_header Cache-Control no-store;
        }
        try_files $uri $uri/ @rewrites;
    }

    # 新增API代理配置
    # location /athena-designer/ {
    #     proxy_pass @ADESIGNER_URL@;
        
    #     add_header 'Access-Control-Allow-Origin' $http_origin;
    #     add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
    #     add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
    #     add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
    #     add_header 'Access-Control-Allow-Credentials' 'true';
        
    #     if ($request_method = 'OPTIONS') {
    #         add_header 'Access-Control-Max-Age' 1728000;
    #         add_header 'Content-Type' 'text/plain; charset=utf-8';
    #         add_header 'Content-Length' 0;
    #         return 204;
    #     }
    # }

    location @rewrites {
        rewrite ^(?!.*\.js$).*$  /index.html last;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

   
}

