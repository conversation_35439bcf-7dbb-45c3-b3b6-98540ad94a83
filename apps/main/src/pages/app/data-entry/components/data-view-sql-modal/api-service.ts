import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { AppService } from 'pages/apps/app.service';
import { Observable } from 'rxjs';

@Injectable()
export class ApiService {
  adesignerUrl: string = '';

  constructor(private http: HttpClient, private configService: SystemConfigService, private appService: AppService) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
  }

  get application() {
    return this.appService?.selectedApp?.code;
  }

  /** 获取解决方案后端列表 */
  postTargetList(payload?: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/modelDriverTarget/servicecode/queryList`;
    return this.http.post(url, { application: this.application });
  }

  // 调用esp校验后端服务是否存在
  checkServiceValid(serviceCode: string): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/modelDriverTarget/serverSource/validateIsRegistry?serviceCode=${serviceCode}`;
    return this.http.get(url);
  }

  /**
   * SQL生产查询方案
   * @param param
   * @returns
   */
  addDataView(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/dataView/addDataView`;
    return this.http.post(url, param);
  }

  /**
   * 查询后端服务下的表
   */
  queryTableAndFields(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/dataView/queryTableAndFields`;
    return this.http.post(url, param);
  }
}
