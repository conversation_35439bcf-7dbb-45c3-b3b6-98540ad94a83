import { Component, Inject, OnInit, OnDestroy } from '@angular/core';
import microApp from '@micro-zoe/micro-app';
import { Subscription } from 'rxjs';
import { AdUserService } from 'pages/login/service/user.service';
import { AD_AUTH_TOKEN } from 'pages/login/service/auth.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { environment } from 'environments/environment';
import { LocaleService } from 'common/service/locale.service';
import { MicroAppLanguageSyncService } from 'common/service/microapp-language-sync.service';

@Component({
  selector: 'app-devOps-wrap',
  templateUrl: './devOps-wrap.component.html',
  styleUrls: ['./devOps-wrap.component.less'],
})
export class DevOpsWrapComponent implements OnInit {
  url = '';
  appName = 'devOps';
  packageName = 'athena-designer-core';
  microAppData = {
    token: this.userService.getUser('fxToken'),
  };

  routeSubscribe$: Subscription;

  constructor(
    private userService: AdUserService,
    private configService: SystemConfigService,
    private router: Router,
    @Inject(AD_AUTH_TOKEN) protected authToken: any,
  ) {
    this.configService.getConfig().subscribe((config: any): void => {
      this.url = config.devOpsUrl;
    });
  }

  ngOnInit(): void {
    this.routeSubscribe$ = this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe((data: any) => {
        window.history.pushState(history.state, '', data?.url);
        window.dispatchEvent(new PopStateEvent('popstate', { state: history.state }));
      });
  }

  ngOnDestroy(): void {
    microApp?.unmountApp('deployer');
    this.routeSubscribe$?.unsubscribe();
  }

  handleCreate(): void {
    console.log('react_deploy 创建了');
  }

  handleBeforeMount(): void {
    console.log('react_deploy 即将被渲染');
  }

  handleMount(): void {
    console.log('react_deploy 已经渲染完成');
  }

  handleUnmount(): void {
    console.log('react_deploy 卸载了');
  }

  handleError(): void {
    console.log('react_deploy 加载出错了');
  }

  handleDataChange(e: CustomEvent): void {
    const { type, data } = e.detail.data;
    console.log('type', type);
    if (type === 'routeLink') {
      window.history.pushState(history.state, '', data?.url);
      window.dispatchEvent(new PopStateEvent('popstate', { state: history.state }));
    }
    if (type === 'routeLinkBlank') {
      // const url = this.router.serializeUrl(this.router.createUrlTree([data?.url]));
      window.open(data?.url, '_blank');
    }
  }
}
