// 按钮功能 模板
export const editOperateSets = (): string[] => {
  return ['app-cancel-specify', 'app-save-specify'];
};

export const OperateSets = {
  query: {
    apiMdCreator: {
      // 二级面包屑
      2: ['app-export-specifications', 'app-template-download'],
      // 三级面包屑
      3: ['app-export-specifications', 'app-template-download'],
      // 四级面包屑
      4: [
        'app-sub-specifications',
        // 'app-applicable-scene',
        'app-edit-specify',
        'app-copy-specifications',
        'app-export-specifications',
        'app-template-download',
      ],
    },
    normal: {
      2: ['app-template-download'],
      // 三级面包屑
      3: ['app-template-download'],
      // 四级面包屑
      4: ['app-export-specifications', 'app-template-download'],
    },
  },
  design: {
    apiMdCreator: {
      // 设计者
      // 二级面包屑
      2: ['app-import-specifications', 'app-template-download'],
      // 三级面包屑
      3: {
        // 草稿态
        1: [
          'app-create-specify',
          'app-import-specifications',
          'app-delete-specify',
          'app-specify-submit',
          'app-template-download',
        ],
        // 审核中
        2: ['app-import-specifications', 'app-specify-back', 'app-template-download'],
        // 开发中
        3: ['app-import-specifications', 'app-specify-back', 'app-specify-fixed', 'app-template-download'],
        // 已完成
        4: [],
      },
      // 四级面包屑
      4: {
        // 草稿态
        1: [
          'app-import-specifications',
          // 'app-applicable-scene',
          'app-edit-specify',
          'app-copy-specifications',
          'app-delete-specify',
          'app-export-specifications',
          'app-specify-submit',
          'app-template-download',
        ],
        // 审核中
        2: [
          'app-import-specifications',
          'app-delete-specify',
          'app-export-specifications',
          'app-specify-back',
          'app-template-download',
        ],
        // 开发中
        3: [
          'app-import-specifications',
          'app-delete-specify',
          'app-export-specifications',
          'app-specify-back',
          'app-specify-fixed',
          'app-template-download',
        ],
        // 已完成
        4: [],
      },
    },
    apiMdApprover: {
      // 审核者
      2: [],
      3: {
        2: ['app-approve-back', 'app-approve-pass'],
      },
      4: {
        2: ['app-approve-back', 'app-approve-pass', 'app-specify-course'],
      },
    },
  },
};
