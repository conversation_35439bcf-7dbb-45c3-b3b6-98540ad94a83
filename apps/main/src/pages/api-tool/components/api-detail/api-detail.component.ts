import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  QueryList,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ApiToolService } from '../../api-tool.service';
import { BaseInfoEditTableComponent } from './base-info-edit-table/base-info-edit-table.component';
import { BaseInfoTableComponent } from './base-info-table/base-info-table.component';
import { PublishService } from 'common/service/event-publish.service';
import { OperateSets, editOperateSets } from '../operate-set/operate-set.modal';
import { filter } from 'rxjs/operators';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { LicenseManager } from 'ag-grid-enterprise';
import { isNone } from 'common/utils/core.utils';
import { AdUserService } from 'pages/login/service/user.service';
import { deepClone } from '../../../../../projects/form-editor-components/src/lib/components/app-custom-property/select-data-info/commonItem';
declare const window: any;

@Component({
  selector: 'app-api-detail',
  templateUrl: './api-detail.component.html',
  styleUrls: ['./api-detail.component.less'],
})
export class ApiDetailComponent implements OnInit, OnDestroy {
  @Input() activeRow: any;
  // @Input() categoryList: any;

  loading: boolean = false;

  operateSets: any[] = [];
  detailData: any; // 详情数据,可模拟
  baseDataForm: FormGroup;
  editType: 'add' | 'edit' | 'copy' | 'sub';
  DesclanguageVal: any;
  checkFalseVisible: boolean = false;
  checkResultLink: any;

  expandRequest: boolean = true;
  expandResSuccess: boolean = true;
  expandResFail: boolean = true;
  expandModelMain: boolean = true;
  expandModelStartSub: boolean = true;
  expandModelReceSub: boolean = true;

  currentVersion: string;
  menuType: 'query' | 'design';
  statusId: any; // 状态Id
  langDescription: string;
  apiData: any;

  showDetail: boolean = false;
  timeout: any;

  changeSubscription: Subscription;
  reviceSubscription: Subscription;
  editor: any;
  showTabs: any[] = [0];

  get hasParentApiName(): boolean {
    const readOnly = !isNone(this.activeRow?.parentApiName);
    this.apiToolService.readOnly = readOnly;
    return readOnly;
  }

  get readOnly(): boolean {
    return this.apiToolService.readOnly;
  }

  @ViewChildren('foldTable') foldTableList: QueryList<any>;
  @ViewChild('baseEditTable') refBaseEditTable: BaseInfoEditTableComponent;
  @ViewChild('editor') refEditor: any;
  @ViewChild('baseTable') refBaseTable: BaseInfoTableComponent;

  @Output() activeRowChange: EventEmitter<any> = new EventEmitter();

  constructor(
    private apiToolService: ApiToolService,
    private translateService: TranslateService,
    private messageService: NzMessageService,
    private eventService: PublishService,
    private router: Router,
    private fb: FormBuilder,
    protected adUserService: AdUserService,
  ) {
    this.baseDataForm = this.fb.group({
      parentApiName: [null],
      apiName: [null, [Validators.required]],
      apiDescription: [null, [Validators.required]],
      apiDescriptionMultilingual: [null, [Validators.required]],
      applicant: [null, [Validators.required]],
      tenantId: [''],
      apiVersion: [null, [Validators.required]],
      approvedStatus: [null, [Validators.required]],
    });
    this.changeSubscription = this.apiToolService.apiChangeSubject$.subscribe((res) => {
      this.currentVersion = res?.version;
      this.queryAPIDetail(true);
    });

    this.reviceSubscription = this.eventService.bus$
      .pipe(filter((event) => event.type.startsWith('api-detail')))
      .subscribe((event) => {
        if (event.type === 'api-detail-save') {
          this.handleSave();
        }
      });

    LicenseManager.setLicenseKey(
      'Using_this_AG_Grid_Enterprise_key_( AG-041185 )_in_excess_of_the_licence_granted_is_not_permitted___Please_report_misuse_to_( <EMAIL> )___For_help_with_changing_this_key_please_contact_( <EMAIL> )___( DATA SYSTEMS CONSULTING CO., LTD. )_is_granted_a_( Single Application )_Developer_License_for_the_application_( Athena )_only_for_( 1 )_Front-End_JavaScript_developer___All_Front-End_JavaScript_developers_working_on_( Athena )_need_to_be_licensed___( Athena )_has_been_granted_a_Deployment_License_Add-on_for_( 1 )_Production_Environment___This_key_works_with_AG_Grid_Enterprise_versions_released_before_( 7 June 2024 )____[v2]_MTcxNzcxNDgwMDAwMA==3777803d9de34bf42162a6105dff0111',
    );
  }

  async ngOnInit() {
    // 用户登陆后，某些场景不走ApiOverviewComponent组件，需要手动查询用户信息
    if (this.adUserService.isLogin && !this.apiToolService.UserName) {
      // await this.getTokenAnalyze();
      this.apiToolService.setUserName(this.adUserService.getUser("userName"))
    }
    this.menuType = location.pathname.includes('query') ? 'query' : 'design';
    const { editType, apiData } = this.apiToolService.BreadContents;
    this.apiData = apiData;
    if (editType) {
      // 新增，修改，复制
      this.editType = editType;
      this.statusId = this.apiToolService.BreadContents.class.id;
      this.currentVersion = null;
      if (editType === 'add') {
        this.activeRow = {
          name: '--',
          apiVersion: null,
          approvedStatus: this.apiToolService.BreadContents.class.status,
          categoryName: null,
        };
        this.detailData = {
          apiName: null,
          apiDescription: null,
          applicant: this.apiToolService.UserName,
          apiVersion: '1.0',
          approvedStatus: this.translateService.instant('dj-草稿'),
          paging: 1,
          invokeType: 'sync',
          isBatch: 'N',
          msgFormat: 'JSON',
          idempotency: 'N',
        };
      }

      if (editType === 'copy') {
        const { apiData } = this.apiToolService.BreadContents;
        this.activeRow = {
          name: apiData.apiName + '.copy',
          version: null,
          approvedStatus: apiData.approvedStatus,
          categoryName: apiData.categoryName,
        };
        this.detailData = {
          ...apiData,
          apiName: apiData.apiName + '.copy',
          approvedStatus: this.translateService.instant('dj-草稿'),
          applicant: this.apiToolService.UserName,
          tenantId: '',
          apiVersion: null,
        };
        if (!isNone(apiData?.parentApiName)) {
          this.activeRow = {
            ...this.activeRow,
            parentApiName: apiData.parentApiName,
            parentApiTenantId: apiData.parentApiTenantId,
          };
          this.detailData = {
            ...this.detailData,
            parentApiName: apiData.parentApiName,
            parentApiTenantId: apiData.parentApiTenantId,
          };
        }
      }

      if (editType === 'sub') {
        const { apiData } = this.apiToolService.BreadContents;
        this.activeRow = {
          parentApiName: apiData.apiName,
          parentApiTenantId: apiData.tenantId,
          name: apiData.apiName + '.sub',
          version: null,
          approvedStatus: apiData.approvedStatus,
          categoryName: apiData.categoryName,
        };
        this.detailData = {
          ...apiData,
          parentApiName: apiData.apiName,
          parentApiTenantId: apiData.tenantId,
          apiName: apiData.apiName + '.sub',
          approvedStatus: this.translateService.instant('dj-草稿'),
          applicant: this.apiToolService.UserName,
          tenantId: '',
          apiVersion: null,
        };
      }

      if (editType === 'edit') {
        const { apiData } = this.apiToolService.BreadContents;
        this.activeRow = {
          name: apiData.apiName,
          version: this.menuType === 'query' ? null : apiData.apiVersion,
          approvedStatus: apiData.approvedStatus,
          categoryName: apiData.categoryName,
        };
        if (apiData?.parentApiName) {
          this.activeRow['parentApiName'] = apiData.parentApiName;
          this.activeRow['parentApiTenantId'] = apiData.parentApiTenantId;
        }
        this.detailData = {
          ...apiData,
          apiName: apiData.apiName,
          approvedStatus: this.translateService.instant('dj-草稿'),
          applicant: this.apiToolService.UserName,
          tenantId: apiData?.tenantId,
          apiVersion: this.menuType === 'query' ? null : apiData.apiVersion,
        };
      }
      this.showDetail = true;
      this.operateSets = editOperateSets();
      this.baseDataForm.patchValue(this.detailData);
      setTimeout(() => {
        this.handleLoadApiVersion();
      }, 0);
    } else {
      this.statusId =
        this.menuType === 'design' ? this.apiToolService.BreadContents?.class?.id ?? this.apiData.statusId : '';
      this.currentVersion = this.apiData?.version ?? this.apiData?.apiVersion;
      this.activeRow = {
        name: this.apiData?.name ?? this.apiData?.apiName,
        version: this.currentVersion,
        approvedStatus: this.apiData?.approvedStatus,
        categoryName: this.apiData?.categoryName,
        tenantId: this.activeRow?.tenantId ?? '',
      };
      this.operateSets = this.getOperateSets();
      this.queryAPIDetail();
    }
  }

  // async getTokenAnalyze() {
  //   const res: any = await this.apiToolService.getTokenAnalyze();
  //   if (res.name) {
  //     this.apiToolService.setUserName(res.name);
  //   }
  // }

  getOperateSets() {
    if (this.menuType === 'query') {
      const role =
        this.apiToolService.UserRole && this.apiToolService.UserRole === 'apiMdCreator' ? 'apiMdCreator' : 'normal';
      return OperateSets.query[role]['4'];
    } else {
      return OperateSets.design[this.apiToolService.UserRole]['4'][this.statusId];
    }
  }

  hanldeExpandModelMain(): void {
    if (this.expandModelMain) {
      this.expandModelStartSub = false;
      this.expandModelReceSub = false;
      this.expandModelMain = false;
    } else {
      this.expandModelMain = true;
    }
  }

  // handleActiveItem(item): void {
  //   if (this.editType) {
  //     this.messageService.error(this.translateService.instant('dj-数据未保存，请先保存后再进行其他操作！'));
  //     return;
  //   }
  //   // this.categoryList = this.categoryList.map((one) => {
  //   //   one.active = one === item;
  //   //   return one;
  //   // });
  //   this.currentVersion = item.version;
  //   this.activeRow = item;
  //   this.activeRowChange.emit(item);
  //   if (item.name) {
  //     this.editType = null;
  //     this.queryAPIDetail();
  //     this.operateSets = this.getOperateSets();
  //   } else {
  //     // add 场景取缓存 不存在
  //   }
  // }

  async queryAPIDetail(afterRefresh?: boolean) {
    const payload = {
      apiName: this.activeRow.name,
      tenantId: this.activeRow?.tenantId ?? '',
      version: this.currentVersion,
      searchType: this.menuType === 'query' ? 'apiSearch' : 'apiDesign',
    };
    this.loading = true;
    this.showDetail = false;
    const res: any = await this.apiToolService.queryAPIDetail(payload);
    this.loading = false;
    this.timeout = setTimeout(() => {
      this.showDetail = true;
    }, 200);
    if (res?.code === 0) {
      if (res.data.code === '000') {
        const detailData = res.data;
        detailData.menuType = this.menuType;
        detailData.categoryName = this.activeRow?.categoryName;
        this.detailData = detailData;
        if (this.menuType === 'design') {
          this.langDescription =
            `${detailData?.apiDescriptionMultilingual?.zh_CN}(简体)/` +
            `${detailData?.apiDescriptionMultilingual?.zh_TW}(繁体)/` +
            `${detailData?.apiDescriptionMultilingual?.en_US}(English)`;
        }
        if (detailData?.parentApiName) {
          this.activeRow['parentApiName'] = detailData.parentApiName;
          this.baseDataForm.patchValue({
            parentApiName: detailData.parentApiName,
          });
        }
        if (afterRefresh) {
          this.apiToolService.setBreadContents({
            type: this.menuType,
            level: 4,
            apiData: this.detailData,
            module: 'ApiDetailComponent',
          });
        } else {
          this.apiToolService.updateBreadContents({
            type: this.menuType,
            level: 4,
            apiData: this.detailData,
          });
        }
      } else {
        this.langDescription = null;
        this.messageService.error(res.data?.description);
      }
    } else {
      this.langDescription = null;
    }
  }

  // 多语言基础赋值
  handleLangInfo(key: any, data: any): void {
    this.detailData[key] = data?.value;
    this.detailData[`${key}Multilingual`] = data.lang;
    const patchVal = {};
    patchVal[key] = data?.value;
    patchVal[`${key}Multilingual`] = data.lang;
    this.baseDataForm.patchValue(patchVal);
    this.DesclanguageVal = data.lang;
  }

  // 保存规格
  async handleSave() {
    let invalidFlag = false;
    let params: any = {};
    // 基础信息验证
    if (!this.baseDataForm.valid) {
      invalidFlag = true;
      Object.values(this.baseDataForm.controls).forEach((control) => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }

    // 其他基础信息验证
    const baseData = this.refBaseEditTable.validateOnSave();
    if (baseData.length === 0) {
      invalidFlag = true;
    } else {
      params = {
        ...baseData[0],
        ...this.baseDataForm.getRawValue(),
      };
    }

    // 处理其他tab未加载导致table数据未处理情况
    if (this.showTabs.length < 3) {
      this.showTabs = [0, 1, 2];
    }
    setTimeout(async () => {
      let requestMessageSpec = [];
      let responseMessageSuccessSpec = [];
      let responseMessageFailedSpec = [];
      // 数据结构验证
      this.foldTableList.forEach((one, index) => {
        const oneTableData = one.validateOnSave();
        const oneRowData = (() => {
          return (one.rowData ?? []).map((row) => {
            Reflect.deleteProperty(row, 'childrens');
            return row;
          });
        })();
        if (oneTableData.length === 0) {
          invalidFlag = true;
        } else {
          if (index === 0) {
            params.requestMessageSpec = oneTableData;
            requestMessageSpec = deepClone(oneRowData);
          }
          if (index === 1) {
            params.responseMessageSuccessSpec = oneTableData;
            responseMessageSuccessSpec = deepClone(oneRowData);
          }
          if (index === 2) {
            params.responseMessageFailedSpec = oneTableData;
            responseMessageFailedSpec = deepClone(oneRowData);
          }
        }
      });

      // 验证完结处理
      if (invalidFlag) {
        return;
      } else {
        // 发送接口
        params.statusId = 1;
        this.showDetail = false;
        this.loading = true;
        const res: any = await this.apiToolService.saveApiDetail(params);
        this.loading = false;
        this.timeout = setTimeout(() => {
          this.detailData = { ...params, requestMessageSpec, responseMessageSuccessSpec, responseMessageFailedSpec };
          this.showDetail = true;
        }, 200);
        if (res?.code === 0) {
          // 保存成功
          if (res.data.code === '101' || res.data.code === '999') {
            // 传参格式问题或后台逻辑问题
            this.messageService.error(this.translateService.instant('dj-保存失败！'));
          }
          if (res.data.code === '211') {
            this.messageService.error(res.data.description);
          }
          if (res.data.code === '303') {
            // 检查有问题 -- 异常清单
            if (res.data?.detailFileLink) {
              this.checkResultLink = res.data?.detailFileLink;
              this.checkFalseVisible = true;
            }
          }
          if (res.data.code === '000') {
            // 000
            // 去往api 设计四级面包屑，api管理-api设计-草稿-单个详情
            this.messageService.success(this.translateService.instant('dj-保存成功'));
            if (this.menuType === 'query') {
              this.apiToolService.setBreadContents({
                type: 'design',
                level: 4,
                class: { id: 1, status: this.translateService.instant('dj-草稿') },
                apiData: {
                  ...params,
                  name: params.apiName,
                  version: params.apiVersion,
                  statusId: 1,
                },
                module: 'ApiDetailComponent',
              });
              this.router.navigateByUrl(`/asset-center/api-tool/design`);
            } else {
              // 设计态成功后跳转
              this.editType = null;
              this.currentVersion = params.apiVersion;
              this.activeRowChange.emit({
                ...params,
                name: params.apiName,
                version: params.apiVersion,
                statusId: 1,
                refresh: true,
              });
            }
            this.operateSets = this.getOperateSets();
          }
        }
      }
    }, 100);
  }

  downloadImportWrong(): void {
    window.open(this.checkResultLink);
  }

  ngOnDestroy(): void {
    clearTimeout(this.timeout);
    this.changeSubscription.unsubscribe();
    this.reviceSubscription.unsubscribe();
  }

  // api 版本变更
  handleChangeVersion(e): void {
    this.apiToolService.apiChangeSubject$.next({ version: e });
  }

  handleLoadApiVersion(): void {
    // 编辑：this.menuType === 'design' && editType === 'edit'
    // 进版：this.menuType === 'query' && editType === 'edit'
    const { editType } = this.apiToolService.BreadContents;
    if (['add', 'copy', 'sub'].includes(editType) || (editType === 'edit' && this.menuType === 'query')) {
      const { apiName, tenantId } = this.baseDataForm.value;
      this.apiToolService.ApiVersion({ apiName: apiName ?? '', tenantId: tenantId ?? '' }).subscribe(
        (res) => {
          if (res.code === 0) {
            if (!isNone(res.data.version)) {
              this.baseDataForm.patchValue({
                apiVersion: res.data.version,
              });
            } else {
              this.messageService.error(res.data.description);
            }
          }
        },
        () => {},
      );
    }
  }

  handleChangeTab(tab): void {
    if (!this.showTabs.includes(tab.index)) {
      this.showTabs.push(tab.index);
    }
  }
}
