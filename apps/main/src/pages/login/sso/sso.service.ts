import { HttpClient } from '@angular/common/http';
import { Injectable, InjectionToken } from '@angular/core';
import { ParamMap } from '@angular/router';
import { SystemConfigService } from 'common/service/system-config.service';
import { Observable } from 'rxjs';
import { AdAuthService } from '../service/auth.service';
import { AdUserService } from '../service/user.service';
import { TenantService } from '../service/tenant.service';

export const SSO_LOGIN: InjectionToken<any> = new InjectionToken('SSO_LOGIN');
@Injectable()
export class SsoService {
  private adesignerUrl: string;
  private platformCategory: string;
  constructor(
    private tenantService: TenantService,
    protected http: HttpClient,
    private adAuthService: AdAuthService,
    private adUserService: AdUserService,
    private _configService: SystemConfigService,
  ) {
    this._configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
    this._configService.getConfig().subscribe((config: any) => {
      const { platformCategory } = config;
      this.platformCategory = platformCategory;
    });
  }
  /**
   * SSO Login.
   *
   * param {ParamMap} queryParam
   * returns {Observable<boolean>}
   */
  public ssoLogin(queryParam: ParamMap): Observable<boolean> {
    return new Observable((observer): void => {
      const userToken = queryParam.get('userToken') || '';
      if (!userToken) {
        observer.next(false);
        observer.complete();
      } else {
        // 調用 DwIamHttpClient 時, 會取出 DW_AUTH_TOKEN, 所以要先設定
        this.adAuthService.setAuthToken({ fxToken: userToken });
        // SSO刷新解决方案下的UserToken
        this.ssoAdLogin().subscribe(
          (res: any) => {
            if (res.code !== 0) return;
            const { data = {} } = res || {};
            const userData = {
              tenantId: String(data.tenantId),
              tenantSid: data.tenantSid,
              tenantName: data.tenantName,
              fxToken: data.fxToken,
              userId: data.userId || data.name,
              state: data.state,
              token: data.fxToken,
              userName: data.userName,
              name: data.name,
              experience: false,

              // currentBranch: data?.currentBranch || '',
            };
            const currTenantList = [
              {
                testTenant: true,
                name: data.name,
                defaultTenantId: String(data.tenantId),
                tenantName: data.tenantName,
                userName: data.userName,
                tenantSid: data.tenantSid,
                experience: false,
              },
            ];
            const currIndividualTenantList = [];
            // 必須依據正常的 after Login, 執行必要的設定, 因為有其他作業會觀察是否登入成功, 而進行對應的動作.
            this.adAuthService.setLogined(userData);

            // 發出 token 有效通知, 讓menu與權限可以取得資料, 讓 DwAuthGuardService 裡的 authorizedService.canActivate() 有資料來源.
            this.tenantService.setTokenValid(true);
            // 云端-取多租戶清單

            this.tenantService.setTenantList(currTenantList);
            this.adUserService.setUserInfo({
              tenantSid: data.tenantSid,
              experience: false,
            });
            // this.tenantService.getTenants().subscribe((currTenantList) => {
            //   const currTenant = (currTenantList ?? []).find((t) => t.tenantId === data.defaultTenantId);
            //   if (!!currTenant) {
            //     this.adUserService.setUserInfo({
            //       tenantSid: currTenant.tenantSid,
            //       experience: currTenant.experience,
            //     });
            //   }
            //   observer.next(true);
            //   observer.complete();
            // });
            // // 获取个案租户列表
            if (this.platformCategory !== 'TENANT') {
              // this.tenantService.getIndividualCaseTenants().subscribe(() => {});
              this.tenantService.setIndividualCaseTenantList(currIndividualTenantList);
            }
            observer.next(true);
            observer.complete();
          },
          () => {
            observer.next(false);
            observer.complete();
          },
        );
      }
    });
  }

  ssoAdLogin(): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/fxUser/getUserInfo`;
    return this.http.get(url, {});
  }
}
