<section *ngIf="platformCategory === 'SYSTEM'" class="solution-center">
  <!-- <h3 class="title">{{ 'dj-解决方案中心' | translate }}</h3> -->
  <!-- <p class="description">{{ 'dj-使用各类开发工具，开始快速构建解决方案' | translate }}</p> -->

  <ng-container>
    <h1 class="home-item-title">{{ 'dj-欢迎您OCSS 全渠道营销管理系统-开发工作台！' | translate | envTranslate }}</h1>
    <p class="home-item-description">
      {{ 'dj-您可以快速构建多种类型的解决方案，并直接在线发布使用' | translate }}
    </p>
    <p class="hot-app">
      {{ 'dj-可创建解决方案' | translate }}
    </p>
  </ng-container>

  <!-- <ul class="enter-box">
    <li
      *ngFor="let item of enterItems"
      [class]="{ 'active-category': activeCategory === item.category }"
      (mouseenter)="enterCategory(item)"
    >
      <img [src]="item.img" />
      <span>{{ item.title | translate }}</span>
    </li>
  </ul> -->

  <section class="create-types">
    <ng-container *ngIf="createItemsFilter?.length">
      <ng-container *ngFor="let item of createItemsFilter">
        <solution-entry-card
          *ngIf="item.auth === undefined || item.auth"
          [data]="item"
          [loading]="solutionCardLoading[item.appType]"
          (cardClick)="handleAddApp($event, item)"
          (detailClick)="handleLinkDetail($event)"
        ></solution-entry-card>
      </ng-container>
    </ng-container>
  </section>
</section>

<div class="home-container">
  <h3 class="title">{{ 'dj-已创建的解决方案' | translate }}</h3>
  <div class="top-box">
    <div class="top-left">
      <nz-input-group nzCompact [nzAddOnBefore]="addOnBeforeSelect" [nzSuffix]="suffixIcon">
        <input
          type="text"
          nz-input
          [(ngModel)]="searchValue"
          [maxlength]="50"
          [formControl]="searchControl"
          [placeholder]="'dj-请输入解决方案名称' | translate"
        />
      </nz-input-group>
      <ng-template #addOnBeforeSelect>
        <ad-select
          [(ngModel)]="searchAppType"
          [nzAllowClear]="false"
          (ngModelChange)="handleAppTypeSearch()"
          class="op-search-select"
          [disabled]="isTenantActive"
        >
          <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-数据驱动1.0' | translate" nzValue="1"></ad-option>
          <ad-option [nzLabel]="'dj-数据驱动2.0' | translate" nzValue="5"></ad-option>
          <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-随心控' | translate" nzValue="4"></ad-option>
          <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-敏捷问数1.0' | translate" nzValue="6"></ad-option>
          <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-敏捷问数2.0' | translate" nzValue="12"></ad-option>
          <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-数智AI特助' | translate" nzValue="7"></ad-option>
          <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-AI模型服务' | translate" nzValue="8"></ad-option>
          <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-高代码' | translate" nzValue="9"></ad-option>
          <!-- <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-数据可视化' | translate" nzValue="10"></ad-option> -->
          <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-业务领域中心' | translate" nzValue="11"></ad-option>
          <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-全部解决方案' | translate" nzValue=""></ad-option>
        </ad-select>
        <ad-select
          [disabled]="isTenantActive"
          [(ngModel)]="searchAppAuth"
          [nzAllowClear]="false"
          (ngModelChange)="handleAppAuthSearch()"
          class="op-search-select auth-search-select"
        >
          <ad-option [nzLabel]="'dj-可管理' | translate" nzValue="application:mgr"></ad-option>
          <ad-option [nzLabel]="'dj-可协作' | translate" nzValue="application:actor"></ad-option>
          <ad-option [nzLabel]="'dj-全部权限' | translate" nzValue=""></ad-option>
        </ad-select>
      </ng-template>
      <ng-template #suffixIcon>
        <span class="input-clear" *ngIf="!!searchValue">
          <i adIcon type="close-circle" theme="fill" (click)="handleClearSearch()"></i>
        </span>
        <i adIcon iconfont="iconinputserach" class="searchIcon iconfont" aria-hidden="true"> </i>
      </ng-template>
    </div>
    <div class="top-right">
      <nz-radio-group class="toggle-btn" [(ngModel)]="switchValue" nzButtonStyle="solid">
        <label nz-radio-button nzValue="card">
          <i class="icon" adIcon nzType="appstore" nzTheme="outline"></i>
        </label>
        <label nz-radio-button nzValue="list">
          <i class="icon" adIcon nzType="bars" nzTheme="outline"></i>
        </label>
      </nz-radio-group>
    </div>
  </div>
  <!-- <nz-spin  [nzSpinning]="appLoading"> -->
  <ad-empty class="adempty" *ngIf="!appList?.length" [nzNotFoundContent]="contentTpl">
    <ng-template #contentTpl>
      {{ 'dj-暂无解决方案' | translate }}
    </ng-template>
  </ad-empty>
  <div class="content-spin" [hidden]="switchValue !== 'card'">
    <div class="content-box" *ngIf="appList.length">
      <div class="app-card-container" *ngFor="let app of appList; let i = index" nz-col>
        <visibility-sensor class="visibility-wrap" (visibilityChange)="app.visible = $event">
          <app-solution-card
            *ngIf="app.visible"
            [data]="app"
            [showAuth]="true"
            [showMore]="isManage(app.roles)"
            [loading]="statusMap.get(app.objectId)"
            [moreContentTemplate]="moreTemplate"
            (handleClick)="handleAppClick(app)"
            (enterHighApp)="handleEnterHighApp(app)"
          >
            <ng-template #moreTemplate>
              <div class="app-card-operate" style="cursor: pointer">
                <div *ngIf="!isNana(app.appType)" (click)="handleSetApp($event, app)">
                  {{ 'dj-设置' | translate }}
                </div>
                <div
                  *ngIf="isManageDelete(app.roles) && app.tag?.sourceComponent !== 'BC'"
                  (click)="handleDeleteApp($event, app)"
                >
                  {{ 'dj-删除' | translate }}
                </div>
              </div>
            </ng-template>
          </app-solution-card>
        </visibility-sensor>
      </div>
    </div>
  </div>
  <!-- <app-solution-table
    *ngIf="appList.length > 0"
    [hidden]="switchValue !== 'list'"
    [data]="appList"
    [enableLoadMore]="true"
    [canLoadMore]="checkCanLoadMore"
    [queryPermission]="handleQueryPermission"
    [suppressColumnVirtualisation]="true"
    (loadMore)="handleLoadMore()"
    (handleAction)="handleGridAppAction($event)"
  >
  </app-solution-table> -->
  <div *ngIf="appList.length > 0" class="table-wrapper" [hidden]="switchValue !== 'list'">
    <app-solution-antd-table
      class="apps-table"
      [loading]="appsLoading"
      [data]="appList"
      [optionTemplate]="optionTemplate"
      [containerRef]="elementRef?.nativeElement"
      (loadMore)="handleLoadMore()"
      (appClick)="handleAppClick($event)"
    ></app-solution-antd-table>
    <ng-template #optionTemplate let-rowData>
      <i
        class="op-icon"
        adIcon
        iconfont="iconbianji1"
        nz-tooltip
        [nzTooltipTitle]="'dj-进入解决方案' | translate"
        (click)="handleAppClick(rowData)"
      ></i>
      <!-- <i
        adIcon
        class="op-icon"
        nz-tooltip
        [nzTooltipPlacement]="'topLeft'"
        [nzTooltipTitle]="'dj-发布' | translate"
        *ngIf="showHighCodeByAppTypes?.includes(rowData?.appType) && !hiddenMenuByEnv"
        [iconfont]="'icongaodaima1'"
        (click)="handleEnterHighApp(rowData)"
      ></i> -->
      <ng-container *ngIf="isManage(rowData.roles)">
        <i
          *ngIf="!isNana(rowData.appType)"
          class="op-icon"
          adIcon
          iconfont="iconshezhi123"
          nz-tooltip
          [nzTooltipTitle]="'dj-设置' | translate"
          (click)="handleSetApp(null, rowData)"
        ></i>
        <i
          *ngIf="isManageDelete(rowData.roles) && rowData.tag?.sourceComponent !== 'BC'"
          class="op-icon"
          adIcon
          iconfont="icondelete3"
          nz-tooltip
          [nzTooltipTitle]="'dj-删除' | translate"
          (click)="handleDeleteApp(null, rowData)"
        ></i>
      </ng-container>
    </ng-template>
  </div>

  <!-- </nz-spin> -->
</div>

<app-create-app
  *ngIf="addAppVisible"
  [visible]="addAppVisible"
  [type]="type"
  [params]="params"
  [opType]="editingApp ? 'edit' : undefined"
  [ignoreSuccessCallback]="editingApp ? true : undefined"
  [appCode]="editingApp?.code"
  (visibleChange)="onVisibleChange($event)"
  (afterCreated)="onAfterCreated($event)"
  (refreshAppList)="handleSearch()"
  (goApp)="handleGoApp($event)"
></app-create-app>

<app-create-agile-data-app
  *ngIf="addAgileDataAppVisible"
  [visible]="addAgileDataAppVisible"
  [type]="type"
  [params]="params"
  (visibleChange)="addAgileDataAppVisible = $event"
  (afterCreated)="onAfterCreated($event)"
></app-create-agile-data-app>

<app-create-nana-assistant-app
  *ngIf="addNanaAssistantAppVisible"
  [visible]="addNanaAssistantAppVisible"
  [type]="type"
  [params]="params"
  (visibleChange)="addNanaAssistantAppVisible = $event"
  (afterCreated)="onAfterCreated($event)"
></app-create-nana-assistant-app>

<app-info-edit
  *ngIf="appInfoVisible"
  [visible]="appInfoVisible"
  [formData]="editingApp"
  (callback)="handleAppEdit($event)"
></app-info-edit>

<app-delete-info
  [applyName]="appName"
  [applyNameArr]="applyNameArr"
  [appCode]="editingApp?.code"
  [appType]="editingApp?.appType"
  [isVisible]="deleteAppVisible"
  (ok)="handleAppOk($event)"
  (newOk)="handleDeleteNewOk($event)"
  (cancel)="handleDeleteCancel()"
>
</app-delete-info>

<!-- 新建解决方案弹窗 -->
<ad-modal
  nzClassName="switch-datasource-modal"
  nzWidth="470px"
  [(nzVisible)]="createSolutionVisible"
  [nzTitle]="'dj-新建解决方案' | translate"
  [nzClosable]="true"
  [nzMaskClosable]="false"
  [nzOkLoading]="createSolutionLoading"
  (nzOnCancel)="createSolutionVisible = false"
  (nzOnOk)="handleOk()"
>
  <ng-container *adModalContent>
    <solution-base-info-form #solutionInfoForm [solutionInfo]="currentSolutionCardData"></solution-base-info-form>
  </ng-container>
</ad-modal>
