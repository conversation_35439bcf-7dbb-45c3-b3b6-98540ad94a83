<ad-modal
  nzClassName="data-view-select-modal"
  [nzWidth]="'786px'"
  [nzTitle]="'dj-创建方式选择' | translate"
  [(nzVisible)]="modalVisible"
  [nzFooter]="null"
  [nzClosable]="true"
  [nzMaskClosable]="false"
  (nzOnCancel)="handleClose()"
>
  <ng-container *adModalContent>
    <div class="select-modal-content">
      <p class="select-title">{{ 'dj-请选择创建方式：' | translate }}</p>
      <div nz-row [nzGutter]="[12, 12]">
        <div nz-col class="gutter-row" [nzSpan]="12">
          <div class="type-select model" (click)="handleTypeSelect('model')">
            <p class="name">{{ 'dj-从模型创建查询方案' | translate }}</p>
            <p class="tips">{{ 'dj-适用场景：已有模型，基于模型创建' | translate }}</p>
            <p class="desc">
              <span>{{ 'dj-系统将根据当前业务对象下的模型生成查询方案' | translate }}</span>
            </p>
          </div>
        </div>
        <div nz-col class="gutter-row" [nzSpan]="12">
          <div class="type-select sql" (click)="handleTypeSelect('sql')">
            <p class="name">{{ 'dj-从SQL创建查询方案' | translate }}</p>
            <p class="tips">{{ 'dj-适用场景：当前使用数据库中已有实体表' | translate }}</p>
            <p class="desc">
              <span>{{ 'dj-需提供MySQL的select语句，查询方案通过解析SQL语句生成结构' | translate }}</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  </ng-container>
</ad-modal>
