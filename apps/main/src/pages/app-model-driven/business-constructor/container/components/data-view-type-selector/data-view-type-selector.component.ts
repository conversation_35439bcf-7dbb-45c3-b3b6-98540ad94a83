import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-data-view-type-selector',
  templateUrl: './data-view-type-selector.component.html',
  styleUrls: ['./data-view-type-selector.component.less'],
})
export class DataViewTypeSelectorComponent implements OnInit {
  @Input() modalVisible: boolean;

  @Output() modalClose: EventEmitter<void> = new EventEmitter();
  @Output() typeSelect: EventEmitter<'model' | 'sql'> = new EventEmitter();

  constructor() {}

  ngOnInit() {}

  public handleClose(): void {
    this.modalClose.emit();
  }

  /**
   * 选择类型
   * @param type
   */
  public handleTypeSelect(type: 'model' | 'sql') {
    this.typeSelect.emit(type);
  }
}
