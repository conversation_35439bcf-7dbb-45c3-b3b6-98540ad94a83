import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { forEach, size, trim } from 'lodash';
import { AppService } from '../../../../../apps/app.service';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AppModelDrivenService } from 'pages/app-model-driven/business-constructor/service/app-model-driven.service';
import { Router } from '@angular/router';
import { MdWebapiService } from 'pages/app-model-driven/service/md-webapi.service';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { checkResourcePerspective } from 'pages/app-model-driven/utils/utils';
import { IndividualService } from 'pages/individual/individual.service';

type JobModalData = {
  category: 'SIGN-DOCUMENT' | 'DOUBLE-DOCUMENT-FORM' | 'DOUBLE-DOCUMENT' | 'DOUBLE-DOCUMENT-MULTI';
  operateType: 'create' | 'edit';
  businessCode: string;
  code: string;
  menuItem: any;
};

@Component({
  selector: 'app-md-job-modal',
  templateUrl: './md-job-modal.component.html',
  styleUrls: ['./md-job-modal.component.less'],
})
export class MdJobModalComponent implements OnInit, OnChanges {
  @ViewChild('dataForm') dataForm;
  // 是否显示弹窗 addModal
  @Input() isShowModal = false;
  // 是否直接显示二级弹窗 addModalDetail
  @Input() isShowSubModal = false;
  // 额外新增前缀
  @Input() prefix = '';
  // 通知父组件关闭弹窗
  @Output() close: EventEmitter<any> = new EventEmitter();
  // 新增或编辑更新左侧菜单
  @Output() updateMenu: EventEmitter<any> = new EventEmitter();
  // category 分类 'SIGN-DOCUMENT' | 'DOUBLE-DOCUMENT-FORM' | 'DOUBLE-DOCUMENT' | 'DOUBLE-DOCUMENT-MULTI'
  // operateType 弹窗的操作类型 'create' | 'edit'
  // businessCode
  // code
  @Input() data: JobModalData = {
    category: 'SIGN-DOCUMENT',
    operateType: 'create',
    businessCode: '',
    code: '',
    menuItem: null,
  };

  get hasQueryPlan() {
    if (
      !!this.data?.menuItem?.businessDirTree?.find((item) => item.type === 'dataView')?.businessDirTree?.length ||
      checkResourcePerspective(this.data?.menuItem?.addSourceType)
    ) {
      return true;
    }
    const object = this.appModelDrivenService.businessObjectMenu?.find(
      (e) => e.businessCode === this.data.businessCode,
    );
    if (object && object.businessDirTree?.some((e) => e.type === 'dataView')) return true;
    return false;
  }

  // 弹窗的标题
  modalTitle: string;
  // 弹窗的描述
  modalTitleDescription: string;
  // 是否显示loading  addLoading
  isLoading = false;
  categroyTitleDescription = {
    'SIGN-DOCUMENT-MODEL_DRIVEN': 'dj-模型单档多栏',
    'SINGLE-DOCUMENT-MODEL_DRIVEN': 'dj-模型单档多栏',
    'DOUBLE-DOCUMENT-FORM-MODEL_DRIVEN': 'dj-模型单档',
    'DOUBLE-DOCUMENT-MODEL_DRIVEN': 'dj-模型双档',
    'DOUBLE-DOCUMENT-MULTI-MODEL_DRIVEN': 'dj-模型多档',
  };
  // 在编辑时给的回显数据 根据code查询后赋值
  formData: any;
  // 控制子组件的显示时机
  isShowDesignDetailForm = false;

  // 是否公共解决方案
  get isCommonApp() {
    return this.appService.isCommonApp;
  }
  isShowTypeModal = false;
  isShowDetailModal = false;
  detailType: string;

  type: string;

  confirmCoverVisible: boolean = false;
  isGeneratePageDesignByQueryPlanLoading: boolean = false;
  language: string;

  /**
   * 是不是资源视角
   */
  get isResourePerspective() {
    return checkResourcePerspective(this.data?.menuItem?.addSourceType);
  }

  constructor(
    public appService: AppService,
    private message: NzMessageService,
    private translateService: TranslateService,
    private appModelDrivenService: AppModelDrivenService,
    private mdWebapiService: MdWebapiService,
    private router: Router,
    private athModalService: AdModalService,
    private individualService: IndividualService,
  ) {}
  ngOnChanges(changes: SimpleChanges): void {
    const { isShowModal, isShowSubModal } = changes;

    if (isShowModal && !isShowSubModal) {
      this.isShowTypeModal = this.isShowModal && !this.isShowSubModal;
    }
    if (isShowSubModal) {
      this.isShowTypeModal = this.isShowModal && !this.isShowSubModal;
      this.isShowDetailModal = this.isShowSubModal;
    }
  }

  ngOnInit(): void {
    const { operateType } = this.data;
    if ('create' === operateType) {
      // 新增
      this.handleAddModal();
      this.isShowDesignDetailForm = true;
    } else {
      // TODO 配合后端改造查询逻辑，当前仅有作业，如果分销后续有持续的开发需求，建议不要简单的做增减，还是需要设计统一的结构和交互
      this.mdWebapiService
        .queryBasicPageDesign({
          code: this.data.code,
          application: this.appService?.selectedApp?.code,
        })
        .subscribe((res) => {
          if (res.code === 0) {
            this.formData = res.data;
            this.formData.simpleModelCodeAndServiceCode =
              this.formData?.simpleModelCode + this.formData?.simpleModelServiceCode;
            // 拿到数据后更新 category，再生成标题
            this.data.category = this.formData.category;
            this.handleAddDetailModal({ type: 'api', action: 'edit' });
            this.isShowDesignDetailForm = true;
          }
        });

      // 以下历史逻辑先保留
      // 编辑
      // 1、根据code来查数据
      // this.mdWebapiService
      //   .getActivityByCode({
      //     code: this.data.code,
      //     application: this.appService?.selectedApp?.code,
      //   })
      //   .subscribe((res) => {
      //     if (res.code === 0) {
      //       this.formData = res.data;
      //       this.formData.simpleModelCodeAndServiceCode =
      //         this.formData?.simpleModelCode + this.formData?.simpleModelServiceCode;
      //       // 拿到数据后更新 category，再生成标题
      //       this.data.category = this.formData.category;
      //       this.handleAddDetailModal({ type: this.formData.workType, action: 'edit' });
      //       this.isShowDesignDetailForm = true;
      //     }
      //   });
    }
  }
  // 添加 category:'SIGN-DOCUMENT' | 'DOUBLE-DOCUMENT-FORM' | 'DOUBLE-DOCUMENT' | 'DOUBLE-DOCUMENT-MULTI'
  // type 在这里是固定的'MODEL_DRIVEN'
  handleAddModal(): void {
    if ('create' === this.data.operateType) {
      this.modalTitle = this.translateService.instant('dj-新增数据录入作业');
    } else {
      this.modalTitle = this.translateService.instant('dj-编辑数据录入作业');
    }
    const attr = `${this.data.category}-MODEL_DRIVEN`;
    this.modalTitleDescription = this.categroyTitleDescription.hasOwnProperty(attr)
      ? this.translateService.instant(this.categroyTitleDescription[attr])
      : '';
  }
  // 添加-二级窗口
  async handleAddDetailModal(data: any): Promise<void> {
    const { type, action } = data ?? {};
    this.type = undefined;
    if (action === 'add' || action === 'edit') {
      this.type = type;
      // 打开二级窗口
      this.detailType = type;
      // this.close.emit();
      this.isShowTypeModal = false;
      setTimeout(() => {
        this.isShowDetailModal = true;
      }, 100);
    } else if (action === 'addCancle') {
      this.handleAddDetailModalAddCancle();
    } else if (action === 'generatePageDesignByQueryPlan') {
      if (checkResourcePerspective(this.data?.menuItem?.addSourceType)) {
        await this.createPageDesignWithResource();
      } else {
        await this.createPageDesignWithBusiness();
      }
    }
  }

  /**
   * 资源视角下创建作业
   */
  private async createPageDesignWithResource(): Promise<void> {
    this.modalTitle = this.translateService.instant('dj-新增数据录入作业');
    const attr = `${this.data.category}-MODEL_DRIVEN`;
    this.modalTitleDescription = this.categroyTitleDescription.hasOwnProperty(attr)
      ? this.translateService.instant(this.categroyTitleDescription[attr])
      : '';
    // 打开二级窗口
    this.detailType = 'MODEL_DRIVEN';
    this.isShowDesignDetailForm = true;
    this.isShowDetailModal = true;
  }

  /**
   * 对象视角下直接自动创建作业
   * @returns
   */
  private async createPageDesignWithBusiness(): Promise<void> {
    try {
      this.isGeneratePageDesignByQueryPlanLoading = true;
      // 查询方案生成作业接口
      const params = {
        businessCode: this.data.businessCode,
        type: 'PageDesgin',
      };
      const hasGenerate = await this.mdWebapiService.checkIsAutoGenerateBefore(params).toPromise();
      this.isGeneratePageDesignByQueryPlanLoading = false;
      if (!!hasGenerate?.data) {
        this.confirmCoverVisible = true;
        return;
      }
      this.generatePageDesignByQueryPlan();
    } catch (error) {
      console.error(error);
      this.isGeneratePageDesignByQueryPlanLoading = false;
    }
  }

  handleAddDetailModalAddCancle() {
    // 关闭新增一级窗口
    this.isShowTypeModal = false;
    this.isShowDetailModal = false;
    this.detailType = null;
    this.close.emit();
  }

  generatePageDesignByQueryPlan(operateType = 'first') {
    const data = {
      generateType: this.data.category,
      serviceCode: this.data.menuItem.serviceCode,
      modelId: this.data.menuItem.modelId,
      operateType: operateType,
      businessCode: this.data.businessCode,
    };
    this.isGeneratePageDesignByQueryPlanLoading = true;

    this.mdWebapiService.generatePageDesignByQueryPlan(data).subscribe(
      (res) => {
        if (res.code === 0) {
          this.message.success(this.translateService.instant('dj-创建成功'));
          this.isGeneratePageDesignByQueryPlanLoading = false;
          this.appModelDrivenService.updateBusinessObjectMenuRefresh({
            businessCode: this.data.businessCode,
            menuType: 'pageDesign',
            operatorType: 'add',
            navigateUrl: res.data?.dataViewPath,
          });

          this.handleAddDetailModalAddCancle();
        }
      },
      () => {
        this.isGeneratePageDesignByQueryPlanLoading = false;
      },
    );
  }

  // 弹窗出来后针对dataForm一些小改动
  handleAddModalOpen() {
    if ('create' === this.data.operateType) {
      const { dataForm } = this.dataForm;
      dataForm.get('category').disable();
      dataForm.patchValue({ category: this.data.category });
      // 新增时code可以编辑 ，编辑时需要禁止掉
      if ('create' === this.data.operateType) {
        ['code'].forEach((s) => {
          if (dataForm.get(s).disabled) {
            dataForm.get(s).enable();
          }
        });
      }
    }
  }

  // 关闭添加窗（取消）
  handleCloseAdd(): void {
    const { dataForm } = this.dataForm;
    dataForm.reset();
    dataForm.patchValue({ pattern: 'DATA_ENTRY' });
    this.isShowDetailModal = false;
    this.isShowTypeModal = false;
    this.close.emit();
  }

  /**
   * 模型创建作业
   * @param param
   */
  private handleCreatePageDesign(param: any, formLang: any): void {
    this.mdWebapiService
      .generatePageDesignByQueryPlan({
        serviceCode: param.productCode,
        generateType: this.data.category,
        operateType: 'first',
        addSourceType: this.data?.menuItem?.addSourceType,
        dataViewCode: param.simpleModelCode,
        jobName: param.name,
        jobCode: param.code,
        lang: {
          jobName: formLang?.name,
        },
      })
      .subscribe(
        (res) => {
          if (res.code === 0) {
            this.message.success(this.translateService.instant('dj-创建成功'));
            this.isGeneratePageDesignByQueryPlanLoading = false;
            this.appModelDrivenService.updateBusinessObjectMenuRefresh({
              businessCode: this.data.businessCode,
              menuType: 'pageDesign',
              operatorType: 'add',
              navigateUrl: res.data?.dataViewPath,
            });

            this.handleAddDetailModalAddCancle();
          }
        },
        () => {
          this.isGeneratePageDesignByQueryPlanLoading = false;
        },
      );
  }

  // 添加或编辑基础资料数据点击的保存 modalFlag:'add' | 'edit'
  // 注意create =>add 的转换
  handleAddBasic(modalFlag = 'add'): void {
    const appCode = this.appService?.selectedApp?.code;
    const dataForm = this.dataForm.dataForm;
    const formLang = this.dataForm.formLang;
    const form = this.dataForm;

    for (const i of Object.keys(dataForm.controls)) {
      dataForm.controls[i].markAsDirty();
      dataForm.controls[i].updateValueAndValidity({ onlySelf: modalFlag === 'add' });
    }
    if (this.isResourePerspective && this.type === undefined) {
      this.handleCreatePageDesign(dataForm.getRawValue(), formLang);
      return;
    }

    const packages = form.customPackageData;
    if (dataForm.valid) {
      // 定制包的额外校验
      if (this.isCommonApp) {
        // 公共解决方案有packages,需要额外校验
        const errorMessages = this.beforeSaveValidate(packages);
        if (size(errorMessages) > 0) {
          // 遍历提示信息
          forEach(errorMessages, (msg) => {
            this.message.error(msg);
          });
          return;
        }
        forEach(packages, (packageItem) => {
          packageItem.nameSpace = appCode;
        });
      }
      if (modalFlag === 'add') {
        this.isLoading = true;
      }
      const activityDto = dataForm?.getRawValue();
      activityDto['lang'] = form.formLang;
      const param = {
        ...activityDto,
        authorityPrefix: activityDto['authorityPrefix']
          ? this.isCommonApp
            ? `${
                this.individualService?.individualCaseApp
                  ? this.individualService.sourceApplicationCode
                  : this.appService?.selectedApp?.code
              }:${modalFlag === 'add' ? 'DataEntry_' : ''}${activityDto.code}`
            : `${
                this.individualService?.individualCaseApp
                  ? this.individualService.sourceApplicationCode
                  : this.appService?.selectedApp?.code
              }:basicDataEntry`
          : '',
        // version: '1.0',  // 版本号
        application: this.appService?.selectedApp?.code,
        applicationName: this.appService?.selectedApp?.name,
        businessCode: this.data.businessCode,
        packages: packages || [],
        tenantId: 'SYSTEM',
        addSourceType: this.data?.menuItem?.addSourceType,
      };
      if (modalFlag === 'add' && param.workType !== 'api') {
        // 新增模式下把关联模型的id-用户输入的code传给后端
        param.code = `${param.simpleModelCode}__${param.code}`;
      }
      if (param.hasOwnProperty('simpleModelCodeAndServiceCodeLabel')) {
        delete param.simpleModelCodeAndServiceCodeLabel;
      }
      if (this.data.menuItem?.addSourceType) param.addSourceType = this.data.menuItem?.addSourceType;

      // TODO 当前分销仅有作业，且需要在很短的时间内接入，所以本期仅需考虑接入新接口，其他逻辑不变，但之后 一定需要 充分评估和设计，处理完整的逻辑
      // 按当前分销的逻辑，此场景仅有编辑会触发新接口，其他场景保持现状
      if (modalFlag === 'edit') {
        this.mdWebapiService
          .savePageDesignBasicInfo({
            code: param.code,
            name: param.name,
            application: param.application,
            lang: param.lang,
          })
          .subscribe(
            (res) => {
              const { name, code, authorityPrefix, dependOnGroundEnd, category, pattern } = param;
              if (res.code === 0) {
                this.isLoading = false;
                // 时间有限，暂时不改变之前的逻辑，以免产生未知风险
                dataForm.patchValue({ pattern: 'DATA_ENTRY' });
                this.message.success(this.translateService.instant('dj-保存成功！'));

                const updateMenuData = {
                  businessCode: this.data.businessCode,
                  menuType: 'pageDesign',
                  operatorType: this.data.operateType,
                  editId: code,
                };
                this.updateMenu.emit(updateMenuData);
                this.close.emit();
              }
            },
            () => {
              this.isLoading = false;
            },
          );
        return;
      }

      this.mdWebapiService.addBasicReport(param, modalFlag).subscribe(
        (res) => {
          const { name, code, authorityPrefix, dependOnGroundEnd, category, pattern } = param;
          if (res.code === 0) {
            this.isLoading = false;
            dataForm.patchValue({ pattern: 'DATA_ENTRY' });
            this.message.success(this.translateService.instant('dj-保存成功！'));
            // this.handleLoadBasic();
            if (modalFlag === 'add') {
              // 模型驱动 新增后可能需要自动打开界面设计
              this.handleInterfaceDesign({
                ...param,
                code: `DataEntry_${code}`,
              });
            }
            let updateMenuData;
            if (this.data.operateType === 'create') {
              updateMenuData = {
                businessCode: this.data.businessCode,
                menuType: 'pageDesign',
                operatorType: 'add',
                navigateUrl: res.data.businessAddResponse.pageDesignPath,
              };
            } else {
              updateMenuData = {
                businessCode: this.data.businessCode,
                menuType: 'pageDesign',
                operatorType: this.data.operateType,
                editId: code,
              };
            }
            this.updateMenu.emit(updateMenuData);
            this.close.emit();
          }
        },
        () => {
          this.isLoading = false;
        },
      );
    }
  }

  // 保存前进行校验
  beforeSaveValidate(packages): any[] {
    const errorMessages = [];
    // 1、校验必填

    forEach(packages, (valueItem, index) => {
      valueItem.code = trim(valueItem.code);
      valueItem.name = trim(valueItem.name);
      if ('' === valueItem.code) {
        errorMessages.push(
          this.translateService.instant('dj-第几行', { n: index + 1 }) +
            this.translateService.instant('dj-插件名称') +
            this.translateService.instant('dj-不可为空'),
        );
      }
      if (valueItem.code && valueItem.code.length > 128) {
        errorMessages.push(
          this.translateService.instant('dj-第几行', { n: index + 1 }) +
            this.translateService.instant('dj-插件名称128字符'),
        );
      }
      if ('' === valueItem.name) {
        errorMessages.push(
          this.translateService.instant('dj-第几行', { n: index + 1 }) +
            this.translateService.instant('dj-插件描述') +
            this.translateService.instant('dj-不可为空'),
        );
      }
      if (valueItem.name && valueItem.name.length > 128) {
        errorMessages.push(
          this.translateService.instant('dj-第几行', { n: index + 1 }) +
            this.translateService.instant('dj-插件描述128字符'),
        );
      }
    });
    // 2、校验枚举code唯一
    // key：code valur:第一次放入的行号
    const codeMap = {};
    forEach(packages, (valueItem, index) => {
      const valueCode = valueItem.code;
      if (valueCode && codeMap[valueCode]) {
        errorMessages.push(
          this.translateService.instant('dj-第几行', { n: codeMap[valueCode] }) +
            this.translateService.instant('dj-第几行', { n: index + 1 }) +
            this.translateService.instant('dj-插件名称') +
            this.translateService.instant('dj-重复'),
        );
      } else {
        codeMap[valueCode] = index + 1;
      }
    });
    return errorMessages;
  }

  /**
   * 点击界面设计
   */
  handleInterfaceDesign(basic) {
    const { code, category, name, lang, authorityPrefix, dependOnGroundEnd, pattern, sourceType } = basic || {};
    // category 4种类型
    // sourceType 'MODEL_DRIVEN'
    // this.handleDataEntryDrawerOpen(category, sourceType, true, code);
  }
  // 打开模型设计页面 ，预计是可以暴露个事件
  handleGotoDesign($event) {
    const menuData = this.appModelDrivenService.businessObjectMenu || [];
    const menuItem = menuData.find((item) => item.businessCode === $event.relateBusinessCode);
    const modelDesign = menuItem.businessDirTree?.find((item) => item.type === 'modelDesign');
    const path = modelDesign.businessDirTree?.find(
      (item) => item.type === 'model' && item.businessSubCode === $event.code,
    )?.path;
    if (!path) return;
    this.close.emit();
    this.isShowTypeModal = false;
    this.router.navigate([`/app/business-constructor/${path}`], {
      queryParams: {
        appCode: this.appService?.selectedApp?.code,
      },
    });
  }

  handleCancel() {
    this.confirmCoverVisible = false;
  }
  handleReGenerate() {
    this.confirmCoverVisible = false;
    this.generatePageDesignByQueryPlan('append');
  }
  handleCover() {
    this.confirmCoverVisible = false;
    this.generatePageDesignByQueryPlan('coverage');
  }
}
