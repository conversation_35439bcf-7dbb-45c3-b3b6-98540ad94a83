::ng-deep .data-view-select-modal {
  max-width: unset;
  .select-modal-content {
    .select-title {
      margin-bottom: 14px !important;
    }

    .type-select {
      height: 360px;
      width: 360px;
      cursor: pointer;
      background-repeat: no-repeat;
      background-size: 360px 360px;
      position: relative;
      box-sizing: content-box;
      margin-bottom: 14px;

      outline: 1px solid #ccd2ff;
      border-radius: 15px;
      &:hover {
        outline: 2px solid #6a4cff;
      }
      &.model {
        background-image: url('/assets/img/data-view-model.png');
      }
      &.sql {
        background-image: url('/assets/img/data-view-sql.png');
      }

      .name {
        color: #4270ad;
        font-size: 18px;
        font-weight: 700;
        left: 24px;
        right: 24px;
        top: 24px;
        position: absolute;
      }

      .tips {
        height: 28px;
        line-height: 28px;
        background-color: #ffffff;
        color: #4270ad;
        font-size: 12px;
        position: absolute;
        left: 24px;
        right: 24px;
        top: 55px;
        padding-left: 8px;
        border-radius: 4px;
      }

      .desc {
        position: absolute;
        left: 24px;
        right: 24px;
        top: 90px;
        font-size: 13px;
        font-weight: 400;
        color: #4270ad;
        & span {
          display: block;
          margin-bottom: 4px;
        }
      }
    }
  }
}
