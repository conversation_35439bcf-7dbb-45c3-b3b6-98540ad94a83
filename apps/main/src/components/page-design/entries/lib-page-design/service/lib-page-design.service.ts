import { Injectable } from '@angular/core';
import { DslData } from '../config/lib-page-design.type';
import { dslMock } from './mock';

@Injectable()
export class LibPageDesignService {
  private _dslData: any; // 配置项
  get dslData() {
    return this._dslData;
  }

  setDslData(data: DslData): void {
    this._dslData = data;
  }

  private _loading: boolean = false; // loading
  get loading(): boolean {
    return this._loading;
  }

  constructor() {
    this.setDslData({
      code: "code001", // DSL唯一键
      version: "2.0", // DSL版本
      dsl: dslMock
    })
  }

  setLoading(data: any): void {
    this._loading = data;
  }
}
