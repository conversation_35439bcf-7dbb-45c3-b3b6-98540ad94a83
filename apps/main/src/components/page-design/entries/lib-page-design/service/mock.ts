const data = [
  {
    "title": "1234521",
    "lang": {
      "title": {
        "zh_CN": "1234521",
        "zh_TW": "1234521",
        "en_US": "1234521"
      }
    },
    "children": [
      {
        "title": "浏览界面",
        "lang": {
          "title": {
            "zh_CN": "浏览界面",
            "zh_TW": "浏览界面",
            "en_US": "Browse Page"
          }
        },
        "value": "DSL_1c60948210002391"
      }
    ]
  },
  {
    "title": "uuyuiy22",
    "lang": {
      "title": {
        "zh_CN": "uuyuiy22",
        "zh_TW": "uuyuiy22",
        "en_US": "uuyuiy22"
      }
    },
    "children": [
      {
        "title": "浏览界面",
        "lang": {
          "title": {
            "zh_CN": "浏览界面",
            "zh_TW": "浏览界面",
            "en_US": "Browse Page"
          }
        },
        "value": "DSL_9db8774210000484"
      }
    ]
  },
  {
    "title": "2323",
    "lang": {
      "title": {
        "zh_CN": "2323",
        "zh_TW": "2323",
        "en_US": "2323"
      }
    },
    "children": [
      {
        "title": "界面设计",
        "lang": {
          "title": {
            "zh_CN": "界面设计",
            "zh_TW": "界面设计",
            "en_US": "Page Design"
          }
        },
        "value": "DSL_fac16b4210002322"
      }
    ]
  },
  {
    "title": "312",
    "lang": {
      "title": {
        "zh_CN": "312",
        "zh_TW": "312",
        "en_US": "312"
      }
    },
    "children": [
      {
        "title": "编辑界面",
        "lang": {
          "title": {
            "zh_CN": "编辑界面",
            "zh_TW": "编辑界面",
            "en_US": "Edit Page"
          }
        },
        "value": "DSL_fad3518210000161"
      },
      {
        "title": "浏览界面",
        "lang": {
          "title": {
            "zh_CN": "浏览界面",
            "zh_TW": "浏览界面",
            "en_US": "Browse Page"
          }
        },
        "value": "DSL_fad34e0210002081"
      }
    ]
  }
];
export const dslMock = {
  layout: [], // 页面代码
  // operations: [],  // 表格操作, 开窗
  hooks: [], // hooks
  rules: [],
  // gridSettings: [] // 表格高级查询
  variables: [
    {
      name: 'sys_page_list',
      defaultValue: [...data],
      scope: 'system',
    },
  ], // 用户变量/系统变量
  dataConnectors: [
    {
      name: 'getName',
      connectType: 'api',
      runOnPageLoad: false,
      option: {
        request: {
          method: 'GET',
          path: '/get/name/info',
          params: [],
          body: {
            type: 'json',
            content: '',
          },
          headers: [],
        },
        preProcess: {
          type: 'javascript',
          script: '',
        },
        postProcess: {
          type: 'javascript',
          script: '',
        },
        response: {
          type: 'Standard',
          meta: {
            name: 'name',
            dataType: 'object',
            children: [
              {
                id: 'db5af325-e0fe-4db8-adec-d72c5543445b',
                name: 'code',
                dataType: 'string',
                description: '',
                isDataBody: false,
              },
              {
                id: 'e022d776-01e8-4bc2-b443-28bd90139a44',
                name: 'getName',
                dataType: 'object',
                description: '',
                children: [
                  {
                    id: '4745a6d5-f999-4dda-9c01-bbbd82fa0725',
                    name: 'name',
                    dataType: 'string',
                    description: '这是name',
                    isDataBody: false,
                  },
                  {
                    id: 'e88a91c1-8529-451c-949a-a14616f8e758',
                    name: 'ids',
                    dataType: 'string',
                    description: '这是ids',
                    isDataBody: false,
                  },
                  {
                    id: '0dd397c3-9465-495e-a9df-2c2bb7eed870',
                    name: 'dec',
                    dataType: 'string',
                    description: '这是desc',
                  },
                ],
                isDataBody: true,
              },
            ],
            id: 'ca89075f-177e-4f36-a0d7-8fb29705e171',
          },
        },
        responseTree: {
          id: 'e022d776-01e8-4bc2-b443-28bd90139a44',
          name: 'getName',
          dataType: 'object',
          description: {
            en_US: '',
            zh_CN: '',
            zh_TW: '',
          },
          children: [
            {
              id: '4745a6d5-f999-4dda-9c01-bbbd82fa0725',
              name: 'name',
              dataType: 'string',
              description: {
                en_US: '',
                zh_CN: '这是name',
                zh_TW: '',
              },
              isDataBody: false,
              data_name: 'name',
              data_type: 'string',
              fullPath: 'getName.name',
              is_array: false,
              field: null,
              children: null,
              desc: '这是name',
            },
            {
              id: 'e88a91c1-8529-451c-949a-a14616f8e758',
              name: 'ids',
              dataType: 'string',
              description: {
                en_US: '',
                zh_CN: '这是ids',
                zh_TW: '',
              },
              isDataBody: false,
              data_name: 'ids',
              data_type: 'string',
              fullPath: 'getName.ids',
              is_array: false,
              field: null,
              children: null,
              desc: '这是ids',
            },
            {
              id: '0dd397c3-9465-495e-a9df-2c2bb7eed870',
              name: 'dec',
              dataType: 'string',
              description: {
                en_US: '',
                zh_CN: '这是desc',
                zh_TW: '',
              },
              data_name: 'dec',
              data_type: 'string',
              fullPath: 'getName.dec',
              is_array: false,
              field: null,
              children: null,
              desc: '这是desc',
            },
          ],
          isDataBody: true,
          data_name: 'getName',
          data_type: 'object',
          fullPath: 'getName',
          is_array: false,
          field: [
            {
              id: '4745a6d5-f999-4dda-9c01-bbbd82fa0725',
              name: 'name',
              dataType: 'string',
              description: {
                en_US: '',
                zh_CN: '这是name',
                zh_TW: '',
              },
              isDataBody: false,
              data_name: 'name',
              data_type: 'string',
              fullPath: 'getName.name',
              is_array: false,
              field: null,
              desc: '这是name',
            },
            {
              id: 'e88a91c1-8529-451c-949a-a14616f8e758',
              name: 'ids',
              dataType: 'string',
              description: {
                en_US: '',
                zh_CN: '这是ids',
                zh_TW: '',
              },
              isDataBody: false,
              data_name: 'ids',
              data_type: 'string',
              fullPath: 'getName.ids',
              is_array: false,
              field: null,
              desc: '这是ids',
            },
            {
              id: '0dd397c3-9465-495e-a9df-2c2bb7eed870',
              name: 'dec',
              dataType: 'string',
              description: {
                en_US: '',
                zh_CN: '这是desc',
                zh_TW: '',
              },
              data_name: 'dec',
              data_type: 'string',
              fullPath: 'getName.dec',
              is_array: false,
              field: null,
              desc: '这是desc',
            },
          ],
          desc: '',
        },
      },
      id: '703b2de1-377a-4e95-860c-4d4e147a53e5',
    },
  ], // 数据源
  globalSetting: {}, // 页面定义
};
