import { Component, OnInit, Input, ChangeDetectorRef } from '@angular/core';
import { LibPageDesignService } from './service/lib-page-design.service';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { BusinessShareInfo } from '../../../bussiness-components/business-share-consumer/type';

@Component({
  selector: 'app-lib-page-design',
  templateUrl: './lib-page-design.component.html',
  styleUrls: ['./lib-page-design.component.less'],
  providers: [LibPageDesignService],
})
export class LibPageDesignComponent implements OnInit {
  @Input() dslBusinessData: any; // 页面基本信息

  // 数据
  dynamicWorkDesignBusinessShareInfo: BusinessShareInfo = null; // 界面数据

  constructor(
    public libDslService: LibPageDesignService,
    private athMessageService: NzMessageService,
    private translateService: TranslateService,
    private cd: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    this.handleInit();
  }

  // 初始化
  private handleInit(): void {
    this.libDslService.setLoading(true);
    const { dynamicWorkDesignRenderData } = this.dslBusinessData.componentProps;
    const { version, dsl } = this.libDslService.dslData;

    // 定义事件处理策略
    const dwDesignDispatchEventHandlers = {
      // 数据源导入
      importDataConnectors: (data: any, callback?: (result: any) => void) => {
        // 模拟处理逻辑
        const result = { success: true, message: '导入成功' };
        // 调用回调函数返回结果
        callback?.(result);
      },
      // todo可以添加其他事件处理逻辑
      // anotherEventType: (data: any, callback?: (result: any) => void) => {
      //   const result = { success: true, message: '其他事件处理成功' };
      //   callback?.(result);
      // },
    };

    this.dynamicWorkDesignBusinessShareInfo = {
      componentType: 'DynamicWorkDesign', // 组件类型
      componentProps: {
        version,
        dynamicWorkDesignInfo: {},
        dynamicWorkDesignRenderData: {
          pageUIElementContent: {
            ...dsl,
            ...dynamicWorkDesignRenderData.pageUIElementContent,
          },
        },
        changeDynamicWorkDesignRenderData: (data) => {
          console.timeEnd('DynamicWorkDesign 渲染结束');
          console.log('----------------页面回调-----------------');
          console.log(data);
        },
        changeDynamicWorkDesignStatus: (data) => {
          this.libDslService.setLoading(data !== 'Ready');
          this.cd.detectChanges();
        },
        dwDesignDispatch: (eventType: string, data: any, callback?: (result: any) => void) => {
          console.log('----------------dwDesignDispatch-----------------');
          console.log(data);
          // 调用事件处理策略
          const handler = dwDesignDispatchEventHandlers[eventType];
          if (handler) {
            handler(data, callback);
          } else {
            console.warn(`未处理的事件类型: ${eventType}`);
          }
        },
      },
    };
  }
}
