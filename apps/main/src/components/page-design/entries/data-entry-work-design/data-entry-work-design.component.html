<nz-spin
  [nzSpinning]="
    !gobalErrorMessage && (dataEntryWorkDesignService.saveLoading || dataEntryWorkDesignService.initInfoLoading)
  "
  [nzTip]="'dj-请勿关闭当前页面' | translate"
>
  <ad-empty class="work-design-empty" [nzNotFoundContent]="contentTpl" *ngIf="!!gobalErrorMessage">
    <ng-template #contentTpl>
      <span>{{ gobalErrorMessage }}</span>
    </ng-template>
  </ad-empty>
  <div class="work-design" *ngIf="!gobalErrorMessage">
    <div class="work-header">
      <div class="header-title">
        <ng-template [ngTemplateOutlet]="headerCustomTemplate"></ng-template>
      </div>
      <!-- <div class="menu-toolbar">
      <div class="menu-toolbar-item" (click)="openCodeModal()">
        <i adIcon iconfont="icondaima123" aria-hidden="true"></i>
        <span>{{ 'dj-代码' | translate }}</span>
      </div>
    </div> -->
      <div class="header-btn" [ngStyle]="{ 'padding-right': modelPageType !== 'notModelDriven' ? '20px' : '60px' }">
        <div
          class="publishe-state"
          [ngClass]="{ published: !!dataEntryWorkDesignService.pageDesignInfo?.publishedTime }"
        >
          {{ (!!dataEntryWorkDesignService.pageDesignInfo?.publishedTime ? 'dj-已发布' : 'dj-未发布') | translate }}
        </div>
        <button ad-button adType="default" (click)="actionModalVisiable = true">
          {{ 'dj-测试action' | translate }}
        </button>
        <button ad-button adType="default" (click)="openCodeModal()">
          {{ 'dj-代码' | translate }}
        </button>
        <ng-container *operateAuth="{ prefix: 'update' }">
          <button ad-button adType="default" (click)="handlePublic()">
            {{ 'dj-发布' | translate }}
          </button>
        </ng-container>
        <ng-container *operateAuth="{ prefix: 'update' }">
          <button ad-button adType="primary" (click)="handleSave()">
            {{ 'dj-保存' | translate }}
          </button>
        </ng-container>

        <a nz-dropdown [nzDropdownMenu]="menu">
          <i class="more" adIcon type="ellipsis"></i>
        </a>
        <nz-dropdown-menu #menu="nzDropdownMenu">
          <ul nz-menu nzSelectable>
            <!-- <li nz-menu-item (click)="openCodeModal()">{{ 'dj-代码' | translate }}</li> -->
            <li nz-menu-item (click)="openModifyHistoryModal()">{{ 'dj-修改历史' | translate }}</li>
          </ul>
        </nz-dropdown-menu>
      </div>
    </div>

    <div class="work-body" *ngIf="!dataEntryWorkDesignService.initInfoLoading">
      <app-business-share-consumer
        *ngIf="!!dynamicWorkDesignBusinessShareInfo"
        #dynamicWorkDesign
        [businessShareInfo]="dynamicWorkDesignBusinessShareInfo"
        appCollaborate
        [collaborateInfo]="{
          type: designerType + modelPageType,
          sourceId: dataEntryWorkDesignService.workDesignInfo?.code
        }"
      ></app-business-share-consumer>
    </div>
  </div>
</nz-spin>

<!-- 代码 -->
<app-extend-editor-modal
  *ngIf="showCodeModal"
  [data]="codeData"
  [title]="'dj-代码' | translate"
  (ok)="handleCodeModal($event)"
  (close)="showCodeModal = false"
>
</app-extend-editor-modal>

<!-- 历史 -->
<app-modify-history-modal
  *ngIf="historyModalProps.transferModal"
  [historyModalProps]="historyModalProps"
  (closeModal)="historyModalProps.transferModal = false"
>
</app-modify-history-modal>

<app-action-modal
  *ngIf="actionModalVisiable"
  [transferModal]="actionModalVisiable"
  labelType="EspAction"
  [transferData]="{}"
  [isDistribution]="true"
  (callBack)="handleSelectActionToDataConnectors($event)"
  (closeModal)="actionModalVisiable = false"
>
</app-action-modal>
