import { cloneDeep, isObject } from 'lodash';
import { dataConnectorTemplate } from '../config/data-entry-work-design.config';

// 通过action组装dataConnectors
export const getDataConnectorByAction = (actionData, espActionFields) => {
  const dataConnector = cloneDeep(dataConnectorTemplate);
  dataConnector.name = espActionFields.data_name;
  dataConnector.description = espActionFields.description;
  const digi_service = dataConnector?.option?.request?.headers?.find((s) => s.key === 'digi-service');
  if (isObject(digi_service.value)) {
    digi_service.value.prod = actionData.provider;
    digi_service.value.name = actionData.serviceName;
    digi_service.value = JSON.stringify(digi_service.value);
  }
  dataConnector.option.response.meta = espActionFields;
  return dataConnector;
};
