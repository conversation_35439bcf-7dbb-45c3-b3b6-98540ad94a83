import { Injectable } from '@angular/core';
import { PageDesignInfo, PageType, PageDslInfo } from '../config/data-entry-work-design.type';
import { Observable, Subject } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

@Injectable()
export class DataEntryWorkDesignService {
  // 作业code
  private _pageDesignCode: string = null;
  get pageDesignCode() {
    return this._pageDesignCode;
  }

  // 作业信息
  private _pageDesignInfo: PageDesignInfo = null;
  get pageDesignInfo() {
    return this._pageDesignInfo;
  }

  // 当前应用下所有作业信息
  private _appPageDesigns: PageDesignInfo[] = null;
  get appPageDesigns() {
    return this._appPageDesigns;
  }

  // 当前页面类型
  private _pageType: PageType = 'design';
  get pageType() {
    return this._pageType;
  }

  // 当前作业下所有页面信息
  get pageDslList(): PageDslInfo[] {
    return this._pageDesignInfo?.pageDslList ?? [];
  }

  // 当前激活的页面信息
  get activePageDslInfo(): PageDslInfo {
    return this.pageDslList.find((item) => item.type === this.pageType);
  }

  private _initInfoLoading: boolean = false; // 初始化数据信息加载的loading
  get initInfoLoading(): boolean {
    return this._initInfoLoading;
  }

  private _saveLoading: boolean = false; // 保存时的loading
  get saveLoading(): boolean {
    return this._saveLoading;
  }

  // 订阅-保存时的loading状态变化
  private _saveLoadingChange$: Subject<boolean> = new Subject<boolean>();
  get saveLoadingChange$(): Observable<boolean> {
    return this._saveLoadingChange$.asObservable();
  }

  constructor(private translateService: TranslateService) {}

  setPageDesignCode(pageDesignCode: string): void {
    this._pageDesignCode = pageDesignCode;
  }

  setPageType(pageType: PageType): void {
    this._pageType = pageType;
  }

  setPageDesignInfo(pageDesignInfo: any): void {
    this._pageDesignInfo = pageDesignInfo;
  }

  setPageDesignInfoByKeyPath(keyPath: string[], value: any): void {
    const targetKey = keyPath.splice(-1);
    const obj = keyPath.reduce((pre, cur) => {
      return pre[cur];
    }, this._pageDesignInfo);

    obj[targetKey[0]] = value;
  }

  // 更新当前页面的Dsl数据
  setActivePageDslInfoDsl(dsl: any) {
    if (this.activePageDslInfo) this.activePageDslInfo.dsl = dsl;
  }

  setInitInfoLoading(initInfoLoading: boolean): void {
    this._initInfoLoading = initInfoLoading;
  }

  setSaveLoading(saveLoading: boolean): void {
    this._saveLoading = saveLoading;
    this._saveLoadingChange$.next(saveLoading);
  }

  setAppPageDesigns(appPageDesigns: any): void {
    this._appPageDesigns = appPageDesigns;
  }
}
