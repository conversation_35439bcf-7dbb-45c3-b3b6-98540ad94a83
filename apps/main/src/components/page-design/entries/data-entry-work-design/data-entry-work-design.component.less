:host {
  ::ng-deep .ant-drawer-header {
    padding: 8px 24px;
  }
  ::ng-deep.ant-drawer-body {
    padding: 0;
    // padding: 0 0 8px;
  }
  ::ng-deep .ant-drawer-close {
    padding: 10px 14px;
  }
  ::ng-deep {
    nz-spin,
    .ant-spin-container {
      height: 100%;
      &.ant-spin-blur{
        opacity: 0.25;
      }
    }
  }

  .work-design-empty {
    margin-top: 200px
  }

  .work-design {
    height: 100%;
    border-top: 1px solid #eeeeee;
    .work-header {
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-title {
        flex: 3;
        padding-left: 10px;
        font-size: 15px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        // max-width: calc(50% - 272px);
        color: rgba(0, 0, 0, 0.85);
      }
      .header-title-tpl {
        flex: 3;
        font-size: 15px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: rgba(0, 0, 0, 0.85);
      }
      .header-menu {
        flex: 1;
        height: 28px;
        line-height: 28px;
        text-align: right;
        .header-menu-show{
          font-size: 13px;
          max-width: 100%;
          user-select: none;
          text-overflow: ellipsis;
          overflow: hidden;
          word-wrap: break-word;
          word-break: break-all;
          white-space: nowrap;
          .anticon{
            transition: all 0.3s ease-out;
            &.open{
              transform: rotate(180deg);
            }
          }


        }   
      }

      .menu-divider{
        width: 1px;
        border-left: 1px solid #D8D8D8;
        height: 16px;
        line-height: 28px;
        flex-grow: 0;
        margin: 0 12px;
      }

      .menu-toolbar {
        flex: 2;
        height: 28px;
        line-height: 28px;
        text-align: left;
        .menu-toolbar-item{
          height: 28px;
          line-height: 28px;
          font-size: 13px;
          color: #666666;
          float: left;
          margin-right: 12px;
          cursor: pointer;
          &:hover{
            opacity: 0.75;
          }
          span{
            margin-left: 4px;
          }
          .anticon{
            font-size: 14px;
          }
        }
      }

      .header-btn {
        flex: 2;
        text-align: right;

        .closeIntro {
          margin-left: 20px;
          color: rgba(0, 0, 0, 0.45);
          font-size: 12px;
        }

        .more {
          font-size: 20px;
          font-weight: bolder;
          margin-left: 10px;
        }

        .change-data-view{
          background: #eef0ff;
          color: #6868ae;
          height: 28px;
          line-height: 28px;
          padding: 0 20px;
          border-radius: 16px;
          margin-right: 10px;
          font-size: 13px;
          display: inline-block;
          cursor: pointer;
      
          &:hover {
            opacity: 0.75;
          }
        }
        
        button {
          margin-left: 10px;
        }

        .publishe-state {
          padding: 0 8px;
          border-radius: 4px;
          font-size: 10px;
          line-height: 16px;
          color: #4E5369;
          background: #EFF1FF;
          display: inline-block;

          &.published{
            color: #009A29;
            background: #E8FFEA;
          }
        }
      }
    }
    .work-body {
      border-top: 1px solid #eeeeee;
      height: calc(100% - 42px);

      .hide{
        display: none;
      }
    }
    
  }
}

:host ::ng-deep .close-page-modal {
  .ant-modal-body {
    padding: 20px 24px;
  }
  .confirm-tips {
    text-align: center;
    margin: 20px 0 30px;
  }
  .modal-footer {
    text-align: center;
    padding-top: 10px;
    button {
      margin-left: 10px;
    }
  }
}

::ng-deep .work-design{
  .header-menu-list {
    padding: 2px 4px;
    border-radius: 4px;

    .text-overflow {
      text-overflow: ellipsis;
      overflow: hidden;
      word-wrap: break-word;
      word-break: break-all;
      white-space: nowrap;
    };

    & .ant-divider{
      margin: 2px 0;
    }
    .header-menu-list-item {
      width: 100%;
      min-width: 120px;
      max-width: 200px;
      line-height: 32px;
      text-align: center;
      font-size: 13px;
      padding: 0 12px;
      color: #333333;
      cursor: pointer;
   
      border-radius: 4px;
      margin: 4px 0;

      &.active {
        background-color: #EEF0FF;
        font-weight: 400;
        color: #6A4CFF;
      }

      &.handle-button{
        color: #6A4CFF;
        &:hover{
          background: none;
        }
        .anticon{
          font-size: 12px;
          margin-right: 4px;
        }
      }

      & > .item-handle-title{
        text-align: left;
        float: left;
        width: calc(100% - 20px);
      }

      & > .item-handle-button{
        width: 20px;
        float: right;
        color: #6A4CFF;
        .anticon{
         margin: 0 2px;
         &:hover{
           opacity: 0.75;
         }
        }
      }

      & > .main-page {
        background-color: #6848f7;
        font-size: 10px;
        border-radius: 4px;
        float: left;
        margin-left: 6px;
        color: #ffffff;
        padding: 4px;
        line-height: 10px;
      }
    }
  }  
}