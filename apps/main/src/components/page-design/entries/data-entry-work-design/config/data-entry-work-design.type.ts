// 多语言

export type PageType = 'browse' | 'edit' | 'design';
export interface Lang {
  [propName: string]: LangObject;
}

// 多语言对象
export interface LangObject {
  zh_CN: string;
  zh_TW: string;
  en_US?: string;
}

// 代码数据
export interface CodeData {
  [propName: string]: any;
}

export interface PageDesignInfo {
  code: string;
  pageDslList: PageDslInfo[];
  name: string;
  lang: Lang;
  [propName: string]: any;
}

export interface PageDslInfo {
  code: string;
  dsl: any;
  [propName: string]: any;
}
