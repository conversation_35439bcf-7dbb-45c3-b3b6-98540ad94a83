<!-- <nz-drawer
  [nzBodyStyle]="{ overflow: 'auto', padding: '12px 0 0 0' }"
  [nzMaskClosable]="false"
  [nzWidth]="'95%'"
  [nzVisible]="visible"
  [nzClosable]="false"
>
  <ng-container *nzDrawerContent>
    <app-data-view-header></app-data-view-header>
    <app-data-view-container></app-data-view-container>
  </ng-container>
</nz-drawer> -->
<div class="data-view-design" *ngIf="dataViewService.dataViewCode">
  <app-data-view-header
    *ngIf="dataViewService.isShowHeader"
    [headerCustomTemplate]="headerCustomTemplate"
    (view)="handleView()"
  ></app-data-view-header>

  <ng-container *ngIf="dataViewService?.dataViewBase">
    <app-data-view-sql-container *ngIf="isSQLDataView"> </app-data-view-sql-container>

    <app-data-view-container
      *ngIf="!isSQLDataView"
      [pageFrom]="pageFrom"
      [editCustomTemplate]="editCustomTemplate"
      [leftCustomWholeTemplate]="leftCustomWholeTemplate"
      [editCustomWholeTemplate]="editCustomWholeTemplate"
      [centerCustomTemplate]="centerCustomTemplate"
      [dataSourceFields]="dataSourceFields"
    ></app-data-view-container>
  </ng-container>
  <nz-spin
    [nzSpinning]="dataViewService.isInitLoading || dataViewService.isSaveLoading || dataViewService.isPublishLoading"
    [nzTip]="(dataViewService.isPublishLoading ? 'dj-请勿关闭当前页面' : '') | translate"
    *ngIf="dataViewService.isInitLoading || dataViewService.isSaveLoading || dataViewService.isPublishLoading"
  ></nz-spin>
</div>

<div class="data-view-design-empty" *ngIf="!dataViewService.dataViewCode">
  <ad-empty [nzNotFoundContent]="contentTpl">
    <ng-template #contentTpl>
      <span>{{ 'dj-暂无数据' | translate }}</span>
    </ng-template>
  </ad-empty>
</div>
