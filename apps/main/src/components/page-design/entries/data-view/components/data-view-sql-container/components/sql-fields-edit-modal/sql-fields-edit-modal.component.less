.field-table {
  .selected-row {
    ::ng-deep td {
      cursor: default;
      border-top: 2px solid #6a4cff;
      border-bottom: 2px solid #6a4cff;
      &:first-child {
        border-left: 2px solid #6a4cff;
      }
      &:last-child {
        border-right: 2px solid #6a4cff;
      }
    }
  }
  ::ng-deep {
    .ant-table-placeholder {
      .ant-table-expanded-row-fixed {
        width: unset !important;
      }
    }
    .operate {
      font-size: 14px;
      color: #6a4cff;
      padding: 3px !important;
      margin-left: 4px;
      &:first-child {
        svg {
          cursor: move !important;
        }
        margin-left: 0;
        cursor: move;
      }
    }
  }
}

.table-item-placeholder {
  height: 240px;
  margin-top: 16px;
  width: 100%;
  background: #f8fafd;
  border-radius: 4px;
}
::ng-deep {
  svg {
    #icontuozhuaiIC {
      path {
        cursor: move !important;
      }
    }
  }
}
