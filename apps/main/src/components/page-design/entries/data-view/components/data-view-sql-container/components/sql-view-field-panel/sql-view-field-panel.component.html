<div class="panel" [ngClass]="{ closed: !dataViewService.rightIsShow }">
  <div class="toggle-button" (click)="toggleIsShow()">
    <i adIcon iconfont="icondoubleRight-copy" class="iconfont" aria-hidden="true"></i>
  </div>
  <div class="header">
    <span>{{ 'dj-字段' | translate }}</span>
  </div>
  <div class="content">
    <nz-collapse class="custom-collapse">
      <!-- header -->
      <nz-input-group [nzPrefix]="search" class="input-group">
        <input nz-input [(ngModel)]="searchValue" [placeholder]="'dj-请输入' | translate" />
        <ng-template #search>
          <i adIcon type="search" class="search"></i>
        </ng-template>
      </nz-input-group>
      <div nz-row class="custom-row">
        <button
          [nzLoading]="loading"
          ad-button
          adType="primary"
          class="custom-button"
          nzSize="small"
          (click)="generateDataView($event)"
        >
          <i adIcon iconfont="iconlianji"></i>
          {{ 'dj-生成查询方案字段' | translate }}
        </button>
        <button ad-button class="custom-button" nzSize="small" (click)="batchEditField($event)">
          <i adIcon iconfont="icona-lianji1"></i>
          {{ 'dj-批量编辑字段' | translate }}
        </button>
      </div>
      <!-- SQL查询方案字段 -->
      <nz-collapse-panel #field [nzActive]="true" [nzHeader]="fieldHeader" nzExpandedIcon="caret-right">
        <app-sql-fields #sqlField [searchValue]="searchValue"></app-sql-fields>
        <ng-template #fieldHeader>
          <div class="collapse-title">{{ 'dj-查询方案的Code' | translate }}</div>
        </ng-template>
      </nz-collapse-panel>
      <!-- 数据排序 -->
      <!-- <nz-collapse-panel #sort [nzActive]="true" [nzHeader]="sortHeader" nzExpandedIcon="caret-right">
        <ng-template #sortHeader>
          <div class="collapse-title">{{ 'dj-数据排序' | translate }}</div>
          <i
            nz-tooltip
            adIcon
            iconfont="iconexplain"
            class="iconfont"
            style="margin-left: 8px"
            aria-hidden="true"
            [nzTooltipTitle]="'dj-运行时页面初次载入数据的默认展示排序' | translate"
          >
          </i>
        </ng-template>
        <app-data-view-table-sort
          [showHeader]="false"
          [orderList]="dataViewService.orderList"
          [allFields]="dataViewService.allFields"
          [currentLang]="dataViewService.currentLang"
          (tableSortChange)="handleTableSortChange($event)"
        ></app-data-view-table-sort>
      </nz-collapse-panel> -->
      <!-- 数据筛选 -->
      <!-- <nz-collapse-panel #filter [nzActive]="true" [nzHeader]="filterHeader" nzExpandedIcon="caret-right">
        <ng-template #filterHeader>
          <div class="collapse-title">{{ 'dj-数据排序' | translate }}</div>
          <i
            nz-tooltip
            adIcon
            iconfont="iconexplain"
            class="iconfont"
            style="margin-left: 8px"
            aria-hidden="true"
            [nzTooltipTitle]="'dj-运行时页面初次载入所过滤的数据' | translate"
          >
          </i>
        </ng-template>
        <app-data-view-filter
          [showTitle]="false"
          [allFields]="dataViewService.allFields"
          [currentLang]="dataViewService.currentLang"
          [conditionList]="dataViewService.conditionList"
          [variableConditions]="dataViewService.variableConditions"
          (dataViewFilterChange)="handleDataViewFilterChange($event)"
        ></app-data-view-filter>
      </nz-collapse-panel> -->
    </nz-collapse>
  </div>
</div>

<!-- 批量编辑字段 -->
<app-sql-fields-edit-modal
  [visible]="batchModalVisible"
  (cancel)="handleModalCancel()"
  (confirm)="handleModalConfirm($event)"
></app-sql-fields-edit-modal>
