import {
  AfterViewInit,
  Component,
  ElementRef,
  OnInit,
  ViewChild,
  Inject,
  OnDestroy,
  Output,
  EventEmitter,
} from '@angular/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { SystemConfigService } from 'common/service/system-config.service';
import { AD_AUTH_TOKEN, AdAuthService } from 'pages/login/service/auth.service';
import { LocaleService } from 'common/service/locale.service';
import { DigiMiddlewareAuthApp } from 'common/config/app-auth-token';
import { AdUserService } from 'pages/login/service/user.service';
import { debounce, delay } from 'lodash';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { TranslateService } from '@ngx-translate/core';
import { DataViewRequestService } from 'components/page-design/entries/data-view/service/data-view-request.service';
import { DataViewService } from 'components/page-design/entries/data-view/service/data-view.service';

@Component({
  selector: 'app-sql-view-editor',
  templateUrl: './sql-view-editor.component.html',
  styleUrls: ['./sql-view-editor.component.less'],
})
export class SqlViewEditorComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('sqlEditor', { static: true }) sqlEditor: ElementRef;

  @Output() abort: EventEmitter<void> = new EventEmitter();
  @Output() invoke: EventEmitter<string> = new EventEmitter();

  public demoConfig = [
    {
      title: this.translate.instant('dj-常规查询'),
      content: `select product_name, product_count,com_time from product_comin where tenantsid=809900105574400\n${this.translate.instant(
        'dj-或者',
      )}\nselect product_name, product_count,com_time from product_comin where tenantsid={@SYSTEM_TENANT_SID}`,
    },
    {
      title: this.translate.instant('dj-关联查询'),
      content: `select manage_status ,complete_state ,eoc_company_id ,owner_dept_name ,owner_emp_name,wylowcode_test_normal.create_date ,wylowcode_test_normal.create_by ,wylowcode_test_normal_zizi.wylowcode_test_normal_zizi_id ,wylowcode_test_normal_zizi.wylowcode_test_normal_id 
from
        wylowcode_test_normal
left join wylowcode_test_normal_zizi on
        wylowcode_test_normal.wylowcode_test_normal_id = wylowcode_test_normal_zizi.wylowcode_test_normal_zizi_id
        and wylowcode_test_normal.tenantsid = {@SYSTEM_TENANT_SID}`,
    },
    {
      title: this.translate.instant('dj-时间查询'),
      content: `select manage_status , complete_state , eoc_company_id , owner_dept_name , owner_emp_name, create_date , create_by 
from wylowcode_test_normal where tenantsid = {@SYSTEM_TENANT_SID} and create_date {@SYSTEM_DATE_IN_7}`,
    },
  ];

  private clientHeight: number = 100;
  private cacheReact: any;
  private cacheReactDOM: any;
  private root: any;
  private component: any;
  private ref: any;
  private systemConfig: any;
  private observer: ResizeObserver;
  private changeDispose?: any;
  private keydownDispose?: any;

  /**
   * 所有的表
   */
  private tableList: any = [];
  /**
   * 表的字段
   */
  private fieldMap: Map<string, any[]> = new Map();

  // 是否换行
  public wordWrap: boolean = false;
  public nzVisible: boolean = false;

  private get serviceCode() {
    return this.dataViewService.dataViewBase?.productCode;
  }

  constructor(
    private message: NzMessageService,
    private configService: SystemConfigService,
    private authService: AdAuthService,
    private languageService: LocaleService,
    private userService: AdUserService,
    private modal: AdModalService,
    private translate: TranslateService,
    private dataviewRequest: DataViewRequestService,
    private dataViewService: DataViewService,
    @Inject(AD_AUTH_TOKEN) protected authToken: any,
  ) {
    this.configService.getConfig().subscribe((config) => {
      this.systemConfig = {
        auth: this.authService.userPermissionMap,
        authId: this.authService.authId,
        currentLanguage: this.languageService?.currentLanguage || 'zh_CN',
        digiMiddlewareAuthApp: DigiMiddlewareAuthApp,
        userInfo: this.userService.getUserInfo(),
        config,
        authToken: this.authToken,
        adesignerUrl: config.adesignerUrl,
      };
    });
  }

  ngOnInit() {
    if (this.dataViewService.isSQLDataView) {
      this.dataViewService.setLastGenerateFieldSql(this.dataViewService.dataViewBase.executeSql);
    }
  }

  ngOnDestroy(): void {
    this.observer?.disconnect();
    this.observer = undefined;
    this.changeDispose?.dispose?.();
    this.keydownDispose?.dispose?.();
    this.dataViewService.setLastGenerateFieldSql(undefined);
  }

  async ngAfterViewInit() {
    await this.loadCache();
    await this.loadTables(this.serviceCode);
    requestAnimationFrame(() => {
      const div = this.sqlEditor.nativeElement as HTMLDivElement;
      this.clientHeight = div.getBoundingClientRect().height;
      this.reanderComponent();

      this.addObserver();
    });
  }

  /**
   * 自动换行
   */
  public handleWordWrap() {
    this.wordWrap = !this.wordWrap;
    const newWordWrap = this.wordWrap ? 'on' : 'off';
    this.ref.current?.getEditor()?.updateOptions({ wordWrap: newWordWrap });
  }

  /**
   * 复制
   */
  public async handleCopy() {
    const editor = this.ref.current?.getEditor?.();
    const result = editor.getValue() || '';
    navigator.clipboard.writeText(result).then(() => {
      // 可以添加复制成功的提示
      this.message.success(this.translate.instant('dj-复制成功'));
    });
  }

  /**
   * 清除内容
   */
  public async handleClear() {
    this.modal.confirm({
      nzTitle: this.translate.instant('dj-确认清空数据吗'),
      nzOnOk: () => {
        const control = this.ref.current?.getController();
        control?.setValueText('');
      },
      nzOnCancel: () => {},
    });
  }

  public async handleAiNew() {
    // todo
  }

  /**
   * ai检测
   */
  public async handleAiCheck() {
    const editor = await this.ref.current?.getEditor?.();
    const sql = editor.getValue() || '';
    if (!sql?.length) {
      this.message.error(this.translate.instant('dj-请输入SQL'));
      return;
    }
    try {
      const result: any = await this.dataviewRequest.checkSqlByAi(sql).toPromise();
      if ('SQL语句校验通过' === result?.data?.result) {
        this.message.success(this.translate.instant('dj-SQL语句校验通过'));
      } else {
        this.modal.info({
          nzTitle: this.translate.instant('dj-检查结果'),
          nzContent: result.data?.explain,
        });
      }
    } catch (e) {
      this.message.error(e.message || e);
    }
  }

  /**
   * 重新导入
   */
  public handleReImport() {
    this.modal.confirm({
      nzTitle: this.translate.instant('dj-重新从后端导入表') + '?',
      nzOkText: this.translate.instant('dj-确定'),
      nzOnOk: async () => {
        const keys = Array.from(this.fieldMap.keys());
        this.tableList = [];
        this.fieldMap.clear();
        this.loadTables(this.serviceCode);
        if (keys.length) {
          keys.forEach((key) => {
            this.loadTableFields(this.serviceCode, key);
          });
        }
      },
      nzCancelText: this.translate.instant('dj-取消'),
      nzOnCancel: () => {},
    });
  }

  /**
   * 终止执行
   */
  public handleAbort() {
    this.abort.emit();
  }

  /**
   * 执行sql
   * @param type
   */
  public async handleRun(type: 'all' | 'selection') {
    const sql = await this.getWaitInvokeSql(type);
    if (!sql) return;
    this.invoke.emit(sql);
  }

  // private

  public async getWaitInvokeSql(type: 'all' | 'selection'): Promise<string | undefined> {
    let sql: string | undefined = undefined;
    if (type === 'all') {
      const result = this.ref.current?.getSqlWithNumber();
      if (!result.value) {
        this.message.error(this.translate.instant('dj-请输入SQL'));
        return;
      }
      if (result?.error) {
        this.message.error(this.translate.instant('dj-请检查SQL'));
        return;
      }
      sql = result.value;
    } else {
      const result = await this.ref.current?.getSqlByCursor?.();
      if (result?.error) {
        if (!result.value?.length) {
          this.message.error(this.translate.instant('dj-请选择SQL'));
        } else {
          this.message.error(this.translate.instant('dj-请检查SQL'));
        }
        return;
      }
      sql = result.value;
    }
    return sql;
  }

  private async loadTables(serviceCode: string) {
    try {
      const result: any = await this.dataviewRequest.queryTableAndFields(serviceCode).toPromise();
      if (result.code === 0) {
        this.tableList = result.data.map((e) => ({
          name: e.tableName,
          displayName: e.tableComment,
        }));
      }
    } catch {
      // empty
    }
  }

  private async loadTableFields(serviceCode: string, table: string) {
    try {
      if (!this.fieldMap.has(table)) {
        const result: any = await this.dataviewRequest.queryTableAndFields(serviceCode, table).toPromise();
        if (result.code === 0) {
          const fields =
            result.data?.[0]?.tableField?.map((e) => ({
              name: e.fieldName,
              displayName: e.fieldDisplayName,
            })) || [];
          this.fieldMap.set(table, fields);
        }
      }
      return this.fieldMap.get(table) || [];
    } catch {
      return [];
    }
  }

  private addObserver() {
    this.observer = new ResizeObserver(
      debounce(
        () => {
          const div = this.sqlEditor.nativeElement as HTMLDivElement;
          const rect = div.getBoundingClientRect();
          this.ref.current?.getEditor()?.layout({
            width: rect.width,
            height: rect.height,
          });
        },
        100,
        { leading: false, trailing: true },
      ),
    );
    this.observer.observe(this.sqlEditor.nativeElement);
  }

  private async loadCache() {
    try {
      const [react, reactDom, editor] = await Promise.all([
        import('react'),
        import('react-dom/client'),
        //@ts-ignore
        import(`athena_designer_core/SqlEditor`),
      ]);
      this.cacheReact = react.default;
      this.cacheReactDOM = reactDom.default;
      this.component = editor.default;
      this.ref = this.cacheReact.createRef();
      this.root = this.cacheReactDOM.createRoot(this.sqlEditor.nativeElement);
    } catch (e) {
      this.message.error(e.message);
    }
  }

  private getConfig = ({ targetDataType, tableName }) => {
    return new Promise(async (resolve) => {
      if (targetDataType === 0) {
        resolve(this.tableList);
      } else if (targetDataType === 1) {
        const fields = await this.loadTableFields(this.serviceCode, tableName);
        const MockData = fields.map((data) => {
          return {
            name: data.name,
            displayName: data.displayName,
            description: data.description,
          };
        });
        resolve(MockData);
      }
    });
  };

  private onMount = async () => {
    const control = this.ref.current?.getController?.();
    const executeSql = this.dataViewService.dataViewBase?.executeSql;
    if (executeSql) {
      this.setEditorValue(control, executeSql);
    }
    const editor = this.ref.current?.getEditor?.();
    // 键盘按下监听事件
    this.keydownDispose = editor?.onKeyDown((event) => {
      const { keyCode, ctrlKey, metaKey } = event;
      if (keyCode === 52 && (metaKey || ctrlKey)) {
        delay(() => {
          const text = editor?.getValue();
          this.setEditorValue(control, text);
        }, 20);
      }
    });
    // 内容变化监听事件
    this.changeDispose = editor?.onDidChangeModelContent(() => {
      this.dataViewService.dataViewBase.executeSql = editor?.getValue() || '';
    });
  };

  private setEditorValue(control: any, sql: string): void {
    const tableNames = this.getTableNameFromSql(sql);
    delay(() => {
      control?.setValueText(sql, tableNames);
    }, 100);
  }

  private systemVariable() {
    return [
      {
        label: this.translate.instant('dj-当前用户的ID'),
        value: '{@SYSTEM_USER_ID}',
      },
      {
        label: this.translate.instant('dj-当前用户的员工ID'),
        value: '{@SYSTEM_EMPLOYEE_ID}',
      },
      {
        label: this.translate.instant('dj-当前用户的员工部门ID'),
        value: '{@SYSTEM_EMPLOYEE_DEPTID}',
      },
      {
        label: this.translate.instant('dj-当前用户的所有部门'),
        value: '{@SYSTEM_EMPLOYEE_DEPTID_ALL}',
      },
      {
        label: this.translate.instant('dj-今天'),
        value: '{@SYSTEM_DATE_TODAY}',
      },
      {
        label: this.translate.instant('dj-这周'),
        value: '{@SYSTEM_DATE_WEEK}',
      },
      {
        label: this.translate.instant('dj-这月'),
        value: '{@SYSTEM_DATE_MONTH}',
      },
      {
        label: this.translate.instant('dj-7天内'),
        value: '{@SYSTEM_DATE_IN_7}',
      },
      {
        label: this.translate.instant('dj-当前租户SID'),
        value: '{@SYSTEM_TENANT_SID}',
      },
    ];
  }

  private reanderComponent() {
    const newProps = {
      systemConfig: this.systemConfig,
      minimap: false,
      height: this.clientHeight + 'px',
      supportDynamicParams: true,
      systemVariable: this.systemVariable(),
      ref: this.ref,
      getConfig: this.getConfig,
      onMount: this.onMount,
    };
    this.root.render(this.cacheReact.createElement(this.component, newProps));
  }

  private getTableNameFromSql(sql: string) {
    const parser = require('js-sql-parser');
    try {
      const result = parser.parse(this.transformSql(sql));
      const deep = (value, set = new Set<string>()) => {
        if (value.from) {
          value.from.value?.forEach((item) => {
            if (item.value?.value?.value) {
              set.add(item.value.value.value);
            }
            if (item.value?.left?.value?.value) {
              set.add(item.value.left.value.value);
            }
            if (item.value?.right?.value?.value) {
              set.add(item.value.right.value.value);
            }
          });
        }
        if (value.left) {
          deep(value.left, set);
        }
        if (value.right) {
          deep(value.right, set);
        }
        return set;
      };
      return Array.from(deep(result.value));
    } catch (error) {
      this.message.error(this.translate.instant('dj-sql无法解析'));
      return [];
    }
  }

  private transformSql(sql: string) {
    if (!sql) return sql;
    const systemVariables = this.systemVariable();
    let returnSql = sql;
    systemVariables.forEach((item) => {
      //@ts-ignore
      returnSql = returnSql.replaceAll(item.value, `'${item.value}'`);
    });
    return returnSql;
  }
}
