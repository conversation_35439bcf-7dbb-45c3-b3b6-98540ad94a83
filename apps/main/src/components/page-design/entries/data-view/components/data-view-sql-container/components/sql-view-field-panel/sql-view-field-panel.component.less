.panel {
  background-color: #fff;
  width: 320px;
  height: 100%;
  transition: all ease 0.15s;
  position: relative;
  border-top: 1px solid #edeff3;

  .toggle-button {
    width: 32px;
    height: 44px;
    line-height: 44px;
    background-color: #ffffff;
    position: absolute;
    top: 2px;
    bottom: 2px;
    right: 0;
    font-size: 12px;
    z-index: 3;
    .iconfont {
      font-size: 12px;
      transition: all 0.3s ease-in-out;
      transform: rotate(0deg);
    }
  }

  & > .header {
    height: 48px;
    padding: 0 14px;
    line-height: 48px;
    border-bottom: 1px solid #e5e5e5;

    & > span {
      float: left;
      font-size: 16px;
      color: #1d1c33;
      margin-right: 4px;
      max-width: 70%;
      overflow: hidden;
      white-space: nowrap;
      word-wrap: break-word;
      word-break: break-all;
      text-overflow: ellipsis;
    }
  }

  & > .content {
    padding: 12px 14px;
    height: calc(100% - 72px);
    overflow-y: auto;
    width: 100%;
    .input-group {
      border-radius: 4px;
      .search {
        color: #a6a6b2;
      }
    }
    & > .content-item {
    }

    .custom-row {
      margin-top: 12px;
      .custom-button {
        display: flex;
        flex: 1;
        margin-left: 12px;
        justify-content: center;
        align-items: center;
        &:first-child {
          margin-left: 0;
        }
      }
    }

    .custom-collapse {
      .collapse-title {
        font-size: 14px;
        color: #1d1c33;
      }
      ::ng-deep {
        .ant-collapse-header {
          padding: 12px 0 0 0 !important;
        }
        .ant-collapse-content {
          margin: 10px 14px 0 14px;
        }
      }
    }
  }

  & > .edit-custom-whole-right-template {
    height: 100%;
    width: 100%;
  }

  &.closed {
    width: 0 !important;
    & > .toggle-button {
      border-radius: 4px 0 0 4px;
      box-shadow: -5px 5px 10px 0px rgba(0, 0, 0, 0.06);
      .iconfont {
        transform: rotate(180deg);
      }
    }
    & > .edit-custom-whole-right-template {
      display: none;
    }
  }
}
