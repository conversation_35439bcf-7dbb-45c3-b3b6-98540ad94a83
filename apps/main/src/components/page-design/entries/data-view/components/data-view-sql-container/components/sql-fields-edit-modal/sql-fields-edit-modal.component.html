<!-- edge设置modal -->
<ad-modal
  [nzVisible]="visible"
  nzWidth="650px"
  [nzTitle]="'dj-统一调整字段' | translate"
  (nzOnCancel)="handleCancel()"
  (nzOnOk)="handleOK()"
>
  <ng-container *adModalContent>
    <nz-table
      #table
      *ngIf="visible"
      [nzData]="sourceTree"
      class="field-table"
      [nzFrontPagination]="false"
      [nzShowSizeChanger]="false"
      [nzScroll]="{ x: '100%', y: '400px' }"
    >
      <thead>
        <tr>
          <th nzWidth="140px">{{ 'dj-字段' | translate }}</th>
          <th nzWidth="130px">{{ 'dj-描述' | translate }}</th>
          <th nzWidth="120px">{{ 'dj-数据类型' | translate }}</th>
          <th nzWidth="150px">{{ 'dj-列编辑器' | translate }}</th>
          <th nzWidth="100px" nzRight>{{ 'dj-操作' | translate }}</th>
        </tr>
      </thead>
      <tbody
        cdkDropList
        cdkDropListOrientation="vertical"
        [cdkDropListSortPredicate]="sortPredicate"
        (cdkDropListDropped)="handleDrop($event)"
      >
        <tr
          *ngFor="let item of sourceTree"
          cdkDrag
          [cdkDragData]="item"
          cdkDragPreviewContainer="parent"
          [ngClass]="{
            'selected-row': item.data_name === editName
          }"
          (click)="editName = item.data_name"
        >
          <td style="min-width: 140px" [nzBreakWord]="true">{{ item.data_name }}</td>
          <td style="min-width: 130px" [nzBreakWord]="true">
            <ng-container *ngIf="item.data_name === editName; else descTemplate">
              <app-modal-input
                [attr]="{
                  name: '描述',
                  required: true,
                  lang: item.lang?.description,
                  needLang: true
                }"
                [value]="item.lang?.description[('dj-LANG' | translate)]"
                (callBack)="handlePatchDescription(item, 'description', $event)"
                ngDefaultControl
              ></app-modal-input>
            </ng-container>
            <ng-template #descTemplate>
              {{item.lang?.description?.[dataViewService.currentLang] ?? item.description}}
            </ng-template>
          </td>
          <td style="min-width: 120px" [nzBreakWord]="true">{{ item.data_type }}</td>
          <td style="min-width: 110px" [nzBreakWord]="true">
            <ad-select
              *ngIf="item.data_name === editName; else editTemplate"
              style="width: 100%"
              [(ngModel)]="item.field_dsl.type"
              [nzAllowClear]="false"
            >
              <ad-option
                *ngFor="let option of showComponentTypeList"
                [nzLabel]="option.label"
                [nzValue]="option.type"
              ></ad-option>
            </ad-select>
            <ng-template #editTemplate>
              {{ componentMap[item.field_dsl.type] }}
            </ng-template>
          </td>
          <td style="min-width: 100px" [nzBreakWord]="true" nzRight>
            <i adIcon class="operate" iconfont="icontuozhuaiIC" cdkDragHandle></i>
            <i
              adIcon
              class="operate"
              *ngIf="!disabledKeys.includes(item.data_name)"
              iconfont="icondelete31"
              (click)="handleDelete(item)"
            ></i>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </ng-container>
</ad-modal>
