.tree-node {
  width: 100%;

  .node-content {
    float: left;
    max-width: 70%;
    text-overflow: ellipsis;
    overflow: hidden;
    word-wrap: break-word;
    word-break: break-all;
    white-space: nowrap;
    font-size: 13px;
    & > .iconfont {
      margin-right: 2px;
    }
    & > .comment {
      font-size: 12px;
      color: #8c8b99;
      margin-left: 12px;
    }
  }

  .node-tools {
    float: right;
    display: none;
    i {
      margin-right: 3px;
      color: #666;
    }
    .drag {
      position: relative;
      top: 1px;
      font-size: 16px;
      ::ng-deep svg {
        cursor: move !important;
      }
    }
  }

  &:hover {
    .node-content {
      max-width: 65%;
    }
    .node-tools {
      display: block;
    }
  }
}

.field-tree {
  ::ng-deep .ant-tree-list {
    padding: 0;
  }

  ::ng-deep .ant-tree-checkbox-checked:not(.ant-tree-checkbox-disabled) .ant-tree-checkbox-inner {
    background-color: #6868ae;
    border-color: #6868ae;
  }

  ::ng-deep .ant-tree-switcher {
    width: 20px;
  }

  &.no-ant-tree-switcher {
    ::ng-deep .ant-tree-switcher {
      width: 4px;
    }
  }
}
