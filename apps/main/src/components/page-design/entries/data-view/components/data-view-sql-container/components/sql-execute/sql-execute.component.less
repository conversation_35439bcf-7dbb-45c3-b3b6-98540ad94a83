.sql-execute {
  width: 100%;
  height: 260px;
  padding: 12px;
  .preview-debug-container {
    margin-top: 10px;

    ::ng-deep {
      .effects {
        .ai-fix {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          padding: 8px 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #fef1e9;
          cursor: pointer;
          .img {
            width: 16px;
            height: auto;
          }
          .text {
            font-size: 12px;
            line-height: 16px;
            text-align: center;
            letter-spacing: normal;
            background: linear-gradient(90deg, #5050e3 0%, #d91ad9 71%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
            margin-left: 6px;
          }
        }
      }
    }
    .resultTable {
      ::ng-deep {
        .empty-data {
          .icon {
            width: 100px;
            height: auto;
          }
          .text {
            margin-bottom: 10px;
            font-size: 14px;
            line-height: 20px;
            text-align: center;
            color: #8c8b99;
          }
        }
      }
    }
  }
}
::ng-deep {
  #iconyunhang1,
  #iconSQL {
    path {
      fill: inherit !important;
    }
  }
}
