<div class="sql-execute">
  <div class="preview-debug-tabs">
    <nz-radio-group [(ngModel)]="mode">
      <label nz-radio-button nzValue="query">
        <i adIcon iconfont="iconbiaoge"></i>
        {{ 'dj-查询结果' | translate }}
      </label>
      <label nz-radio-button nzValue="invoke">
        <i adIcon iconfont="iconyunhang1" style="transform: scale(1.2)"></i>
        {{ 'dj-运行结果' | translate }}
      </label>
    </nz-radio-group>
  </div>
  <div class="preview-debug-container">
    <ng-container *ngIf="mode === 'query'">
      <nz-table
        #basicTable
        [nzBordered]="true"
        [nzShowPagination]="false"
        [nzLoading]="loading"
        [nzData]="results"
        [nzNoResult]="noResult"
        class="resultTable"
        [nzScroll]="{ x: '100%', y: '160px' }"
      >
        <thead>
          <tr>
            <th nzWidth="50px" nzAlign="left">{{ 'dj-序号' | translate }}</th>
            <th
              nzWidth="100px"
              nzAlign="left"
              nz-tooltip
              [nzTooltipTitle]="item.data_name + '(' + item.description + ')'"
              nzEllipsis
              *ngFor="let item of fields"
            >
              {{ item.description }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr nzAlign="left" *ngFor="let row of results; let idx = index">
            <td>{{ idx + 1 }}</td>
            <td nzEllipsis *ngFor="let col of fields">
              <span nz-tooltip [nzTooltipTitle]="row[col.data_name]">
                <ng-container *ngIf="col.data_name; else noData">
                  {{ row[col.data_name] }}
                </ng-container>
                <ng-template #noData> -- </ng-template>
              </span>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </ng-container>

    <ng-container *ngIf="mode === 'invoke'">
      <nz-table
        [nzShowPagination]="false"
        [nzLoading]="loading"
        [nzData]="records"
        [nzNoResult]="noResult"
        class="resultTable"
        [nzScroll]="{ x: '100%', y: '160px' }"
      >
        <thead>
          <tr>
            <th nzWidth="50px" nzAlign="left"></th>
            <th nzWidth="100px" nzAlign="left">{{ 'dj-序号' | translate }}</th>
            <th nzWidth="100px" nzAlign="left">{{ 'dj-执行时间' | translate }}</th>
            <th nzWidth="200px" nzAlign="left">{{ 'dj-执行内容' | translate }}</th>
            <th nzWidth="150px" nzAlign="left">{{ 'dj-执行结果' | translate }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of records; let idx = index" nzAlign="left">
            <td>
              <ng-container *ngIf="data.code === '0'; else error">
                <i style="color: #00b042" nz-icon nzType="check-circle" nzTheme="fill"></i>
              </ng-container>
              <ng-template #error>
                <i style="color: #ff4d4f" nz-icon nzType="close-circle" nzTheme="fill"></i>
              </ng-template>
            </td>
            <td>{{ idx + 1 }}</td>
            <td nzEllipsis>
              <span nz-tooltip [nzTooltipTitle]="data.executionTime">
                {{ data.executionTime }}
              </span>
            </td>
            <td nzEllipsis>
              <span nz-tooltip [nzTooltipTitle]="data.returnSql" [nzTooltipOverlayStyle]="{ 'max-width': '800px' }">
                {{ data.returnSql }}
              </span>
            </td>
            <td nzEllipsis>
              <div nz-tooltip class="effects" [nzTooltipTitle]="data.rows">
                {{ data.rows }}
                <span class="ai-fix" *ngIf="data.code !== '0'" (click)="handleFix(data)">
                  <img alt="" class="img" src="/assets/img/ai-generate-avatar.png" />
                  <span class="text">{{ 'dj-AI除错' | translate }}</span>
                </span>
              </div>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </ng-container>

    <ng-template #noResult>
      <div class="empty-data">
        <img alt="" class="icon" src="assets/img/empty.png" />
        <div class="text">{{ 'dj-暂无数据' | translate }}</div>
      </div>
    </ng-template>
  </div>
</div>
