import { Component, OnInit, Input, TemplateRef, ViewChild, EventEmitter, Output } from '@angular/core';
import { DataViewService } from '../../service/data-view.service';
import { DataViewRequestService } from '../../service/data-view-request.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { TranslateService } from '@ngx-translate/core';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { TViewType } from '../../config/data-view.type';

@Component({
  selector: 'app-data-view-header',
  templateUrl: './data-view-header.component.html',
  styleUrls: ['./data-view-header.component.less'],
})
export class DataViewHeaderComponent implements OnInit {
  @Input() params: any; // 接口需要的参数
  @Input() headerCustomTemplate: TemplateRef<any> | null = null;
  @Output() view: EventEmitter<void> = new EventEmitter();

  // 左上角的发布按钮的句柄（之前的逻辑）
  @ViewChild('publishButton', { static: false }) publishButtonRef: any;

  constructor(
    public dataViewService: DataViewService,
    private dataViewRequestService: DataViewRequestService,
    private translateService: TranslateService,
    private athMessageService: NzMessageService,
    private athModalService: AdModalService,
  ) {}

  get isSQLDataView() {
    return this.dataViewService?.isSQLDataView;
  }

  get viewType(): TViewType {
    return this.dataViewService?.dataViewBase?.viewType ?? null;
  }

  get modelPublished(): boolean {
    return this.dataViewService?.model?.isPublished ?? false;
  }

  ngOnInit(): void {}

  async handleSave(isAfterSaveNeedPublish = false): Promise<void> {
    // if (this.viewType === 'searchView') {
    //   this.athModalService.confirm({
    //     nzTitle: this.translateService.instant('dj-视图有可能被界面关联使用，请谨慎修改'),
    //     nzClassName: 'searchview-save-tips',
    //     nzOkText: this.translateService.instant('dj-继续保存'),
    //     nzOnOk: () => {
    //       this.handleSaveAfter(isAfterSaveNeedPublish);
    //     },
    //     nzOnCancel: () => {},
    //   });
    // } else {
    this.handleSaveNext(isAfterSaveNeedPublish);
    // }
  }

  async handleSaveNext(isAfterSaveNeedPublish = false): Promise<void> {
    try {
      const dataViewBase = this.dataViewService.dataViewBase;
      if (this.dataViewService.isSQLDataView) {
        if (this.dataViewService.lastGenerateFieldSql !== dataViewBase.executeSql) {
          this.athModalService.info({
            nzTitle: this.translateService.instant('dj-SQL变化提示'),
          });
          return;
        }
      }
      this.dataViewService.setIsSaveLoading(true);
      const returnData = await this.checkBindRelation();
      this.dataViewService.setIsSaveLoading(false);
      if (returnData?.data) {
        this.athModalService.confirm({
          nzTitle: this.translateService.instant('dj-当前查询方案已被TBB报表绑定，是否继续？'),
          nzOkText: this.translateService.instant('dj-确定'),
          nzOnOk: () => {
            this.handleSaveAfter(isAfterSaveNeedPublish);
          },
          nzOnCancel: () => {},
        });
      } else {
        this.handleSaveAfter(isAfterSaveNeedPublish);
      }
    } catch (error) {
      this.dataViewService.setIsSaveLoading(false);
      this.athMessageService.error(error.message);
    }
  }

  async handleSaveAfter(isAfterSaveNeedPublish = false): Promise<void> {
    this.dataViewService.setIsSaveLoading(true);
    try {
      await this.handleSaveDataView();

      if (isAfterSaveNeedPublish) {
        this.publishButtonRef.startPublish();
      } else {
        this.athMessageService.success(this.translateService.instant('dj-保存成功'));
      }
    } catch (error) {
      // console.error(error);
    }

    this.dataViewService.updateDataViewOrigin();
    this.dataViewService.setIsSaveLoading(false);
  }

  handleSaveDataView(): Promise<any> {
    const saveInfo = this.dataViewService.getSaveDataView();
    /**
     * 将viewShowFields中的manage_status强制移动到数组最后一个
     */
    const manageStatusFullPath = `${this.dataViewService?.dataViewBase?.originalModelId}.manage_status`;
    const infoPc = saveInfo.viewShowFields?.field?.reduce(
      (prev, next) => {
        if (next.fullPath === manageStatusFullPath) {
          prev.target = next;
        } else {
          prev.others.push(next);
        }
        return prev;
      },
      { others: [], target: null },
    );
    const infoMobile = saveInfo.viewShowFieldsMobile?.field?.reduce(
      (prev, next) => {
        if (next.fullPath === manageStatusFullPath) {
          prev.target = next;
        } else {
          prev.others.push(next);
        }
        return prev;
      },
      { others: [], target: null },
    );
    if (infoPc.target) {
      saveInfo.viewShowFields.field = [...infoPc.others, infoPc.target];
    }
    if (infoMobile.target) {
      saveInfo.viewShowFieldsMobile.field = [...infoMobile.others, infoMobile.target];
    }
    return this.dataViewRequestService.saveDataView(saveInfo).toPromise();
  }

  // 检测是否被绑定
  checkBindRelation(): Promise<any> {
    const params = {
      type: 'view',
      code: this.dataViewService.dataViewCode,
      serviceCode: this.dataViewService.dataViewBase.productCode,
    };
    return this.dataViewRequestService.checkBindRelation(params).toPromise();
  }

  // handleClose() {}

  // 处理了用户点击了发布（延用之前的逻辑）
  handleClickPublicAction(): void {
    // 要走保存逻辑
    this.handleSave(true);
  }

  // 预览
  handleView() {
    this.view.emit();
  }
}
