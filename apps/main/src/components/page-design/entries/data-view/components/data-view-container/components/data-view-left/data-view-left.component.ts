import { Component, OnInit, ViewChild, TemplateRef, Input } from '@angular/core';
import { NzTreeNode } from 'ng-zorro-antd/core/tree';
import { DataViewService } from '../../../../service/data-view.service';
import { LeftTabType, TViewType } from '../../../../config/data-view.type';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { TranslateService } from '@ngx-translate/core';
import { NzTreeComponent } from 'ng-zorro-antd/tree';
import { DataViewRequestService } from '../../../../service/data-view-request.service';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-data-view-left',
  templateUrl: './data-view-left.component.html',
  styleUrls: ['./data-view-left.component.less'],
})
export class DataViewLeftComponent implements OnInit {
  isAddOperateModalVisible: boolean = false; //新增运算弹窗
  currentEditField: any = null; // 添加当前编辑字段属性
  @Input() pageFrom: string;
  @Input() leftCustomWholeTemplate: TemplateRef<any> | null = null;

  LeftTabType = LeftTabType;
  searchValue: any;
  searchResult: any = true;

  @ViewChild('treeComponent', { static: false }) nzTreeComponent!: NzTreeComponent;

  constructor(
    public dataViewService: DataViewService,
    private dataViewRequestService: DataViewRequestService,
    public athModalService: AdModalService,
    public translateService: TranslateService,
    private message: NzMessageService,
  ) {}

  get viewType(): TViewType {
    return this.dataViewService?.dataViewBase?.viewType ?? null;
  }

  ngOnInit(): void {}

  /**
   * 运算删除操作
   * @param e
   */
  handleOperateDelete(e, field) {
    e.stopPropagation();
    this.athModalService.confirm({
      nzTitle: this.translateService.instant('dj-是否确认删除？'),
      nzOkText: this.translateService.instant('dj-确定'),
      nzOnOk: () => {
        // 从运算字段列表中删除
        const operationField = this.dataViewService.operationField.filter((item) => item.data_name !== field.data_name);
        this.dataViewService.setOperationField(operationField);
        // 从显示字段中删除
        // const findIndex = this.dataViewService.viewShowFields.findIndex(
        //   (field) => field.field === e.node.origin.field.field
        // );
        // if (findIndex > -1) {
        //   this.dataViewService.viewShowFieldsDelete(findIndex);
        // }

        // 更新左侧树
        this.dataViewService.updateSourceTreeByLeftTabType(LeftTabType.operation);
        this.message.success(this.translateService.instant('dj-删除成功'));
      },
    });
  }

  /**
   * 运算编辑操作
   * @param e
   */
  handleOperateEdit(e, field) {
    e.stopPropagation();
    this.currentEditField = field;
    this.isAddOperateModalVisible = true;
  }
  handleOperateFieldSave(data) {
    const operationField = this.dataViewService.operationField;
    if (this.currentEditField) {
      // 编辑模式：替换原有数据
      const index = operationField.findIndex((item) => item.data_name === this.currentEditField.data_name);
      if (index > -1) {
        operationField[index] = data;
      }
    } else {
      // 新增模式：追加数据
      operationField.push(data);
    }
    this.dataViewService.setOperationField(operationField);
    this.dataViewService.updateSourceTreeByLeftTabType(LeftTabType.operation);
    this.isAddOperateModalVisible = false;
    this.currentEditField = null; // 清空编辑字段
  }
  toggleIsShow(): void {
    this.dataViewService.setLeftIsShow(!this.dataViewService.leftIsShow);
  }

  toggleTab(tabType: LeftTabType): void {
    this.searchValue = '';
    this.dataViewService.setLeftTabType(tabType);
    this.dataViewService.updateSourceTreeByLeftTabType();
  }
  //新增运算字段
  addOperateField(): void {
    this.currentEditField = null;
    this.isAddOperateModalVisible = true;
  }

  // 全选
  selectAll(): void {
    const isSelectAll = !this.dataViewService.sourceTreeIsAllSelect;
    if (!isSelectAll) {
      this.athModalService.confirm({
        nzTitle: `${this.translateService.instant('dj-确定要取消全选吗？')}`,
        nzOkText: this.translateService.instant('dj-确定'),
        nzOnOk: () => {
          this.nzTreeComponent.getTreeNodes().forEach((node: NzTreeNode) => {
            this.handleCheckBoxNode(node, isSelectAll);
          });
        },
        nzOnCancel: () => {},
      });
    } else {
      this.nzTreeComponent.getTreeNodes().forEach((node: NzTreeNode) => {
        this.handleCheckBoxNode(node, isSelectAll);
      });
    }
  }

  searchFunc = (node: any) => {
    if (node.title?.includes(this.searchValue)) return true;
    return false;
  };

  handleSearchValueChange($event: any): void {
    const { keys = [] } = $event;
    this.searchResult = keys.length > 0 || !this.searchValue;
  }

  // 点击tree的checkbox
  handleCheckBoxChange($event: any): void {
    this.handleCheckBoxNode($event.node);
  }

  handleCheckBoxNode(node: NzTreeNode, isParentChecked = null): void {
    if (node.children?.length > 0) {
      node.children.forEach((childNode) => {
        this.handleCheckBoxNode(childNode, isParentChecked ?? node.isChecked);
      });
    } else {
      const ignoreKeys = this.dataViewService.getDefaultCheckedFields();
      if (!ignoreKeys.includes(node.origin.key)) {
        if (isParentChecked === true || (isParentChecked === null && node.origin.checked)) {
          // 检查 data_name 是否重复
          const duplicateFields = this.dataViewService.viewShowFields.filter(
            (field) => field.data_name === node.origin.field.data_name && field.fullPath !== node.origin.field.fullPath,
          );

          if (duplicateFields.length > 0) {
            // 获取重复字段的 fullPath
            this.message.warning(
              this.translateService.instant(`dj-与fullPath重复`, { fullPath: duplicateFields[0]?.fullPath }),
            );
            // 取消本次选中操作
            node.isChecked = false;
            // 如果使用了 nzTreeNode 的 checked 属性，也需要设置
            if (node.origin) {
              node.origin.checked = false;
            }
            return;
          }
        }

        this.handleCheckBoxFields(node.origin.field, isParentChecked);
      }
    }
  }

  handleCheckBoxFields(nodeField, isParentChecked = null): void {
    const findIndex = this.dataViewService.viewShowFields.findIndex((field) => {
      return field.fullPath === nodeField.fullPath;
    });

    if (findIndex >= 0 && isParentChecked !== true) {
      this.dataViewService.viewShowFieldsDelete(findIndex);
    }

    if (findIndex < 0 && isParentChecked !== false) {
      this.dataViewService.viewShowFieldsInsert(nodeField, this.dataViewService.viewShowFields.length);
    }
  }

  // getTreeNodeKeyList(nodeList: NzTreeNode[]): string[] {
  //   const keyList = [];
  //   nodeList.forEach((node) => {
  //     keyList.push(node.key);
  //     if (node.children?.length > 0) {
  //       keyList.push(...this.getTreeNodeKeyList(node.children));
  //     }
  //   });
  //   return keyList;
  // }
}
