<div>
  <nz-tree
    class="field-tree"
    [ngClass]="{ 'no-ant-tree-switcher': dataViewService.leftTabType === LeftTabType.main }"
    #treeComponent
    nzHideUnMatched
    [nzData]="dataViewService.viewShowFields"
    [nzSearchValue]="searchValue"
    [nzTreeTemplate]="nzTreeTemplate"
    [nzSearchFunc]="searchFunc"
    [nzShowExpand]="false"
  >
  </nz-tree>

  <ng-template #nzTreeTemplate let-node>
    <div class="tree-node" *ngIf="node.children?.length > 0 || node.isChecked; else nodeElseBlock">
      <span class="node-content"
        ><i adIcon iconfont="iconguanlianziduan" *ngIf="node?.origin?.isJoinField" class="iconfont" aria-hidden="true">
        </i>
        {{ node?.origin?.data_name }}
        <span class="comment">
          {{ node?.origin?.lang?.description?.[('dj-LANG'|translate)] ?? node.origin?.description }}
        </span>
      </span>
    </div>
    <ng-template #nodeElseBlock>
      <div class="tree-node">
        <span class="node-content">
          <i adIcon iconfont="iconguanlianziduan" *ngIf="node?.origin?.isJoinField" class="iconfont" aria-hidden="true">
          </i>
          {{ node.origin?.data_name }}
          <span class="comment">
            {{ node.origin?.lang?.description?.[('dj-LANG'|translate)] ?? node.origin.description }}
          </span>
        </span>
      </div>
    </ng-template>
  </ng-template>
  <ad-empty *ngIf="!dataViewService.viewShowFields?.length" [nzNotFoundContent]="contentTpl">
    <ng-template #contentTpl>
      <span>{{ 'dj-暂无数据' | translate }}</span>
    </ng-template>
  </ad-empty>
</div>
