import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { DataViewService } from '../../../../service/data-view.service';
import { ComponentType, TableField } from '../../../../config/data-view.type';
import { cloneDeep } from 'lodash';
import { TranslateService } from '@ngx-translate/core';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { CdkDragDrop, moveItemInArray, CdkDrag } from '@angular/cdk/drag-drop';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-sql-fields-edit-modal',
  templateUrl: './sql-fields-edit-modal.component.html',
  styleUrls: ['./sql-fields-edit-modal.component.less'],
})
export class SqlFieldsEditModalComponent implements OnInit, OnChanges {
  @Input() visible: boolean;

  @Output() cancel: EventEmitter<void> = new EventEmitter();
  @Output() confirm: EventEmitter<TableField[]> = new EventEmitter();

  get disabledKeys() {
    return this.dataViewService.getDefaultFields();
  }

  /**
   * 当前编辑的行索引
   */
  public editName: string | undefined = undefined;

  public sourceTree: TableField[] = [];

  constructor(
    public dataViewService: DataViewService,
    private translate: TranslateService,
    private modal: AdModalService,
    private message: NzMessageService,
  ) {}

  // 组件类型
  private readonly componentTypeList = [
    {
      type: ComponentType.DATEPICKER,
      label: this.translate.instant('dj-日期'),
    },
    {
      type: ComponentType.TIMEPICKER,
      label: this.translate.instant('dj-时间'),
    },
    {
      type: ComponentType.PERCENT_INPUT,
      label: this.translate.instant('dj-百分比'),
    },
    {
      type: ComponentType.INPUT,
      label: this.translate.instant('dj-文本'),
    },
    // {
    //   type: ComponentType.AMOUNT_INPUT,
    //   label: this.translate.instant('dj-金额'),
    // },
    // {
    //   type: ComponentType.MEASURE,
    //   label: this.translate.instant('dj-计量'),
    // },
    {
      type: ComponentType.SELECT,
      label: this.translate.instant('dj-选择框'),
    },
    {
      type: ComponentType.EOC_SELECT,
      label: this.translate.instant('dj-营运单元'),
    },
    {
      type: ComponentType.NAME_CODE_COMPONENT,
      label: this.translate.instant('dj-内含外显'),
    },
    {
      type: ComponentType.NEW_OLD_COMPONENT,
      label: this.translate.instant('dj-新旧值'),
    },
    {
      type: ComponentType.WORKFLOW_PROGRESS,
      label: this.translate.instant('dj-DwWorkFlowProgress'), // 签核历程（借用已存在的多语言）
    },
    {
      type: ComponentType.ATH_TAG,
      label: this.translate.instant('dj-标签类控件'),
    },
    {
      type: ComponentType.FILE_UPLOAD,
      label: this.translate.instant('dj-附件类型'),
    },
  ];

  get showComponentTypeList() {
    const sourceField = this.dataViewService?.allFields?.find((field) => {
      return field.fullPath === this.dataViewService?.currentSelectField?.fullPath;
    });
    const showSignOff = sourceField?.showSignOff;

    return showSignOff
      ? this.componentTypeList
      : this.componentTypeList.filter((item) => item.type !== ComponentType.WORKFLOW_PROGRESS);
  }

  get componentMap() {
    return this.showComponentTypeList.reduce((pre, curr) => {
      pre[curr.type] = curr.label;
      return pre;
    }, {});
  }

  ngOnInit() {}

  ngOnChanges(changes: SimpleChanges): void {
    if (Reflect.has(changes, 'visible')) {
      if (changes.visible.currentValue !== changes.visible.previousValue) {
        if (changes.visible.currentValue) {
          this.sourceTree = cloneDeep(this.dataViewService.viewShowFields);
        } else {
          this.sourceTree = [];
        }
      }
    }
  }

  public sortPredicate = (index: number, item: CdkDrag<TableField>) => {
    const kFixField = 'manage_status';
    if (item.data.data_name === kFixField) return false;
    if (this.sourceTree[index].data_name === kFixField) return false;
    return true;
  };

  public handleDelete(obj: TableField) {
    this.modal.confirm({
      nzTitle: this.translate.instant('dj-确认删除？'),
      nzOkText: this.translate.instant('dj-删除'),
      nzOnOk: () => {
        const findIndex = this.sourceTree.findIndex((field) => {
          return field.fullPath === obj.fullPath;
        });
        if (findIndex >= 0) {
          this.sourceTree.splice(findIndex, 1);
        }
      },
      nzCancelText: this.translate.instant('dj-取消'),
      nzOnCancel: () => {},
    });
  }

  public handleDrop(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.sourceTree, event.previousIndex, event.currentIndex);
  }

  public handlePatchDescription(obj: TableField, key: string, value: any) {
    obj[key] = value.value;
    obj.lang[key] = value.lang;
  }

  /**
   * 取消
   */
  public handleCancel(): void {
    this.cancel.emit();
  }

  /**
   * 确认
   */
  public handleOK(): void {
    const hasError = this.sourceTree.some((e) => {
      if (!e.description || !e.lang?.description) return true;
      if (!e.field_dsl?.type) return true;
      return false;
    });
    if (!hasError) {
      this.confirm.emit(this.sourceTree);
    } else {
      this.message.error(this.translate.instant('dj-请完善字段配置'));
    }
  }
}
