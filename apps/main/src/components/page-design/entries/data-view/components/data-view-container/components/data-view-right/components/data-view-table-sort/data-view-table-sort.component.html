<div class="data-view-sort-label" *ngIf="showHeader">
  {{ 'dj-数据排序' | translate }}
  <i
    nz-tooltip
    adIcon
    iconfont="iconexplain"
    class="iconfont"
    aria-hidden="true"
    [nzTooltipTitle]="'dj-运行时页面初次载入数据的默认展示排序' | translate"
  >
  </i>
</div>
<div class="data-view-sort-list">
  <div class="data-view-sort-list-item" *ngFor="let item of orderList; let i = index">
    <span
      class="title"
      nz-tooltip
      [nzTooltipTitle]="
        getShowName(item) + ':' + (item.orderType === 'asc' ? ('dj-升序' | translate) : ('dj-降序' | translate))
      "
    >
      <span class="key">{{ getShowName(item) }}</span
      ><span class="value">:{{ item.orderType === 'asc' ? ('dj-升序' | translate) : ('dj-降序' | translate) }}</span>
    </span>
    <span class="delete" (click)="removeCondition(i)">
      <i adIcon iconfont="icondanchuxiaoxiguanbi" class="iconfont" aria-hidden="true"> </i>
    </span>
  </div>
</div>
<div class="data-view-sort-add" (click)="openModal()">{{ 'dj-排序管理' | translate }}</div>

<app-data-view-table-sort-content
  (close)="showModal = false"
  (submit)="handleSubmit($event)"
  [showModal]="showModal"
  [orderList]="orderList"
  [allFields]="allFields"
></app-data-view-table-sort-content>
