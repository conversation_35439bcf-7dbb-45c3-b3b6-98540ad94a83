import { Component, OnInit, ViewChild } from '@angular/core';
import { DataViewService } from '../../service/data-view.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { TranslateService } from '@ngx-translate/core';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { DataViewRequestService } from 'components/page-design/entries/data-view/service/data-view-request.service';
import { SqlViewEditorComponent } from './components/sql-view-editor/sql-view-editor.component';

@Component({
  selector: 'app-data-view-sql-container',
  templateUrl: './data-view-sql-container.component.html',
  styleUrls: ['./data-view-sql-container.component.less'],
})
export class DataViewSqlContainerComponent implements OnInit {
  @ViewChild('sqlEditor', { static: false }) sqlEditor!: SqlViewEditorComponent;

  constructor(
    public dataViewService: DataViewService,
    private dataviewRequest: DataViewRequestService,
    private message: NzMessageService,
    private translate: TranslateService,
    private modal: AdModalService,
  ) {}

  private get serviceCode() {
    return this.dataViewService.dataViewBase?.productCode;
  }

  private abortController: AbortController | undefined;

  ngOnInit() {}

  public getSqlWithCursor = async () => {
    const sql = (await this.sqlEditor.getWaitInvokeSql('selection')) || (await this.sqlEditor.getWaitInvokeSql('all'));
    return sql;
  };

  /**
   * 终止执行
   * @returns
   */
  public abortRequest() {
    if (!this.abortController) {
      this.message.error(this.translate.instant('dj-没有执行中的查询'));
      return;
    }
    this.modal.confirm({
      nzTitle: this.translate.instant('dj-是否确认终止'),
      nzOkText: this.translate.instant('dj-确定'),
      nzOnOk: () => {
        this.abortController?.abort();
        this.abortController = undefined;
      },
      nzCancelText: this.translate.instant('dj-取消'),
      nzOnCancel: () => {},
    });
  }

  /**
   * 执行sql
   * @param sql
   */
  public async executeSql(sql: string) {
    try {
      await this.parseSqlToHeader(sql);
      this.dataViewService.setResultLoading(true);
      this.abortController = new AbortController();
      const result: any = await this.dataviewRequest
        .executeSql(
          {
            serviceCode: this.serviceCode,
            masterTableName: 'aliasOrMasterTableName',
            appCode: this.dataViewService.dataViewBase.application,
            executeSql: sql.endsWith(';') ? sql.substring(0, sql.length - 1) : sql,
          },
          this.abortController.signal,
        )
        .toPromise();
      if (result.code === 0) {
        const { dataList, record } = result.data;
        this.dataViewService.setSqlResults(dataList);
        this.dataViewService.setExecuteResults(record);
      }
    } finally {
      this.abortController = undefined;
      this.dataViewService.setResultLoading(false);
    }
  }

  /**
   * sql中解析出表头
   * @param sql
   */
  private async parseSqlToHeader(sql: string): Promise<void> {
    try {
      const result: any = await this.dataviewRequest
        .parseSqlToHeader({
          serviceCode: this.serviceCode,
          executeSql: sql,
        })
        .toPromise();
      const dataList = result?.data || [];
      this.dataViewService.setResultHeader(dataList);
    } catch {
      this.dataViewService.setResultHeader([]);
    }
  }
}
