import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { DataViewService } from '../../../../service/data-view.service';
import { TranslateService } from '@ngx-translate/core';
import { NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import { DataViewRequestService } from 'components/page-design/entries/data-view/service/data-view-request.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { SqlFieldsComponent } from '../sql-fields/sql-fields.component';
import { BaseField, TableField } from 'components/page-design/entries/data-view/config/data-view.type';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { sourceFiledToTreeNodeList } from 'components/page-design/entries/data-view/utils/tools';

@Component({
  selector: 'app-sql-view-field-panel',
  templateUrl: './sql-view-field-panel.component.html',
  styleUrls: ['./sql-view-field-panel.component.less'],
})
export class SqlViewFieldPanelComponent implements OnInit {
  @Input() getSqlWithCursor: () => Promise<string | undefined>;

  @ViewChild('sqlField', { static: false }) sqlField!: SqlFieldsComponent;

  public loading: boolean = false;

  private get serviceCode() {
    return this.dataViewService.dataViewBase?.productCode;
  }

  constructor(
    public dataViewService: DataViewService,
    private dataviewRequest: DataViewRequestService,
    private translate: TranslateService,
    private message: NzMessageService,
    private modal: AdModalService,
  ) {}

  public searchValue: string = '';

  /**
   * 批量编辑字段的modal显示
   */
  public batchModalVisible: boolean = false;

  ngOnInit() {}

  public toggleIsShow() {
    this.dataViewService.setRightIsShow(!this.dataViewService.rightIsShow);
  }

  public handleTableSortChange(e: any): void {
    this.dataViewService.setOrderList(e);
  }

  public handleDataViewFilterChange(e: any): void {
    this.dataViewService.setConditionList(e.conditionList);
    this.dataViewService.setQueryConditions(e.queryConditions);
  }

  public async generateDataView(e: MouseEvent) {
    e.stopPropagation();
    const sql = await this.getSqlWithCursor();
    if (!sql) {
      return;
    }
    try {
      this.loading = true;
      const fields = await this.parseSqlToHeader(this.dataViewService.dataViewBase?.executeSql);
      if (fields?.length) {
        this.modal.confirm({
          nzTitle: this.translate.instant('dj-是否重新生成查询方案字段'),
          nzOkText: this.translate.instant('dj-确定'),
          nzOnOk: () => {
            this.overrideShowFields(fields);
            this.dataViewService.setLastGenerateFieldSql(sql);
          },
          nzCancelText: this.translate.instant('dj-取消'),
          nzOnCancel: () => {},
        });
      } else {
        this.message.error(this.translate.instant('dj-生成查询方案字段失败'));
      }
    } finally {
      this.loading = false;
    }
  }

  public batchEditField(e: MouseEvent) {
    e.stopPropagation();
    this.batchModalVisible = true;
  }

  public handleModalCancel() {
    this.batchModalVisible = false;
  }

  public handleModalConfirm(data: TableField[]) {
    this.dataViewService.setViewShowFields(data);
    this.handleModalCancel();
  }

  /**
   * 覆盖查询字段
   * @param fields
   */
  private overrideShowFields(fields: BaseField[]) {
    this.dataViewService.setViewShowFieldsForSQL(fields);
    fields.forEach((item) => this.handleCheckBoxNode(item, true));
    this.dataViewService.viewShowFields.sort((_, next) => {
      if (next.data_name === 'manage_status') return -1;
      return 0;
    });
    this.message.success(this.translate.instant('dj-操作成功'));
  }

  private handleCheckBoxNode(item: BaseField, isParentChecked = null) {
    const ignoreKeys = this.dataViewService.getDefaultCheckedFields();
    if (!ignoreKeys.includes(item.fullPath)) {
      if (isParentChecked === true || isParentChecked === null) {
        // 检查 data_name 是否重复
        const duplicateFields = this.dataViewService.viewShowFields.filter(
          (field) => field.data_name === item.data_name && field.fullPath !== item.fullPath,
        );

        if (duplicateFields.length > 0) {
          this.message.warning(this.translate.instant(`dj-与fullPath重复`, { fullPath: duplicateFields[0]?.fullPath }));
          return;
        }
      }

      this.handleCheckBoxFields(item, isParentChecked);
    }
  }

  private handleCheckBoxFields(nodeField: BaseField, isParentChecked = null): void {
    const findIndex = this.dataViewService.viewShowFields.findIndex((field) => {
      return field.fullPath === nodeField.fullPath;
    });

    if (findIndex >= 0 && isParentChecked !== true) {
      this.dataViewService.viewShowFieldsDelete(findIndex);
    }

    if (findIndex < 0 && isParentChecked !== false) {
      this.dataViewService.viewShowFieldsInsert(nodeField, this.dataViewService.viewShowFields.length);
    }
  }

  /**
   * sql中解析出表头
   * @param sql
   */
  private async parseSqlToHeader(sql: string): Promise<BaseField[]> {
    try {
      const result: any = await this.dataviewRequest
        .parseSqlToHeader({
          serviceCode: this.serviceCode,
          executeSql: sql,
        })
        .toPromise();
      const list = result?.data || [];
      if (list.length) {
        const descriptions = list.map((e) => e.description);
        const languages: any[] = await Promise.all(
          descriptions.map((str) =>
            this.dataviewRequest
              .translate({
                content: str,
                convertTypes: ['zh2Hant', 'zh2Hans'],
              })
              .toPromise(),
          ),
        );
        list.forEach((item: any, i: number) => {
          item.lang = {
            description: {
              en_US: languages[i]?.data?.zh2Hans,
              zh_CN: languages[i]?.data?.zh2Hans,
              zh_TW: languages[i]?.data?.zh2Hant,
            },
          };
        });
      }
      return list;
    } catch {
      return [];
    }
  }
}
