import { Component, Input, OnInit } from '@angular/core';
import { IExecuteResult } from 'components/page-design/entries/data-view/config/data-view.type';
import { DataViewService } from 'components/page-design/entries/data-view/service/data-view.service';
import { DataViewRequestService } from 'components/page-design/entries/data-view/service/data-view-request.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { TranslateService } from '@ngx-translate/core';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';

@Component({
  selector: 'app-sql-execute',
  templateUrl: './sql-execute.component.html',
  styleUrls: ['./sql-execute.component.less'],
})
export class SqlExecuteComponent implements OnInit {
  public mode: 'query' | 'invoke' = 'query';

  get fields() {
    return this.dataViewService.resultHeader || [];
  }

  get results() {
    return this.dataViewService.sqlResults || [];
  }

  get records() {
    return this.dataViewService.executeResults || [];
  }

  get loading() {
    return this.dataViewService.resultLoading;
  }

  constructor(
    private dataViewService: DataViewService,
    private message: NzMessageService,
    private translate: TranslateService,
    private modal: AdModalService,
    private dataviewRequest: DataViewRequestService,
  ) {}

  ngOnInit() {}

  public async handleFix(item: IExecuteResult) {
    try {
      const result: any = await this.dataviewRequest.checkSqlByAi(item.returnSql).toPromise();
      if ('SQL语句校验通过' === result?.data?.result) {
        this.message.success(this.translate.instant('dj-SQL语句校验通过'));
      } else {
        this.modal.info({
          nzTitle: this.translate.instant('dj-检查结果'),
          nzContent: result.data?.explain,
        });
      }
    } catch (e) {
      this.message.error(e.message || e);
    }
  }
}
