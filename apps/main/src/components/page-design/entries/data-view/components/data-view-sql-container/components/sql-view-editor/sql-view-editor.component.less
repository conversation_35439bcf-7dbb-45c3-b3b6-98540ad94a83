.code-wraper {
  width: 100%;
  height: 100%;
  padding: 12px;
  display: flex;
  flex-direction: column;
  .header {
    display: flex;
    align-items: center;
    .offset-r {
      margin-right: 8px;
    }
    .group {
      background-color: #ffffff;
      border-radius: 3px;
      padding: 4px 6px;
      box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.05), 0px 4px 5px 0px rgba(0, 0, 0, 0.08),
        0px 2px 4px -1px rgba(0, 0, 0, 0.12);
      display: inline-flex;
      align-items: center;

      .button {
        cursor: pointer;
        min-width: 28px;
        height: 28px;
        border-radius: 4px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        .icon {
          font-size: 16px;
          color: #605ce5;
        }
        &.round {
          background: #f7f7fa;
        }
        .img {
          width: 16px;
          height: auto;
        }
        .text {
          font-size: 12px;
          line-height: 16px;
          text-align: center;
          letter-spacing: normal;
          background: linear-gradient(90deg, #5050e3 0%, #d91ad9 71%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-fill-color: transparent;
          margin-left: 6px;
        }
      }

      .arrow {
        font-size: 18px;
        padding: 2px;
        color: #605ce5;
      }
    }
  }
  .sql-monaco-editor {
    display: flex;
    flex: 1;
    margin-top: 10px;
    ::ng-deep {
      .antd-app {
        width: 100%;
      }
    }
  }
}
::ng-deep {
  .invoke-menu {
    display: flex;
    display: flex;
    align-items: flex-start;
    padding: 6px 8px;
    flex-direction: column;
    .item {
      width: 100%;
      font-size: 14px;
      line-height: 20px;
      color: #1d1c33;
    }
  }
  .ant-popover-inner-content {
    &:has(.pop-content) {
      padding-top: 0;
    }
  }
}

.pop-title {
  font-size: 13px;
  color: #1d1c33;
}
.pop-content {
  width: 500px;

  .tab-content {
    margin-top: 12px;
    font-size: 13px;
    color: #1d1c33;
    word-break: break-word;
    white-space: break-spaces;
  }
}
