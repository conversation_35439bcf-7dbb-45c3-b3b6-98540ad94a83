import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { NzTreeNode } from 'ng-zorro-antd/core/tree';
import { DataViewService } from '../../../../service/data-view.service';
import { LeftTabType, TViewType } from '../../../../config/data-view.type';
import { NzTreeComponent } from 'ng-zorro-antd/tree';
import { NzMessageService } from 'ng-zorro-antd/message';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-sql-fields',
  templateUrl: './sql-fields.component.html',
  styleUrls: ['./sql-fields.component.less'],
})
export class SqlFieldsComponent implements OnInit {
  @Input() searchValue: string;

  @ViewChild('treeComponent', { static: false }) nzTreeComponent!: NzTreeComponent;

  LeftTabType = LeftTabType;

  constructor(
    public dataViewService: DataViewService,
    private message: NzMessageService,
    private translate: TranslateService,
  ) {}

  ngOnInit() {}

  public searchFunc = (node: any) => {
    if (node?.data_name?.includes(this.searchValue)) return true;
    return false;
  };
}
