<div class="code-wraper">
  <div class="header">
    <div class="group offset-r">
      <span
        class="button round offset-r"
        nz-tooltip
        [nzTooltipTitle]="(!wordWrap ? 'dj-自动换行' : 'dj-无自动换行') | translate"
        (click)="handleWordWrap()"
      >
        <i adIcon class="icon" [iconfont]="!wordWrap ? 'iconhuanhang-xian' : 'iconquxiaozidonghuanhang-xian'"></i>
      </span>
      <span class="button round offset-r" nz-tooltip [nzTooltipTitle]="'dj-复制' | translate" (click)="handleCopy()">
        <i adIcon class="icon" iconfont="iconfuzhi-xian"></i>
      </span>
      <span class="button round offset-r" nz-tooltip [nzTooltipTitle]="'dj-清空' | translate" (click)="handleClear()">
        <i adIcon class="icon" iconfont="iconsaozhou-xian"></i>
      </span>
      <!-- <span class="button round offset-r" style="padding: 0 6px" (click)="handleAiNew()">
        <img alt="" class="img" src="/assets/img/ai-generate-avatar.png" />
        <span class="text">{{ 'dj-AI生成' | translate }}</span>
      </span> -->
      <span class="button round" style="padding: 0 6px" (click)="handleAiCheck()">
        <img alt="" class="img" src="/assets/img/ai-generate-avatar.png" />
        <span class="text">{{ 'dj-AI检查' | translate }}</span>
      </span>
    </div>
    <div class="group offset-r">
      <span
        class="button round offset-r"
        nz-tooltip
        [nzTooltipTitle]="'dj-重新从后端导入表' | translate"
        (click)="handleReImport()"
      >
        <i adIcon class="icon" iconfont="icondaorubiaoge-xian"></i>
      </span>
      <span class="button round offset-r" nz-tooltip [nzTooltipTitle]="'dj-终止' | translate" (click)="handleAbort()">
        <i adIcon class="icon" iconfont="iconjinyong-xian"></i>
      </span>
      <span
        class="button round"
        nz-dropdown
        [nzVisible]="nzVisible"
        nz-tooltip
        [nzTooltipTitle]="'dj-运行' | translate"
        (nzVisibleChange)="nzVisible = $event"
        [nzDropdownMenu]="menu"
        nzPlacement="bottomLeft"
      >
        <i adIcon class="icon" style="transform: scale(1.1)" iconfont="iconbofang-xian"></i>
        <nz-dropdown-menu #menu="nzDropdownMenu">
          <ul nz-menu class="invoke-menu">
            <li nz-menu-item class="item" (click)="handleRun('all')">{{ 'dj-运行全部' | translate }}</li>
            <li nz-menu-item class="item" (click)="handleRun('selection')">{{ 'dj-运行当前语句' | translate }}</li>
          </ul>
        </nz-dropdown-menu>
      </span>
      <i adIcon iconfont="iconjiantouxia-mian" (click)="nzVisible = true" class="arrow"></i>
    </div>
    <div class="group">
      <span
        class="button round"
        nz-popover
        trigger="click"
        [nzPopoverTitle]="null"
        nzPopoverPlacement="bottom"
        [nzPopoverContent]="contentTemplate"
      >
        <i adIcon class="icon" iconfont="iconjuli-xian"></i>
      </span>
      <ng-template #contentTemplate>
        <div class="pop-content">
          <nz-tabset class="tab-status-content">
            <nz-tab [nzTitle]="item.title" *ngFor="let item of demoConfig">
              <div class="tab-content">{{ item.content }}</div>
            </nz-tab>
          </nz-tabset>
        </div>
      </ng-template>
    </div>
  </div>
  <div class="sql-monaco-editor" #sqlEditor></div>
</div>
