<div class="data-view-header">
  <span class="title" *ngIf="!headerCustomTemplate">{{ 'dj-浏览视图设计' | translate }}</span>
  <div class="left">
    <span *ngIf="!headerCustomTemplate">{{ 'dj-数据视图名称' | translate }}</span>
    <ng-template [ngTemplateOutlet]="headerCustomTemplate"></ng-template>
  </div>

  <div class="right">
    <button
      *ngIf="!isSQLDataView"
      ad-button
      nz-tooltip
      [nzTooltipTitle]="modelPublished ? '' : ('dj-请先发布模型' | translate)"
      [disabled]="!modelPublished"
      adType="default"
      (click)="handleView()"
      style="margin-right: 10px"
    >
      {{ 'dj-预览' | translate }}
    </button>
    <ng-container *operateAuth="{ prefix: 'update' }">
      <app-module-publish-button
        #publishButton
        [size]="'default'"
        [module]="'dataView'"
        [pkValue]="dataViewService.dataViewCode"
        [needTenant]="true"
        [needSave]="true"
        (clickPublicAction)="handleClickPublicAction()"
        (publicAction)="dataViewService.setIsPublishLoading($event)"
        [classes]="'ant-btn-primary-border'"
      ></app-module-publish-button>

      <button ad-button adType="primary" style="margin-right: 10px" (click)="handleSave()">
        {{ 'dj-保存' | translate }}
      </button>
    </ng-container>
    <!-- <i
        adIcon
        iconfont="icondanchuxiaoxiguanbi"
        aria-hidden="true"
        class="close-icon iconfont"
        (click)="handleClose()"
        *ngIf="!headerCustomTemplate"
      ></i> -->
  </div>
</div>
