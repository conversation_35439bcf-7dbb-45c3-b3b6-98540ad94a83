import { Component, OnInit, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { ComponentType } from '../../../../../../config/data-view.type';

@Component({
  selector: 'app-data-view-field-column-edit',
  templateUrl: './data-view-field-column-edit.component.html',
  styleUrls: ['./data-view-field-column-edit.component.less'],
})
export class DataViewFieldColumnEditComponent implements OnInit, OnChanges {
  @Input() type = '';
  @Input() typeList = [];
  @Input() allFields = [];
  @Input() relateField = ''; // 关联字段的 fullpath
  @Input() tagType = ''; // 标签控件的类型
  @Output() fieldColumnEditTypeChange = new EventEmitter<any>();
  @Output() fieldColumnEditRelateFieldChange = new EventEmitter<any>();
  @Output() editFieldTagType = new EventEmitter<any>();

  tagOptions = [
    { value: 'face', label: 'dj-面性标签' },
    { value: 'line', label: 'dj-线性标签' },
    { value: 'color', label: 'dj-深色标签' },
  ];
  isShowRelateField = false; // 展示关联字段
  isShowTagType = false; // 展示标签类型

  constructor() {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('type')) {
      this.updateIsShowRelateField();
    }
  }

  updateIsShowRelateField(): void {
    this.isShowRelateField = (
      [ComponentType.NAME_CODE_COMPONENT, ComponentType.NEW_OLD_COMPONENT] as string[]
    ).includes(this.type);
    if (!this.isShowRelateField) {
      this.fieldColumnEditRelateFieldChange.emit(undefined);
    }
    this.isShowTagType = ([ComponentType.ATH_TAG] as string[]).includes(this.type);
    if (!this.isShowTagType) {
      this.editFieldTagType.emit(undefined);
    } else if (!this.tagType) {
      // 当无值时赋默认值
      this.editFieldTagType.emit('face');
    }
  }

  changeValue(e: any): void {
    this.fieldColumnEditTypeChange.emit(e);
  }

  // 调整标签类型
  changeTagType(e: any): void {
    this.editFieldTagType.emit(e);
  }

  changeRelateFieldValue(e: any): void {
    const field = this.allFields.find((item) => item.fullPath === e);
    this.fieldColumnEditRelateFieldChange.emit(field);
  }
}
