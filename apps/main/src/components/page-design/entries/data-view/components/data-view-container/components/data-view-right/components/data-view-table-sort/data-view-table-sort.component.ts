import { Component, EventEmitter, OnInit, Input, Output } from '@angular/core';
import { OrderItem, SourceField } from '../../../../../../config/data-view.type';
import { cloneDeep } from 'lodash';

@Component({
  selector: 'app-data-view-table-sort',
  templateUrl: './data-view-table-sort.component.html',
  styleUrls: ['./data-view-table-sort.component.less'],
})
export class DataViewTableSortComponent implements OnInit {
  @Input() showHeader: boolean = true;
  @Input() orderList: OrderItem[] = []; // 排序list
  @Input() allFields: SourceField[] = []; // 数据源字段
  @Input() currentLang: string = 'zh_CN'; // 当前的语言环境
  @Output() tableSortChange = new EventEmitter<OrderItem[]>();

  showModal = false;

  constructor() {}

  ngOnInit(): void {}

  openModal() {
    this.showModal = true;
  }

  handleSubmit(e: any): void {
    this.showModal = false;
    const orderListOut = e.map((item, index) => {
      const fullPathList = item.fullPath.split('.');
      const schema = fullPathList.splice(-1)[0];
      const path = fullPathList.join('.');
      return {
        schema,
        table_path: path,
        orderType: item.orderType,
        order: index,
      };
    });

    this.tableSortChange.emit(orderListOut);
  }

  getShowName(order: OrderItem): string {
    const fullPath = order.table_path ? order.table_path + '.' + order.schema : order.schema;
    const field = this.allFields.find((item) => item.fullPath === fullPath);
    const showName = field?.lang?.description?.[this.currentLang] ?? field?.description ?? '';
    return `${showName}(${fullPath})`;
  }

  /**
   * 删除当前排序条件
   *
   * @param key { number } 排序条件数据对象
   */
  removeCondition(key: number): void {
    const list = cloneDeep(this.orderList);
    list.splice(key, 1);
    this.tableSortChange.emit(list);
  }
}
