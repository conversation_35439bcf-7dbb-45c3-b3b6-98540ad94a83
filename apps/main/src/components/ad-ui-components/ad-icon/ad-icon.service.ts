import { DOCUMENT } from '@angular/common';
import { HttpBackend } from '@angular/common/http';
import { Inject, Injectable, Optional, RendererFactory2 } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';

import { IconDefinition } from '@ant-design/icons-angular';
import { NzConfigService } from 'ng-zorro-antd/core/config';
import { NzSafeAny } from 'ng-zorro-antd/core/types';
import { NzIconService, NZ_ICONS } from 'ng-zorro-antd/icon';
import { ATH_ICONFONT_URL } from './icon.token';
import { getEnvParamsScheduleDomain } from '@env';

@Injectable({
  providedIn: 'root',
})
export class AdIconService extends NzIconService {
  constructor(
    @Optional()
    @Inject(ATH_ICONFONT_URL)
    iconfontUrl: string,
    rendererFactory: RendererFactory2,
    sanitizer: DomSanitizer,
    protected nzConfigService: NzConfigService,
    @Optional() handler: HttpBackend,
    @Optional() @Inject(DOCUMENT) _document: NzSafeAny,
    @Optional() @Inject(NZ_ICONS) icons?: IconDefinition[],
  ) {
    super(rendererFactory, sanitizer, nzConfigService, handler, _document, icons);
    this.fetchFromIconfont({
      scriptUrl: `${getEnvParamsScheduleDomain()}/scheduler/static/assets/iconfont/iconfont.js`,
    });
  }
}
