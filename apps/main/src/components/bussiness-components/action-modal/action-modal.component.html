<!--action列表开窗-->
<ad-modal
  nzClassName="app-action-list-modal"
  [nzWidth]="'800px'"
  [nzTitle]="'dj-选择action' | translate"
  [(nzVisible)]="transferModal"
  [nzFooter]="null"
  [nzClosable]="true"
  [nzMaskClosable]="false"
  (nzOnCancel)="handleCloseSelect()"
>
  <ng-container *adModalContent>
    <ad-tabs
      class="action-tabs"
      [ngClass]="{ 'is-hide': isDistribution }"
      style="margin-bottom: 20px"
      [navStyle]="'default'"
      [(nzSelectedIndex)]="selectedIndex"
      (nzSelectedIndexChange)="handleSelectedIndexChange($event)"
    >
      <ad-tab *ngFor="let tab of tabs" [nzTitle]="tab.label | translate"></ad-tab>
    </ad-tabs>
    <div nz-row [nzGutter]="24">
      <div nz-col [nzSpan]="12" class="actionType" *ngIf="showActionType()">
        <span>
          {{ 'dj-action类型' | translate }}
        </span>
        <ad-select
          [nzPlaceHolder]="'dj-请选择' | translate"
          [(ngModel)]="labelType"
          (ngModelChange)="handleChangeType($event)"
        >
          <ng-container *ngFor="let data of actionTypeData">
            <ad-option [nzValue]="data.labelType" [nzLabel]="data.labelName"></ad-option>
          </ng-container>
        </ad-select>
      </div>
      <div nz-col [nzSpan]="12" class="searchAction">
        <nz-input-group class="search-input" [nzSuffix]="suffixIconSearch">
          <input
            type="text"
            nz-input
            [placeholder]="'dj-请输入关键字' | translate"
            [(ngModel)]="searchValue"
            (keyup.enter)="handleSearchAction()"
          />
        </nz-input-group>
        <ng-template #suffixIconSearch>
          <i adIcon type="search" (click)="handleSearchAction()"></i>
        </ng-template>
      </div>
    </div>
    <div class="attr-table">
      <ad-table
        style="width: 100%; height: 400px"
        [headerHeight]="40"
        [loading]="loading"
        [rowData]="allActions"
        [columnDefs]="columnDefs"
        [frameworkComponents]="[]"
        [rowCheckbox]="true"
        [rowSelection]="'single'"
        checkboxBindField="checked"
        [rowBuffer]="20"
        [pagination]="true"
        [frontPagination]="false"
        [showPaginationTotal]="true"
        [total]="actionTotal"
        [pageNumber]="actionPageNum"
        [pageSize]="actionPageSize"
        [showSizeChanger]="true"
        (rowSelected)="handleRowSelected($event)"
        (paginationChanged)="handlePaginationChanged($event)"
        (firstDataRendered)="onFirstDataRendered($event)"
      >
      </ad-table>
      <!-- <div class="page-number" *ngIf="allActions?.length > 0">100 {{ 'dj-条/页' | translate }}</div> -->
    </div>
    <div *ngIf="allActions?.length > 0 && !selectedActionId" class="red-tip">
      {{ 'dj-请选择一项数据' | translate }}
    </div>
    <div class="modal-footer">
      <button ad-button adType="default" (click)="handleCloseSelect()">
        {{ 'dj-取消' | translate }}
      </button>
      <button ad-button adType="primary" (click)="handleChangeAction()">
        {{ 'dj-确定' | translate }}
      </button>
    </div>
  </ng-container>
</ad-modal>
