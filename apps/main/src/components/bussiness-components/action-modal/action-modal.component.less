::ng-deep .app-action-list-modal {
  .action-tabs{
    &.is-hide {
      display: none;
    }
  }
  .actionType {
    .ant-select {
      width: 200px;
      margin-left: 10px;
    }
  }
  .searchAction {
    width: 50%;
    margin-bottom: 10px;
  }
  .searchAction {
    margin-bottom: 10px;
  }
  .attr-table {
    height: 444px;
    position: relative;
    ::ng-deep {
      ad-table {
        // .ag-cell {
        //   line-height: 34px;
        // }
        // .athena-table-pagination {
        //   margin-top: 4px;
        // }
      }
    }
    .page-number {
      position: absolute;
      left: 0;
      top: 419px;
      font-size: 12px;
    }
  }
  .red-tip {
    color: #ea3d46;
    font-size: 13px;
  }
  .modal-footer {
    text-align: center;
    justify-content: center;
    padding-top: 30px;
    border: 0;
    button {
      width: 88px;
      height: 32px;
      border-radius: 20px;
      margin: 0 11px;

      &:not(.ant-btn-primary) {
        border-color: #6a4cff;
        color: #6a4cff;

        &:hover {
          border-color: #5a4fee;
          color: #5a4fee;
        }
      }
    }
  }
}
