import { TranslateLoader } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { Observable, forkJoin } from 'rxjs';
import { map } from 'rxjs/operators';
import { getEnvParamsScheduleDomain } from '@env';

export class AppTranslateLoader implements TranslateLoader {
  constructor(
    private http: HttpClient,
    private prefix: string = 'scheduler/static/assets/i18n/',
    private suffix: string = '.json',
  ) {}

  public getTranslation(lang: string): Observable<any> {
    return forkJoin([
      this.http.get(`${getEnvParamsScheduleDomain()}/${this.prefix}${lang}/basic${this.suffix}`),
      this.http.get(`${getEnvParamsScheduleDomain()}/${this.prefix}${lang}/design${this.suffix}`),
    ]).pipe(
      map((response) => {
        return { ...response[0], ...response[1] };
      }),
    );
  }
}

export function HttpLoaderFactory(http: HttpClient) {
  return new AppTranslateLoader(http);
}
