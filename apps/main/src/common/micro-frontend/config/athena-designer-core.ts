import { getEnvParamsRemoteDomain } from '@env';

const data = {
  name: 'athena_designer_core',
  remotes: {
    athena_designer_core: `athena_designer_core@${getEnvParamsRemoteDomain()}/remoteEntry.js`,
  },
  remoteModules: [
    {
      remoteEntry: `${getEnvParamsRemoteDomain()}/remoteEntry.js`,
      remoteName: 'athena_designer_core',
      exposedModule: './ModelGraph',
    },
    {
      remoteEntry: `${getEnvParamsRemoteDomain()}/remoteEntry.js`,
      remoteName: 'athena_designer_core',
      exposedModule: './ComponentSetting',
    },
    {
      remoteEntry: `${getEnvParamsRemoteDomain()}/remoteEntry.js`,
      remoteName: 'athena_designer_core',
      exposedModule: './OperatePermission',
    },
    {
      remoteEntry: `${getEnvParamsRemoteDomain()}/remoteEntry.js`,
      remoteName: 'athena_designer_core',
      exposedModule: './DynamicWorkDesign',
    },
    {
      remoteEntry: `${getEnvParamsRemoteDomain()}/remoteEntry.js`,
      remoteName: 'athena_designer_core',
      exposedModule: './DropDownVocabularyModal',
    },
    {
      remoteEntry: `${getEnvParamsRemoteDomain()}/remoteEntry.js`,
      remoteName: 'athena_designer_core',
      exposedModule: './DataElementModal',
    },
    {
      remoteEntry: `${getEnvParamsRemoteDomain()}/remoteEntry.js`,
      remoteName: 'athena_designer_core',
      exposedModule: './RepresentClassModal',
    },
    // {
    //   remoteEntry: `${envParams.remoteDomain}/remoteEntry.js`,
    //   remoteName: 'athena_designer_core',
    //   exposedModule: './react',
    // },
    // {
    //   remoteEntry: `${envParams.remoteDomain}/remoteEntry.js`,
    //   remoteName: 'athena_designer_core',
    //   exposedModule: './react-dom',
    // },
  ],
  shared: {
    react: {
      singleton: true,
      requiredVersion: '18.2.0',
      // eager: true,
    },
    'react-dom': {
      singleton: true,
      requiredVersion: '18.2.0',
      // eager: true,
    },
  },
};
export default data;
