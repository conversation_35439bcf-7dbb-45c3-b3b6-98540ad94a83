body {
  margin: 0;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell',
    'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

.antd-app {
  height: 100%;
  overflow: auto;
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.module-federation-model-root {
  .ant-tooltip {
    &.ant-tooltip-placement-top {
      padding-bottom: unset;
      .ant-tooltip-arrow {
        transform: translateX(-50%) translateY(100%) rotate(180deg);
      }
    }
    &.ant-tooltip-placement-bottom {
      padding-top: unset;
      .ant-tooltip-arrow {
        transform: translateX(-50%) translateY(-100%);
      }
    }
    &.ant-tooltip-placement-right {
      padding-left: unset;
      .ant-tooltip-arrow {
        transform: translateY(-50%) translateX(-100%) rotate(-90deg);
      }
    }
    &.ant-tooltip-placement-left {
      padding-right: unset;
      .ant-tooltip-arrow {
        transform: translateY(-50%) translateX(100%) rotate(90deg);
      }
    }
    &.ant-tooltip-placement-topLeft {
      padding-bottom: unset;
    }
    &.ant-tooltip-placement-topRight {
      padding-bottom: unset;
    }
    &.ant-tooltip-placement-bottomLeft {
      padding-bottom: unset;
    }
    &.ant-tooltip-placement-bottomRight {
      padding-bottom: unset;
    }
  }

  .ant-popover-placement-top {
    padding: 0;
    .ant-popover-arrow {
      transform: translateX(-50%) translateY(100%) rotate(180deg);
      &::before {
        position: absolute;
        bottom: 0;
        inset-inline-start: 0;
        width: 16px;
        height: 8px;
        background: var(--antd-arrow-background-color);
        clip-path: polygon(
          1.6568542494923806px 100%,
          50% 1.6568542494923806px,
          14.34314575050762px 100%,
          1.6568542494923806px 100%
        );
        clip-path: path(
          'M 0 8 A 4 4 0 0 0 2.82842712474619 6.82842712474619 L 7.292893218813452 2.363961030678928 A 1 1 0 0 1 8.707106781186548 2.363961030678928 L 13.17157287525381 6.82842712474619 A 4 4 0 0 0 16 8 Z'
        );
        content: '';
      }
      &::after {
        content: '';
        position: absolute;
        width: 8.970562748477143px;
        height: 8.970562748477143px;
        bottom: 0;
        inset-inline: 0;
        margin: auto;
        border-radius: 0 0 1px 0;
        transform: translateY(50%) rotate(-135deg);
        box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.05);
        z-index: 0;
        background: transparent;
      }
    }
  }

  .ant-popconfirm-inner-content {
    .ant-popconfirm-message {
      margin-bottom: 8px;
      display: flex;
      flex-wrap: nowrap;
      align-items: start;
      .ant-popconfirm-message-icon .anticon {
        color: #fe7b1d;
        font-size: 13px;
        line-height: 1;
        margin-inline-end: 8px;
      }
      .ant-popconfirm-title {
        font-weight: normal;
      }
    }
    .ant-popconfirm-buttons {
      text-align: end;
      white-space: nowrap;
      button {
        margin-inline-start: 8px;
      }
    }
  }

  .ant-select-item {
    position: relative;
    display: block;
    min-height: 32px;
    padding: 5px 12px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: normal;
    font-size: 14px;
    line-height: 22px;
    cursor: pointer;
    transition: background 0.3s ease;
  }

  .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
    color: #6a4cff;
    font-weight: normal;
    background-color: #eef0ff;
    // border-radius: 0;
  }
}
.ant-radio,
.ant-checkbox {
  top: unset;
}

.ant-modal .ant-modal-close-x {
  width: unset;
  height: unset;
}

.route-wrap-spin {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.ant-steps .ant-steps-item-disabled * {
  cursor: not-allowed !important;
}
.ant-btn-primary:hover:not([disabled]),
.ant-btn-primary:focus:not(.ant-btn-background-ghost) {
  background-color: #6868ae;
}
