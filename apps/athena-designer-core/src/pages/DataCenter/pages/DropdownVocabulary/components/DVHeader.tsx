import React, { BaseSyntheticEvent, useContext, useMemo, useRef } from 'react';
import { Flex, message, Space } from 'antd';
import { useTranslation } from 'react-i18next';
import { DVModuleContext } from './DVModuleStateWrapper';
import AppInput from '@/components/AppInput';
import AppSelect from '@/components/AppSelect';
import { Icon } from '@/components';
import AppButton from '@/components/AppButton';
import { useDataStandardStore } from '@/pages/DataCenter/store';
import { DV_MODULE_ACTION_ENUM, EAuditStatus } from '../hooks/useDVModuleStateHook';
import { EditActionType } from './EditModal';
import useAxios from '../hooks/useAxios';
import serviceConfig from '../tools';
import useLoading from '../hooks/useLoading';
import { downloadTool } from '@/components/AppUpload/tools';
import AppUpload, { UploadBusinessType } from '@/components/AppUpload';

import headerStyle from './header.module.less';

const DVHeader = () => {
  const { t } = useTranslation();
  const moduleContext = useContext(DVModuleContext);
  const { isDataStandardPermission } = useDataStandardStore();
  const { fetch: downloadFN } = useAxios(serviceConfig.downloadTemplate);
  const {fetch: dataPublshFn} = useAxios(serviceConfig.dataPublish)
  const { state, setDVState } = moduleContext;
  const uploadRef = useRef(null);

  const {loading: dataPublishLoading, fn: handleDataPublish} = useLoading(async() => {
    const res = await dataPublshFn()
    if(res.code === 0){
      message.success(t('dj-数据发布成功'))
    }
  },[])
  const { loading: downloading, fn: doDownloadTemplate } = useLoading(async () => {
    const res = await downloadFN({ targetTemplate: 'dictionary' });
    downloadTool(res as BlobPart, `${t('dj-下拉辞汇模板')}.xls`);
  }, []);
  const selectOptions = useMemo(() => {
    return [
      {
        value: 'Y',
        label: t('dj-标准辞汇'),
      },
      {
        value: 'N',
        label: t('dj-非标准辞汇'),
      },
    ];
  }, []);

  const approveOptions = useMemo(() => {
    return [
      {
        value: EAuditStatus.TO_AUDIT,
        label: t('dj-待审核'),
      },
      {
        value: EAuditStatus.AUDIT_PASS,
        label: t('dj-审核通过'),
      },
    ];
  }, []);
  const doConditionInput = (event: BaseSyntheticEvent) => {
    setDVState({
      type: DV_MODULE_ACTION_ENUM.CONDITION,
      data: {
        condition: event.target.value,
      },
    });
  };
  const doAdd = () => {
    setDVState({
      type: DV_MODULE_ACTION_ENUM.OP_INFO,
      data: {
        opType: EditActionType.ADD,
        isShow: true,
        info: {},
        disableCode: false,
        disableCheckBox: false,
      },
    });
  };
  const doBatchImport = () => {
    if (uploadRef.current) {
      uploadRef.current.showOrHide(true);
    }
  };
  const doStandardChange = (value: string) => {
    setDVState({
      type: DV_MODULE_ACTION_ENUM.SET_SELECTROWS,
      data: [],
    });
    setDVState({
      type: DV_MODULE_ACTION_ENUM.STANDARD,
      data: {
        standard: value,
      },
    });
  };
  const doAuditStatusChange = (value: EAuditStatus) => {
    setDVState({
      type: DV_MODULE_ACTION_ENUM.SET_SELECTROWS,
      data: [],
    });
    setDVState({
      type: DV_MODULE_ACTION_ENUM.AUDITSTTAUS,
      data: {
        auditStatus: value,
      },
    });
  };
  const doReQuery = () => {
    setDVState({
      type: DV_MODULE_ACTION_ENUM.TABLE_REQUERY,
    });
  };
  return (
    <div className={headerStyle.dvHeader}>
      <Space.Compact>
        <AppSelect
          manualClassLists={['compactSelect']}
          size="small"
          value={state.queryParams.standard}
          onChange={doStandardChange}
          options={selectOptions}
          allOption
          popupClassName="selectStyle"
        />
        <AppSelect
          manualClassLists={['compactSelect']}
          size="small"
          value={state.queryParams.auditStatus}
          onChange={doAuditStatusChange}
          options={approveOptions}
          allOption
          allOptionValue={EAuditStatus.ALL}
          popupClassName="selectStyle"
        />
        <AppInput
          onChange={doConditionInput}
          placeholder={t('dj-请输入辞汇编号或说明')}
          style={{ width: '250px' }}
          allowClear
          // size="small"
          suffix={<Icon className="iconfont" type="iconsousuo" onClick={doReQuery} />}
        />
      </Space.Compact>
      <Flex gap="small">
        <AppButton type="primary" loading={dataPublishLoading} ghost size="small" onClick={handleDataPublish}>
          {t('dj-数据发布')}
        </AppButton>
        <AppButton type="primary" size="small" onClick={doAdd} ghost>
          {t('dj-新增')}
        </AppButton>
        <AppButton type="primary" size="small" onClick={doBatchImport} ghost>
          {t('dj-批量导入')}
        </AppButton>
        <AppButton
          type="primary"
          size="small"
          onClick={doDownloadTemplate}
          loading={downloading}
          ghost
        >
          {t('dj-下载模版')}
        </AppButton>
      </Flex>
      <AppUpload
        title={t('dj-选择上传文件')}
        ref={uploadRef}
        afterUpload={doReQuery}
        modalProps={{
          type: UploadBusinessType.Dictionary,
        }}
      />
    </div>
  );
};

export default DVHeader;
