import { AthTreeDataNode, DslWorkDesignData } from '../components/DynamicWorkDesignContent/type';
import {
  DynamicWorkDesignInfo,
  DynamicWorkDesignRenderWholeData,
  Pattern,
  Category,
  PageCode,
  PageUIElementContent,
} from '../config/type/index';
import { v4 as uuidv4 } from 'uuid';
import { cleanObject, getButtonTypeByAction, handleInnerActions } from './button';
import { cloneDeep } from 'lodash';
import { IExtraDataStateInfo } from '../components/DynamicWorkDesignContent/hooks/useExtraData';

type OperationTypeToButtonTypeFn = (operate?: string) => string;

/**
 * 历史operation类型转动态按钮类型
 */
const OperationTypeToButtonTypeMap: Map<string, OperationTypeToButtonTypeFn> = new Map([
  ['add-row', () => 'BUTTON_ADD_ITEM'],
  ['delete-row', () => 'BUTTON_DELETE_ITEM'],
  ['delete-row-submit', () => 'BUTTON_DELETE_ITEM'],
  ['batch-delete', () => 'BUTTON_BATCH_DELETE_ITEM'],
  [
    'copy',
    (operate?: string): string => {
      if (operate === 'openpage') return 'BUTTON_OPENPAGE_COPY';
      // 临时值，插入layout时需要重新刷新
      else return 'BUTTON_COPY_ITEM';
    },
  ],
  ['add', () => 'BUTTON_OPENPAGE_ADD'],
  ['edit', () => 'BUTTON_OPENPAGE_EDIT'],
  ['openpage_copy', () => 'BUTTON_OPENPAGE_COPY'],
  ['upload_file', () => 'BUTTON_UPLOAD_FILE'],
  ['download_template', () => 'BUTTON_DOWNLOAD_TEMPLATE'],
  ['drawings-download', () => 'BUTTON_DRAWINGS_DOWNLOAD'],
  ['table-export', () => 'BUTTON_FRONT_EXPORT'],
  ['data_export', () => 'BUTTON_BACKEND_EXPORT'],
  ['batch-set-date', () => 'BUTTON_BATCH_SET_DATE'],
  ['script', () => 'BUTTON_OPERATE_SCRIPT'],
  ['split-row', () => 'BUTTON_SPLIT_ROW'],
  ['auto-split-row', () => 'BUTTON_AUTO_SPLIT_ROW'],
  ['print', () => 'BUTTON_PRINT'],
]);

/**
 * 历史toolbar内部按钮类型转动态按钮类型
 */
const ToolbarTypeToButtonTypeMap: Map<string, string> = new Map([
  ['add', 'BUTTON_OPENPAGE_ADD'],
  ['copy', 'BUTTON_OPENPAGE_COPY'],
  ['edit', 'BUTTON_TOOLBAR_EDIT'],
  ['previous', 'BUTTON_TOOLBAR_PREVIOUS'],
  ['next', 'BUTTON_TOOLBAR_NEXT'],
]);

const processOldPrintOperationData = (data: any): any => {
  if (data?.extendedFields) {
    if (
      !Reflect.has(data?.extendedFields, 'templates') &&
      data?.extendedFields?.action?.businessConfig?.activityId
    ) {
      return {
        ...data,
        extendedFields: {
          ...data.extendedFields,
          templates: [
            {
              actionId: data.extendedFields.action?.actionId,
              templateId: data.extendedFields.action?.businessConfig.activityId,
              actionParams: data.extendedFields.action?.actionParams || [],
            },
          ],
        },
      };
    }
  }
  return data;
};

export const getPreloadRenderDslWorkDesignData = (): DslWorkDesignData => {
  return {
    pageUIElementContent: {
      layout: [],
      operations: [],
      submitActions: [],
      hooks: [],
      gridSettings: [],
    },
    fieldTreeMap: new Map(),
    dynamicWorkDesignInfo: {},
    dataSourceInfo: {
      dataSourceName: '',
      dataSourceNames: [],
      dataSources: {},
    },
  };
};

export function createSchemaToFullPathMap(fieldTree: AthTreeDataNode[] = []): Map<string, string> {
  let stack: AthTreeDataNode[] = cloneDeep(fieldTree);
  const schemaToFullPathMap: Map<string, string> = new Map();
  while (stack.length > 0) {
    const node = stack.shift();
    const schema = node.data_name;
    const fullPath = node.fullPath;
    schemaToFullPathMap.set(schema, fullPath);
    if (node.children && node.children.length > 0) {
      stack = stack.concat(node.children);
    }
  }
  return schemaToFullPathMap;
}

// 获取用于渲染的基础必要数据，返回null 则表示基础数据未准备充分
// 该方法可以用于检测是否可以开始渲染，或者被getRenderDslWorkDesignData使用，获取完整的渲染数据
export const getRenderDslWorkDesignBaseData = (
  dynamicWorkDesignInfoOrigin: DynamicWorkDesignInfo,
  dynamicWorkDesignRenderDataOrigin: DynamicWorkDesignRenderWholeData,
  version
): Omit<
  DslWorkDesignData,
  'systemConfig' | 'isvComponentList' | 'dynamicWorkDesignInfo'
> | null => {
  if ((!dynamicWorkDesignInfoOrigin && version === '2.0') || !dynamicWorkDesignRenderDataOrigin) return null;
  const dynamicWorkDesignInfo = cloneDeep(dynamicWorkDesignInfoOrigin);
  const dynamicWorkDesignRenderData = cloneDeep(dynamicWorkDesignRenderDataOrigin);

  const { code: workDesignCode, pageCode } = dynamicWorkDesignInfo;
  const {
    dataSourceName = '',
    pageUIElementContent: rawPageUIElementContent,
    dataSources = {},
    extendedFields = {},
    ruleList = [],
    dataSourceNames = [],
    fieldTreeMap,
  } = dynamicWorkDesignRenderData;

  const pageUIElementContent = {
    layout: [],
    operations: [],
    submitActions: [],
    hooks: [],
    gridSettings: [],
    ...rawPageUIElementContent,
  };

  let transferPageUIElementContent;
  if (version === '1.0') {
    if (!fieldTreeMap || fieldTreeMap.size === 0 || !fieldTreeMap.has(dataSourceName)) return null;

    if (!dataSources?.[dataSourceName]) return null;
    // 之后不会再存在 submitActions ，但对历史数据需要兼容，所以这里会有转换逻辑
    // submitActions 转换成 buttonGroup 之后，清空submitActions
    // 2025-02-10: qiansw: 因定制页面，按钮不可以转化为动态按钮（垃圾平台不支持！！！），暂不转化，仅支持json
    if (
      !dynamicWorkDesignInfo?.dynamicWorkDesignConfig?.commonConfig?.isCustomize &&
      pageUIElementContent?.submitActions.length > 0
    ) {
      const buttonGroup = submitActionsConvertToButtonGroup(
        pageUIElementContent?.submitActions,
        pageCode
      );
      pageUIElementContent.submitActions = [];
      // 这里有平台的特殊逻辑，对于平台来说LAYOUT组件比较特殊，所以 有 LAYOUT 组件存在的 情况下
      // 把 buttonGroup 放到 LAYOUT 组件的content的 group 的 末尾，而不是 放到 layout 末尾
      // 判断第一层即可
      const layoutComponent = pageUIElementContent?.layout?.find((item) => item.type === 'LAYOUT');
      if (layoutComponent && layoutComponent?.content?.group) {
        layoutComponent.content.group = [...layoutComponent.content.group, ...buttonGroup];
      } else {
        pageUIElementContent.layout = [...pageUIElementContent.layout, ...buttonGroup];
      }
    }

    /**
     * WARN: 兼容！兼容！兼容！
     * 不是在兼容就是在兼容的路上!
     */

    const currentFieldTree = fieldTreeMap.get(dataSourceName);
    const schemaToFullPathMap: Map<string, string> = createSchemaToFullPathMap(currentFieldTree);

    // 兼容处理，如果操作数据没有id就补充id
    pageUIElementContent.operations =
      pageUIElementContent.operations?.map((operation) => {
        const dumpOperation = { ...(operation ?? {}) };
        const { target } = dumpOperation ?? {};
        /**
         * schemaToFullPathMap是schema指向fullPath的信息，所以，如果能在这个map中找到，说明这个operation的target是schema
         * 那么需要将这个target转成fullPath
         * 同样的，虽然这个target可能不是fullPath，但是也有可能不是schema
         * 加入树有三层M,D,S
         * 这个target,即可能是M.D.S也可能是S,同样也有可能是D.S
         * 这里只是借助这个办法，拿最后一个.后面的schema,可能和这个方法的本意不一致
         */
        const { schema } = getSchemaAndPathFromTarget(target);
        if (schemaToFullPathMap.has(schema)) {
          dumpOperation.target = schemaToFullPathMap.get(schema);
        }
        if (dumpOperation?.type === 'print' && dumpOperation?.mode === 'row') {
          /**
           * 兼容处理operations中打印模板的历史数据
           */
          const hackOperation = processOldPrintOperationData(dumpOperation);
          return {
            ...hackOperation,
            id: dumpOperation.id ?? uuidv4(),
          };
        }
        return {
          ...dumpOperation,
          id: dumpOperation.id ?? uuidv4(),
        };
      }) ?? [];

    transferPageUIElementContent = transformOperationsAndToolbarToDynamicButton(
      pageUIElementContent,
      dynamicWorkDesignInfo?.pageCode,
      /**
       * 目前只接了基础资料
       */
      Pattern.DATA_ENTRY,
      dynamicWorkDesignInfo?.category,
      dynamicWorkDesignInfo?.parentPageCode
    );
  }

  return {
    pageUIElementContent: version === '1.0' ? transferPageUIElementContent : pageUIElementContent,
    rules: ruleList,
    fieldTreeMap,
    statusInfo: extendedFields,
    dataSourceInfo: {
      dataSourceName,
      dataSources,
      dataSourceNames,
    },
  };
};

export const rebuildHidden = (hidden: any): any => {
  if (!hidden) return {};
  const { condition, ...rest } = hidden ?? {};
  return {
    script: condition,
    ...rest,
  };
};

// submitActions转换buttonGroup逻辑
export const submitActionsConvertToButtonGroup = (
  submitActions: any[],
  pageCode: PageCode
): any[] => {
  const buttons = submitActions.map((item, index) => {
    const {
      id,
      type,
      actionType,
      actionParams,
      actionId,
      serviceName,
      url,
      paras,
      lang,
      returnText,
      executeAfterCheckCompleted,
      extendParas,
      dispatchBPM,
      needProxyToken,
      dispatch,
      terminateProcess,
      defaultAction,
      trackCode,
      submitType,
      hidden,
      condition,
      confirm,
      combineActions,
      attachActions,
      sizeType,
      ...rest
    } = item;
    const { returnText: returnTextLang, ...restLang } = lang ?? {};
    const action = {
      type,
      actionType,
      actionParams,
      actionId,
      serviceName,
      url,
      paras,
      returnText,
      lang: {
        returnText: returnTextLang,
      },
      executeAfterCheckCompleted,
      extendParas,
      dispatchBPM,
      needProxyToken,
      dispatch,
      terminateProcess,
      trackCode,
      submitType,
      attachActions,
      combineActions,
    };
    const isHiddenStr = typeof hidden === 'string';
    const isConditionStr = typeof condition === 'string';
    const actualHidden = isHiddenStr ? { script: hidden } : rebuildHidden(hidden);
    const actualCondition = isConditionStr ? { script: condition } : { ...(condition ?? {}) };
    const info = {
      id: id ?? uuidv4(),
      ...rest, // 最后一个SubmitAction设为默认
      styleMode: defaultAction || index === submitActions.length - 1 ? 'primary' : 'default',
      size: sizeType || 'large',
      type: getButtonTypeByAction(action),
      hiddenConfig: actualHidden,
      condition: actualCondition,
      confirm: { ...(confirm ?? {}) },
      lang: {
        ...restLang,
      },
      action: handleInnerActions(cleanObject(action)),
    };
    return cleanObject(info);
  });
  return [
    {
      evolutionFromSubmitActions: true, // 标记：从submitActions转化而来
      id: uuidv4(),
      type: 'BUTTON_GROUP',
      headerName: '按钮组',
      verticalAlignment: 'center',
      block: true,
      justifyContent: 'center',
      gap: '12px',
      position: 'absolute',
      padding: ['12px', '0', '12px', '0'],
      bottom: '0px',
      lang: {
        headerName: {
          zh_CN: '按钮组',
          zh_TW: '按鈕組',
          en_US: 'Button Group',
        },
      },
      moreButtonConfig: {
        enable: false,
      },
      group: buttons ?? [],
    },
  ];
  //return [
  //  {
  //    id: uuidv4(),
  //    type: 'FLEX',
  //    justifyContent: 'center',
  //    alignItems: 'center',
  //    flexDirection: 'row',
  //    flexWrap: 'row',
  //    gap: '12px',
  //    height: 'auto',
  //    overflow: 'auto',
  //    position: 'absolute',
  //    bottom: '20px',
  //    style: '{"width":"100%","flex-grow":"0","flex-shrink":"0","flex-basis":"auto"}',
  //    items: [
  //      {
  //        style: '{}',
  //        group: [
  //          {
  //            id: uuidv4(),
  //            type: 'BUTTON_GROUP',
  //            headerName: '按钮组',
  //            verticalAlignment: 'center',
  //            gap: '12px',
  //            lang: {
  //              headerName: {
  //                zh_CN: '按钮组',
  //                zh_TW: '按鈕組',
  //                en_US: 'Button Group',
  //              },
  //            },
  //            moreButtonConfig: {
  //              enable: false,
  //            },
  //            group: buttons ?? [],
  //          },
  //        ],
  //      },
  //    ],
  //  },
  //];
};

/**
 * 通过schema&path构建fullPath
 */
export const getFullPathFromSchemaAndPath = (schema?: string, path?: string) => {
  if (!schema) return null;
  return [path, schema].filter(Boolean).join('.');
};

/**
 * 将历史operations的type转成动态按钮的类型
 */
export const transformOriginOperationTypeToDynamicButtonType = (
  type: string,
  operate: string
): string => {
  let actualType: string = '';
  for (const [originType, buttonTypeFn] of OperationTypeToButtonTypeMap.entries()) {
    if (originType === type) {
      actualType = buttonTypeFn(operate);
      break;
    }
  }
  return actualType;
};

/**
 * 从target中读取schema+path
 */
export const getSchemaAndPathFromTarget = (
  target: string = ''
): { schema: string; path: string } => {
  const paths = target.split('.') ?? [];
  const schema = paths.length === 0 ? '' : paths[paths.length - 1];
  const path = paths.length === 0 ? '' : paths.slice(0, paths.length - 1).join('.');
  return {
    schema,
    path,
  };
};

/**
 * 将原operations数据转成动态按钮
 */
export const transformOperationToDynamicButton = (operation: any, parentPageCode?: string): any => {
  const {
    title,
    description,
    lang,
    target,
    mode,
    attach = {},
    athType,
    id,
    type,
    operate,
    authKey,
    condition,
    hidden,
    applyToField,
    extendedFields,
    operation: originOperation,
    ...rest
  } = operation;
  const { schema, path } = getSchemaAndPathFromTarget(target);
  const buttonType = transformOriginOperationTypeToDynamicButtonType(type, operate);
  const isHiddenStr = typeof hidden === 'string';
  const isConditionStr = typeof condition === 'string';
  const actualHidden = isHiddenStr ? { script: hidden } : rebuildHidden(hidden);
  const actualCondition = isConditionStr ? { script: condition } : { ...(condition ?? {}) };
  const baseDslInfo = {
    id: id ?? uuidv4(),
    type: buttonType,
    title,
    description,
    lang,
    attachMode: mode,
    targetSchema: schema,
    targetPath: path,
    authKey,
    hiddenConfig: actualHidden,
    condition: actualCondition,
    /**
     * 尽量维持原有operation样式
     */
    styleMode: mode === 'row' ? 'text' : (athType ?? 'default'),
    activityId: parentPageCode,
  };
  /**
   * 转换失败表示这个operation是客制按钮，那么转成自定义组件
   */
  if (!buttonType) {
    return {
      ...baseDslInfo,
      headerName: title,
      lang: {
        ...(lang ?? {}),
        headerName: {
          ...(lang?.title ?? {}),
        },
      },
      type,
      operation: {
        ...rest,
        operate,
      },
    };
  }
  /**
   * 打印按钮数据结构和其它operation数据结构不一致
   */
  if (buttonType === 'BUTTON_PRINT') {
    return {
      ...baseDslInfo,
      action: {
        ...(extendedFields?.action ?? {}),
      },
      templates: extendedFields?.templates ?? [],
    };
  }
  const { confirm, ...originRest } = originOperation ?? {};
  const newDslInfo: any = {
    ...baseDslInfo,
    operation: {
      ...(originRest ?? {}),
      attach: {
        ...attach,
        applyToField,
      },
      extendedFields: extendedFields ? extendedFields : null,
      ...rest,
      operate,
    },
  };
  if (confirm) {
    newDslInfo.confirm = confirm;
  }
  return newDslInfo;
};

/**
 * 将原Toolbar数据转成按钮组+动态按钮的形式
 * WARN: 这边有个历史问题，顺手解决，就是为Toolbar内部没有id的按钮补上id
 */
export const transformToolbarToDynamicButton = (toolbar: any): any => {
  const { id, headerName, lang, target = '', items = [] } = toolbar;
  /**
   * WARN: toolbar的target直接转schema+path是有风险的，之前的target是输入框，且之前的target代表的其实是schema，但是我们这边目前并没有更好的方式避免这个问题
   */
  const { schema, path } = getSchemaAndPathFromTarget(target);
  const dynamicButtons = items.map((item: any) => {
    const { id, type, title, lang, icon, authKey, condition, hidden, operation, ...rest } =
      item ?? {};
    return {
      id: id ?? uuidv4(),
      type: ToolbarTypeToButtonTypeMap.get(type),
      title,
      lang,
      targetSchema: schema,
      targetPath: path,
      iconConfig: {
        name: icon,
      },
      authKey,
      condition,
      hidden,
      ...rest,
      operation: operation ? operation : {},
    };
  });
  const buttonGroupDslInfo = {
    id: id ?? uuidv4(),
    type: 'BUTTON_GROUP',
    headerName,
    lang,
    gap: '12px',
    verticalAlignment: 'center',
    moreButtonConfig: {
      enable: false,
    },
    block: true,
    justifyContent: 'flex-end',
    group: dynamicButtons,
  };
  Object.getOwnPropertyNames(toolbar).forEach((key: string) => delete toolbar[key]);
  Object.assign(toolbar, buttonGroupDslInfo);
};

/**
 * 使用BUTTON_GROUP包裹operations
 */
export const wrapperOperationByButtonGroup = (operations: any[] = []) => {
  return {
    id: uuidv4(),
    type: 'BUTTON_GROUP',
    gap: '12px',
    headerName: '整单操作',
    verticalAlignment: 'center',
    moreButtonConfig: {
      enable: false,
    },
    lang: {
      headerName: {
        zh_CN: '整单操作',
        zh_TW: '',
        en_US: 'text',
      },
    },
    block: true,
    justifyContent: 'flex-end',
    group: operations,
  };
};

export const updateButtonType = ({
  buttonDsl,
  pageCode,
  category,
  pattern,
}: {
  buttonDsl: any;
  pageCode: PageCode;
  category: Category;
  pattern: Pattern;
}) => {
  if (pattern === Pattern.DATA_ENTRY) {
    if (
      [PageCode.SUB_PAGE, PageCode.BROWSE_PAGE].includes(pageCode) &&
      buttonDsl.type === 'BUTTON_COPY_ITEM' &&
      !buttonDsl.operation?.operate
    ) {
      return {
        ...(buttonDsl ?? {}),
        type: 'BUTTON_OPENPAGE_COPY',
      };
    }
  }
  return buttonDsl;
};

/**
 * 前置处理前期转换遗漏掉的delete-row-submit按钮
 */
export const preAnalyzeDslInfo = (layout: any[]) => {
  if (!layout || layout.length === 0) return;
  layout.forEach((node) => {
    const type = node.type;
    switch (type) {
      case 'ATHENA_TABLE': {
        node.columnDefs?.forEach((column: any) => {
          if (column?.columns && column.columns.length > 0) {
            preAnalyzeDslInfo(column.columns);
          }
        });
        break;
      }
      case 'delete-row-submit': {
        const { operation } = node;
        const { operation: originOperation, ...rest } = operation ?? {};
        const { confirm, ...originRest } = originOperation ?? {};
        const newDslInfo = {
          ...node,
          type: 'BUTTON_DELETE_ITEM',
          operation: {
            ...(rest ?? {}),
            ...(originRest ?? {}),
          },
        };
        if (confirm) {
          newDslInfo.confirm = confirm;
        }
        Object.assign(node, newDslInfo);
        break;
      }
      case 'COLLAPSE': {
        const panels = node.panels;
        if (!panels) break;
        let childs = [];
        panels.forEach((panel: any) => {
          if (panel.group && panel.group.length > 0) {
            childs = [...childs, ...panel.group];
          }
        });
        preAnalyzeDslInfo(childs);
        break;
      }
      case 'LAYOUT': {
        let childs = [];
        const parts = ['header', 'sider', 'content', 'footer'];
        parts.forEach((part) => {
          if (node?.[part]?.group?.length > 0) {
            childs = [...childs, ...node[part].group];
          }
        });
        preAnalyzeDslInfo(childs);
        break;
      }
      case 'FLEXIBLE_BOX':
      case 'GRIDSTER':
      case 'LIST': {
        if (!node.group || node.group.length === 0) break;
        preAnalyzeDslInfo(node.group);
        break;
      }
      case 'TABS': {
        if (!node.tabs || node.tabs.length === 0) break;
        let childs = [];
        node.tabs.forEach((tab: any) => {
          if (tab.group && tab.group.length > 0) {
            childs = [...childs, ...tab.group];
          }
        });
        preAnalyzeDslInfo(childs);
        break;
      }
      case 'BUTTON_GROUP': {
        if (!node.group || node.group.length === 0) break;
        preAnalyzeDslInfo(node.group);
        break;
      }
      case 'FLEX': {
        if (!node.items || node.items.length === 0) break;
        let childs = [];
        /**
         * 。。。。。。。。。。。。。。。。
         * 结构如此，就特么这样吧，爱咋咋滴
         * 。。。。。。。。。。。。。。。。
         */
        node.items.forEach((item: any) => {
          if (item.group && item.group.length > 0) {
            item.group.forEach((group: any) => {
              if (group.items && group.items.length > 0) {
                group.items.forEach((groupItem: any) => {
                  if (groupItem.group && groupItem.group.length > 0) {
                    childs = [...childs, ...groupItem.group];
                  }
                });
              }
            });
          }
        });
        preAnalyzeDslInfo(childs);
        break;
      }
      default:
        break;
    }
  });
};

/**
 * 1. 处理layout，将operation转换的动态按钮塞到layout的相应位置
 * 2. 处理Toolbar, 将其转成BUTTON_GROUP+BUTTON的形式
 * 3. 更新operationDslMap, 一旦operation找到了它应该放置的位置，则删除这个键值对，后续会根据这个Map中剩余的键值对，将operaions回填到restOperations,避免数据丢失
 */
export const analyzeDslInfo = ({
  layout,
  operationDslMap,
  pageCode,
  category,
  pattern,
}: {
  layout: any[];
  operationDslMap: Map<string, any[]>;
  pageCode: PageCode;
  category: Category;
  pattern: Pattern;
}) => {
  layout.forEach((node) => {
    const type = node.type;
    switch (type) {
      case 'ATHENA_TABLE': {
        const { schema, path } = node;
        const fullPath = getFullPathFromSchemaAndPath(schema, path);
        if (operationDslMap.has(fullPath)) {
          const operationInfos = operationDslMap.get(fullPath);
          const rowOperationButton = [];
          const allOperationButton = [];
          operationInfos.forEach(({ dynamicButton }) => {
            if (dynamicButton.attachMode === 'row') {
              const actualDynamicButton = updateButtonType({
                buttonDsl: dynamicButton,
                pageCode,
                category,
                pattern,
              });
              rowOperationButton.push(actualDynamicButton);
            } else if (dynamicButton.attachMode === 'all') {
              allOperationButton.push(dynamicButton);
            }
          });
          if (rowOperationButton.length > 0) {
            if (!node.columnDefs) {
              node.columnDefs = [];
            }
            node.columnDefs.push({
              headerName: '操作',
              width: 160,
              level: 0,
              lang: {
                headerName: {
                  zh_CN: '操作',
                  zh_TW: '操作',
                  en_US: 'Operate',
                },
              },
              id: uuidv4(),
              columns: rowOperationButton,
            });
          }
          if (allOperationButton.length > 0) {
            if (!node.slots) {
              node.slots = [{ select: 'slot-top-right', group: [] }];
            }
            if (!node.slots.some((slot: any) => slot.select === 'slot-top-right')) {
              node.slots.push({ select: 'slot-top-right', group: [] });
            }
            node.slots.map((slot: any) => {
              if (slot.select === 'slot-top-right') {
                slot.group = slot.group.concat(wrapperOperationByButtonGroup(allOperationButton));
              }
              return slot;
            });
          }
          operationDslMap.delete(fullPath);
        }
        break;
      }
      case 'TOOLBAR': {
        transformToolbarToDynamicButton(node);
        break;
      }
      case 'COLLAPSE': {
        const panels = node.panels;
        if (!panels) break;
        let childs = [];
        panels.forEach((panel: any) => {
          if (panel.group && panel.group.length > 0) {
            childs = [...childs, ...panel.group];
          }
        });
        analyzeDslInfo({
          layout: childs,
          operationDslMap,
          pageCode,
          category,
          pattern,
        });
        break;
      }
      case 'LAYOUT': {
        let childs = [];
        const parts = ['header', 'sider', 'content', 'footer'];
        parts.forEach((part) => {
          if (node?.[part]?.group?.length > 0) {
            childs = [...childs, ...node[part].group];
          }
        });
        analyzeDslInfo({
          layout: childs,
          operationDslMap,
          pageCode,
          category,
          pattern,
        });
        break;
      }
      case 'FLEXIBLE_BOX':
      case 'GRIDSTER':
      case 'LIST': {
        if (!node.group || node.group.length === 0) break;
        analyzeDslInfo({
          layout: node.group,
          operationDslMap,
          pageCode,
          category,
          pattern,
        });
        break;
      }
      case 'FORM_LIST': {
        const { schema, path } = node;
        const fullPath = getFullPathFromSchemaAndPath(schema, path);
        if (operationDslMap.has(fullPath)) {
          const operations = operationDslMap.get(fullPath);
          if (!node.slots) {
            node.slots = [{ select: 'slot-top-right', group: [] }];
          }
          if (!node.slots.some((slot: any) => slot.select === 'slot-top-right')) {
            node.slots.push({ select: 'slot-top-right', group: [] });
          }
          node.slots.map((slot: any) => {
            if (slot.select === 'slot-top-right') {
              slot.group = slot.group.concat(wrapperOperationByButtonGroup(operations));
            }
            return slot;
          });
          operationDslMap.delete(fullPath);
        }
        break;
      }
      case 'TABS': {
        if (!node.tabs || node.tabs.length === 0) break;
        let childs = [];
        node.tabs.forEach((tab: any) => {
          if (tab.group && tab.group.length > 0) {
            childs = [...childs, ...tab.group];
          }
        });
        analyzeDslInfo({
          layout: childs,
          operationDslMap,
          pageCode,
          category,
          pattern,
        });
        break;
      }
      case 'FLEX': {
        if (!node.items || node.items.length === 0) break;
        let childs = [];
        /**
         * 。。。。。。。。。。。。。。。。
         * 结构如此，就特么这样吧，爱咋咋滴
         * 。。。。。。。。。。。。。。。。
         */
        node.items.forEach((item: any) => {
          if (item.group && item.group.length > 0) {
            item.group.forEach((group: any) => {
              if (group.items && group.items.length > 0) {
                group.items.forEach((groupItem: any) => {
                  if (groupItem.group && groupItem.group.length > 0) {
                    childs = [...childs, ...groupItem.group];
                  }
                });
              }
            });
          }
        });
        analyzeDslInfo({
          layout: childs,
          operationDslMap,
          pageCode,
          category,
          pattern,
        });
        break;
      }
      default:
        break;
    }
  });
};

/**
 * 将历史数据中的operations和整单操作组件转成动态按钮
 */
export const transformOperationsAndToolbarToDynamicButton = (
  pageUIElementContent: PageUIElementContent,
  pageCode: PageCode,
  pattern: Pattern,
  category: Category,
  parentPageCode?: string
) => {
  const copyPageUIElementContent = cloneDeep(pageUIElementContent);
  const { operations = [], layout = [] } = copyPageUIElementContent;
  /**
   * 存储开窗operations，不处理，只做保留
   * WARN: 还包括mode=line的operations，一直都不做处理，和之前逻辑统一，只保留数据
   */
  const restOperations = [];
  /**
   * 存储可能需要处理的oprations
   * WARN: 顾名思义，可能需要处理，那么除了开窗，有可能不处理的operation是指找不到匹配的schema的table的场景
   * 这些operation后续会放到restOperation中重新放到pageUIElementContent中
   * 也就是说：如果历史operations没有与之相匹配的table，也不处理，但是保留数据
   */
  const maybeNeedDealOperations = [];
  operations.forEach((operation) => {
    if (
      ['openwindow', 'open-task-window'].includes(operation.operate) ||
      operation.mode === 'line'
    ) {
      restOperations.push(operation);
    } else {
      maybeNeedDealOperations.push(operation);
    }
  });
  const operationDslMap: Map<string, any> = new Map();
  maybeNeedDealOperations.forEach((operation) => {
    const { target } = operation;
    const dynamicButton = transformOperationToDynamicButton(operation, parentPageCode);
    if (operationDslMap.has(target)) {
      const currentDynamicButtons = operationDslMap.get(target);
      operationDslMap.set(target, [...currentDynamicButtons, { operation, dynamicButton }]);
    } else {
      operationDslMap.set(target, [{ operation, dynamicButton }]);
    }
  });
  preAnalyzeDslInfo(layout);
  analyzeDslInfo({ layout, operationDslMap, pageCode, category, pattern });
  for (const operations of operationDslMap.values()) {
    operations?.forEach(({ operation }) => {
      restOperations.push(operation);
    });
  }
  copyPageUIElementContent.operations = restOperations;
  return copyPageUIElementContent;
};

// 获取用于渲染的完整数据，返回null 则表示基础数据未准备充分
export const getRenderDslWorkDesignData = (
  dynamicWorkDesignInfo: DynamicWorkDesignInfo,
  dynamicWorkDesignRenderData: DynamicWorkDesignRenderWholeData,
  systemConfig,
  isvComponentList: any[],
  currentRenderDslWorkDesignBaseData?: Omit<
    DslWorkDesignData,
    'systemConfig' | 'isvComponentList' | 'dynamicWorkDesignInfo'
  > | null,
  extraData?: IExtraDataStateInfo,
  version
): DslWorkDesignData | null => {
  const renderDslWorkDesignBaseData =
    currentRenderDslWorkDesignBaseData ??
    getRenderDslWorkDesignBaseData(dynamicWorkDesignInfo, dynamicWorkDesignRenderData, version);

  if (!renderDslWorkDesignBaseData) return null;

  return {
    ...renderDslWorkDesignBaseData,
    systemConfig,
    isvComponentList,
    dynamicWorkDesignInfo,
    extraData,
  };
};
