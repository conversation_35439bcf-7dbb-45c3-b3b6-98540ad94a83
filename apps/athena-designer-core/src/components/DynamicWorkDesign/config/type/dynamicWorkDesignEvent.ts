// 界面设计器全局事件总线的事件类型
// 约定模块名 + 事件名
// 模块名(和athena-designer-editor保持一致)：ath（即DynamicWorkDesign自身） lowCode（即界面设计器子应用相关的事件）
export enum DynamicWorkDesignEventType {
  LowCodeRulesHandle = 'LowCodeRulesHandle', // lowCode 对 规则进行了操作
  // LowCodeChangeDynamicWorkDesignRenderData = 'LowCodeChangeDynamicWorkDesignRenderData', // lowCcode 界面设计器数据渲染数据发生变化事件，该事件会触发DynamicWorkDesign对外的changeDynamicWorkDesignRenderData
  AthSideBarToggleLowCodeTabContent = 'AthSideBarToggleLowCodeTabContent', // ath 侧边栏切换lowcode中的tab容器
  // LowCodeOpenWindowHandle = 'LowCodeOpenWindowHandle', // lowCcode 对 开窗进行了操作
  AthChangePageUIElementContent = 'AthChangePageUIElementContent', // DynamicWorkDesign 通知lowcode去更新pageUIElementContent
}
