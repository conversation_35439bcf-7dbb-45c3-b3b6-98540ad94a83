import React, { createContext } from 'react';

type Callback<T> = (result: T) => void;

type DwDesignDispatchType = <T>(
  eventType: string,
  data: any,
  callback?: Callback<T>
) => void;

type DynamicWorkDesignContextType = {
  dwDesignDispatch: DwDesignDispatchType;
};
export const DynamicWorkDesignContext = createContext<DynamicWorkDesignContextType | undefined>(undefined);

export const useDynamicWorkDesignContext = () => {
  const context = React.useContext(DynamicWorkDesignContext);
  if (!context) {
    throw new Error('useDynamicWorkDesignContext must be used within a DynamicWorkDesignProvider');
  }
  return context;
};
