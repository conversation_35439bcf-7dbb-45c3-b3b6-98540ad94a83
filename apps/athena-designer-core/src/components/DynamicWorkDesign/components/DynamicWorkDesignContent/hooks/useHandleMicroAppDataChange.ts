import { globalEventEmitter } from '@/common/hooks';
import { MessageToMainContent, MessageToMainType } from '../type';
import { athMicroApp } from '../athMicroApp';
import { DynamicWorkDesignEventType } from '@/components/DynamicWorkDesign/config/type';
import {
  useDynamicWorkDesignSideBarStore,
  useDynamicWorkDesignStore,
} from '@/components/DynamicWorkDesign/store';
import { TAB_CONFIG } from '../../DynamicWorkDesignSideBar/config';

function useHandleMicroAppDataChange(appName: string) {
  const {
    dynamicWorkDesignRenderData,
    setDynamicWorkDesignRenderDataByKey,
    setIsRenderDataLoading,
    setIsInitEditorLoading,
  } = useDynamicWorkDesignStore((state) => ({
    dynamicWorkDesignInfo: state.dynamicWorkDesignInfo,
    dynamicWorkDesignRenderData: state.dynamicWorkDesignRenderData,
    setDynamicWorkDesignRenderData: state.setDynamicWorkDesignRenderData,
    setDynamicWorkDesignRenderDataByKey: state.setDynamicWorkDesignRenderDataByKey,
    setIsRenderDataLoading: state.setIsRenderDataLoading,
    setIsInitEditorLoading: state.setIsInitEditorLoading,
  }));

  // sidebar是否是铆定状态（由于可能会对中心画布的样式产生影响，所以放在store中）
  const {
    isSideBarRivet,
    setIsSideBarRivet,
    currentTabType,
    setCurrentTabType,
    updatePluginStatus,
  } = useDynamicWorkDesignSideBarStore((state) => ({
    isSideBarRivet: state.isSideBarRivet,
    setIsSideBarRivet: state.setIsSideBarRivet,
    currentTabType: state.currentTabType,
    setCurrentTabType: state.setCurrentTabType,
    updatePluginStatus: state.updatePluginStatus,
  }));

  // micro app 的数据通信 是 数据驱动的，更像是 组件属性的 赋值，所以会有 merge 和 缓存的概念
  // 但在这里，我们的使用 更希望是 事件驱动的，更符合 我们的 逻辑
  // 所以 我们基于事件 驱动 进行 主 子应用 交互，交互后 会选择主动清理 组件的 缓存数据
  const handleDatachange = (e: CustomEvent) => {
    console.log('来自子应用的数据:', e.detail.data);
    const messageToMainContent = e.detail.data as MessageToMainContent;
    const { type, data } = messageToMainContent;

    switch (type) {
      case MessageToMainType.LifeCycles:
        // 子应用的生命周期事件;
        // 子应用准备完毕，可以开始loader数据
        if (data.type === 'mounted') {
          console.log('athena-designer-editor mounted');
          // setIsInitEditorLoading(false);
        }

        if (data.type === 'simulatorRendererReady') {
          console.log('athena-designer-editor simulatorRendererReady');
          setIsInitEditorLoading(false);
        }

        if (data.type === 'ready') {
          console.log('athena-designer-editor ready');
        }

        break;

      case MessageToMainType.Rules:
        globalEventEmitter.emit(DynamicWorkDesignEventType.LowCodeRulesHandle, data);
        break;

      case MessageToMainType.DataSourceInfo:
        if (data.type === 'update' && data.key === 'dataSourceName') {
          setDynamicWorkDesignRenderDataByKey('dataSourceName', data.data);
        }
        break;

      case MessageToMainType.PageUIElementContent:
        const {
          type: PageUIElementContentType,
          pageUIElementContent,
          iamCondition = [],
          groupSchemaList = [],
        } = data;
        if (PageUIElementContentType === 'update') {
          setDynamicWorkDesignRenderDataByKey('pageUIElementContent', pageUIElementContent);
          setDynamicWorkDesignRenderDataByKey('pageUIElementContent', {
            ...(dynamicWorkDesignRenderData?.pageUIElementContent ?? {}),
            ...(pageUIElementContent || {})
          });
          setDynamicWorkDesignRenderDataByKey('iamCondition', iamCondition);
          setDynamicWorkDesignRenderDataByKey('groupSchemaList', groupSchemaList);

          // globalEventEmitter.emit(
          //   DynamicWorkDesignEventType.LowCodeChangeDynamicWorkDesignRenderData,
          //   { ...dynamicWorkDesignRenderData, pageUIElementContent, iamCondition }
          // );
          // 当收到该事件代表本次render完成
          setIsRenderDataLoading(false);
        }

        break;

      case MessageToMainType.Gobal:
        const { type, data: gobalData } = data;
        if (type === 'updateIsSideBarRivet') {
          setIsSideBarRivet(gobalData.isSideBarRivet);
        }
        if (type === 'updatePanelState') {
          const { panelName, isShow } = data?.data ?? {};
          if (!isShow && TAB_CONFIG[currentTabType]?.lowCodePanel === panelName) {
            setCurrentTabType(null);
          }
        }
        if (type === 'updatePluginStatus') {
          const { pluginName, status } = data?.data ?? {};
          updatePluginStatus(pluginName, status);
        }
        break;

      // case MessageToMainType.OpenWindow:
      //   globalEventEmitter.emit(DynamicWorkDesignEventType.LowCodeOpenWindowHandle, data);
      //   break;

      default:
        break;
    }

    athMicroApp.clearData(appName);
  };

  return { handleDatachange };
}

export default useHandleMicroAppDataChange;
