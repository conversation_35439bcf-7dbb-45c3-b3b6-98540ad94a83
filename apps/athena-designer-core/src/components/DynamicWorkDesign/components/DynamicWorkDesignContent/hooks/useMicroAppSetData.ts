import { useEffect, useMemo } from 'react';
import { athMicroApp, appName, url, noDataFieldTree } from '../athMicroApp';
import {
  useDynamicWorkDesignContentStore,
  useDynamicWorkDesignStore,
} from '@/components/DynamicWorkDesign/store';
import { MessageToSubType } from '../type';
import {
  ActionDataSourceInfoData,
  ActionFieldTreeData,
  ActionRuleData,
  MessageToSubAction,
  MessageToSubInitLcdp,
} from '@athena-designer-editor/src/plugins/plugin-ath-loader/type';
import { uniqueId } from 'lodash';
import useExtraData from './useExtraData';
import { hideComponentByVersion } from '@components/DynamicWorkDesign/config/common.config';

// 在这里还是 由于 microApp的 通信机制 并不是 事件模式
// 所以如果连续两次setData，而key又相同，那么将会触发一次事件，而值是两次的merge，这个明显不能满足我们对事件逻辑的需求
// 所以 在 这里，每条消息在通信前将会生成一个唯一key
// 在lowcode解析时，通过key的规则解析成message list ，然后依次执行
export const sendMessageToEditor = (message: MessageToSubAction | MessageToSubInitLcdp) => {
  athMicroApp.clearData(appName);
  const dataKey = uniqueId('@messageData');
  const messageData = {
    [dataKey]: message,
  };
  athMicroApp.setData(appName, messageData as any);
};

function useMicroAppSetData() {
  const { version, dynamicWorkDesignRenderData, setIsRenderDataLoading } = useDynamicWorkDesignStore(
    (state) => ({
      dynamicWorkDesignRenderData: state.dynamicWorkDesignRenderData,
      setIsRenderDataLoading: state.setIsRenderDataLoading,
      version: state.version
    })
  );

  const { lowcodeRenderInitData } = useDynamicWorkDesignContentStore((state) => ({
    lowcodeRenderInitData: state.lowcodeRenderInitData,
  }));

  const extraData = useExtraData({
    iamCondition: dynamicWorkDesignRenderData?.iamCondition ?? [],
    backendDefaultLayout: dynamicWorkDesignRenderData?.backendDefaultLayout ?? null,
  });

  // 规则
  useEffect(() => {
    if (version !== '2.0') {
      sendMessageToEditor({
        type: MessageToSubType.Action,
        data: {
          key: 'rule',
          data: {
            type: 'update',
            data: dynamicWorkDesignRenderData?.ruleList ?? [],
          } as ActionRuleData,
        },
      } as MessageToSubAction);
    }
  }, [version, dynamicWorkDesignRenderData?.ruleList]);

  // 数据源相关
  useEffect(() => {
    if (version !== '2.0') {
      const {
        dataSourceName = '',
        dataSourceNames = [],
        dataSources = {},
      } = dynamicWorkDesignRenderData ?? {};
      sendMessageToEditor({
        type: MessageToSubType.Action,
        data: {
          key: 'dataSourceInfo',
          data: {
            type: 'update',
            data: {
              dataSourceName,
              dataSourceNames,
              dataSources,
            },
          } as ActionDataSourceInfoData,
        },
      } as MessageToSubAction);
    }
  }, [
    version,
    dynamicWorkDesignRenderData?.dataSourceNames,
    dynamicWorkDesignRenderData?.dataSourceName,
    dynamicWorkDesignRenderData?.dataSources,
  ]);

  // 字段树相关
  useEffect(() => {
    const { fieldTreeMap } = dynamicWorkDesignRenderData ?? {};
    if (version !== '2.0') {
      sendMessageToEditor({
        type: MessageToSubType.Action,
        data: {
          key: 'fieldTreeMap',
          data: {
            type: 'update',
            data: fieldTreeMap?.size > 0 ? fieldTreeMap : noDataFieldTree,
          } as ActionFieldTreeData,
        },
      } as MessageToSubAction);
    }
  }, [version, dynamicWorkDesignRenderData?.fieldTreeMap]);

  // 额外信息
  useEffect(() => {
    if (version !== '2.0') {
      sendMessageToEditor({
        type: MessageToSubType.Action,
        data: {
          key: 'extraData',
          data: {
            type: 'update',
            data: extraData,
          } as any,
        },
      } as MessageToSubAction);
    }
  }, [version, extraData]);

  // init逻辑
  // DynamicWorkDesign变更时会清空旧lowcodeRenderInitData并重组，然后进行渲染
  useEffect(() => {
    if (lowcodeRenderInitData) {
      setIsRenderDataLoading(true);
      sendMessageToEditor({
        type: MessageToSubType.InitLcdp,
        data: {
          ...lowcodeRenderInitData,
          version,
          hideComponent: hideComponentByVersion[version] || []
        },
      } as MessageToSubInitLcdp);
    }
  }, [lowcodeRenderInitData]);

  return { sendMessageToEditor };
}

export default useMicroAppSetData;
