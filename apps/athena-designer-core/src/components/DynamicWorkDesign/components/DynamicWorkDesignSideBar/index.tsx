import React, { useState, useEffect, useLayoutEffect } from 'react';
import './index.less';
import RuleTab from './RuleTab';
import Icon from '@/components/Icon';
import { SaveType, TabType } from './type';
import { TAB_CONFIG } from './config';
import { Tooltip } from 'antd';
import { t } from 'i18next';
import { useDynamicWorkDesignSideBarStore } from '../../store';
import { useDynamicWorkDesignStore } from '../../store';
import { globalEventEmitter } from '@/common/hooks';
import { DynamicWorkDesignEventType, DynamicWorkDesignStatus, PageCode } from '../../config/type';
import { DataSource } from '@components/DataSource';
import { useFieldTree } from '@components/DynamicWorkDesign/components/DynamicWorkDesignSideBar/hooks/useFieldTree';
import { useBackendDefaultLayout } from '@components/DynamicWorkDesign/components/DynamicWorkDesignSideBar/hooks/useBackendDefaultLayout';
import { DataConnectors } from '@components/DataConnectors';
import { DataVariables } from '@components/DataVariables';

import type {
  IDataSourceChangePageUIElementCodeParams,
  IDataSourceOrigin,
} from '@components/DataSource/types/dataSource';
import type { SyncTabsCallbackData } from '@components/DataSource/types/syncTabs';
import type { IPageDataInfo } from '@/components/DataSource/types';
import useDynamicWorkDesignStatus from '@components/DynamicWorkDesign/hooks/business/useDynamicWorkDesignStatus';

export interface DynamicWorkDesignSidebarProps {
  inquireWordLibrary?: boolean;
}
const DynamicWorkDesignSideBar: React.FC<DynamicWorkDesignSidebarProps> = (
  props: DynamicWorkDesignSidebarProps
) => {
  const { inquireWordLibrary = false } = props;
  // const [currentTabType, setCurrentTabType] = useState<TabType | null>(null);
  // 展示的tab类型
  const [tabTypeShowList, setTabTypeShowList] = useState<TabType[]>([]);
  // 禁用的tab类型
  const [tabTypeDisableList, setTabTypeDisableList] = useState<TabType[]>([]);
  // 当前tab内部的页签index（比如 数据》数据源&变量）
  const [tabContentIndex, seTabContentIndex] = useState(0);

  const {
    version,
    dynamicWorkDesignInfo,
    dynamicWorkDesignRenderData,
    setDynamicWorkDesignRenderDataByKey,
  } = useDynamicWorkDesignStore((state) => ({
    version: state.version,
    dynamicWorkDesignInfo: state.dynamicWorkDesignInfo,
    dynamicWorkDesignRenderData: state.dynamicWorkDesignRenderData,
    setDynamicWorkDesignRenderData: state.setDynamicWorkDesignRenderData,
    setDynamicWorkDesignRenderDataByKey: state.setDynamicWorkDesignRenderDataByKey,
  }));

  const isVersionOne = version === '1.0';
  const isVersionTwo = version === '2.0';

  // sidebar是否是铆定状态（由于可能会对中心画布的样式产生影响，所以放在store中）
  const { isSideBarRivet, setIsSideBarRivet, currentTabType, setCurrentTabType, pluginStatusMap } =
    useDynamicWorkDesignSideBarStore((state) => ({
      isSideBarRivet: state.isSideBarRivet,
      setIsSideBarRivet: state.setIsSideBarRivet,
      currentTabType: state.currentTabType,
      setCurrentTabType: state.setCurrentTabType,
      pluginStatusMap: state.pluginStatusMap,
    }));
  // 处理字段树抽到单独的hook中
  useFieldTree(isVersionOne, inquireWordLibrary);
  // 拖拽左侧字段树自动生成
  useBackendDefaultLayout(isVersionOne);

  const { dynamicWorkDesignStatus } = useDynamicWorkDesignStatus();

  useLayoutEffect(() => {
    if (
      dynamicWorkDesignStatus === DynamicWorkDesignStatus.Ready &&
      !currentTabType &&
      isVersionOne
    ) {
      // TODO 待优化
      setTimeout(() => {
        handleTabClick(TabType.DataSource);
      });
    }
  }, [dynamicWorkDesignStatus]);

  useEffect(() => {
    let tabTypeShowListData: TabType[] = [
      TabType.DataSource,
      // TabType.Field,
      TabType.Widget,
      TabType.OUTLINE,
      TabType.Rule,
      TabType.Hook,
      TabType.PERMISSION,
    ];
    const tabTypeDisableListData: TabType[] = [];

    const noDataSource = isVersionOne
      ? !dynamicWorkDesignRenderData?.dataSources ||
        Object.keys(dynamicWorkDesignRenderData?.dataSources)?.length === 0
      : !dynamicWorkDesignRenderData?.pageUIElementContent?.dataConnectors ||
        dynamicWorkDesignRenderData.pageUIElementContent.dataConnectors.length === 0;

    if (isVersionOne) {
      if (
        ![PageCode.BASIC_DATA, PageCode.EDIT_PAGE, PageCode.SUB_PAGE].includes(
          dynamicWorkDesignInfo?.pageCode
        )
      ) {
        // tabTypeDisableListData.push(TabType.Rule);
        tabTypeShowListData = tabTypeShowListData.filter((tabType) => tabType !== TabType.Rule);
      }

      if (
        ![
          PageCode.BASIC_DATA,
          PageCode.BROWSE_PAGE,
          PageCode.EDIT_PAGE,
          PageCode.SUB_PAGE,
        ].includes(dynamicWorkDesignInfo?.pageCode)
      ) {
        tabTypeShowListData = tabTypeShowListData.filter(
          (tabType) => tabType !== TabType.PERMISSION
        );
      }
    } else if (isVersionTwo) {
      tabTypeShowListData = [
        TabType.DataConnector,
        // TabType.Field,
        TabType.Widget,
        TabType.OUTLINE,
        TabType.Rule,
        TabType.Hook,
      ];
    }

    setTabTypeShowList(tabTypeShowListData);

    if (noDataSource) {
      // tabTypeDisableListData.push(TabType.Field);
      tabTypeDisableListData.push(TabType.Hook);
      tabTypeDisableListData.push(TabType.Rule);
      // tabTypeDisableListData.push(TabType.Widget);
      // tabTypeDisableListData.push(TabType.OUTLINE);
      tabTypeDisableListData.push(TabType.PERMISSION);
    }

    setTabTypeDisableList(tabTypeDisableListData);

    // 如果当前没有数据源，或者数据源是空的，则默认选到数据源tab
    if (
      !!currentTabType &&
      dynamicWorkDesignStatus === DynamicWorkDesignStatus.Ready &&
      (noDataSource ||
        tabTypeDisableListData.includes(currentTabType) ||
        !tabTypeShowListData.includes(currentTabType)) &&
      isVersionOne
    ) {
      // TODO 待优化
      setTimeout(() => {
        handleTabClick(TabType.DataSource);
      });
    }
  }, [
    version,
    currentTabType,
    dynamicWorkDesignRenderData?.dataSources,
    dynamicWorkDesignRenderData?.dataSourceName,
    dynamicWorkDesignStatus,
    dynamicWorkDesignInfo?.pageCode,
  ]);

  useEffect(() => {
    if (isVersionTwo && currentTabType === TabType.DataSource) {
      setCurrentTabType(TabType.DataConnector);
    }
  }, [version, currentTabType]);

  useEffect(() => {
    window.addEventListener('error', (e) => {
      if (
        e.message.includes('ResizeObserver loop limit exceeded') ||
        e.message.includes('ResizeObserver loop completed with undelivered notifications')
      ) {
        const resizeObserverErrDivs = document.querySelectorAll(
          '#webpack-dev-server-client-overlay-div'
        );
        const resizeObserverErrs = document.querySelectorAll('#webpack-dev-server-client-overlay');
        if (resizeObserverErrs?.length > 0) {
          resizeObserverErrs.forEach((dom) => {
            dom.setAttribute('style', 'display: none');
          });
        }
        if (resizeObserverErrDivs?.length > 0) {
          resizeObserverErrDivs.forEach((dom) => {
            dom.setAttribute('style', 'display: none');
          });
        }
      }
    });
  }, []);

  const handleTabClick = (tabType: TabType | null) => {
    if (tabTypeDisableList.includes(tabType)) return;
    if (TAB_CONFIG[tabType]?.contentRenderType === 'lowCode') {
      globalEventEmitter.emit(
        DynamicWorkDesignEventType.AthSideBarToggleLowCodeTabContent,
        true,
        TAB_CONFIG[tabType]?.lowCodePanel,
        isSideBarRivet
      );
    } else {
      globalEventEmitter.emit(DynamicWorkDesignEventType.AthSideBarToggleLowCodeTabContent, false);
    }
    setCurrentTabType(tabType);
  };

  // 是否渲染tab内容
  const isRenderTabContent = (tabType: TabType) => {
    return currentTabType === tabType || TAB_CONFIG[tabType].contentHideType !== 'none';
  };

  // 获取tab容器的额外样式
  const getTabContentContainerExtraClass = (tabType: TabType) => {
    const extraClassList = [];
    if (currentTabType !== tabType && TAB_CONFIG[tabType].contentHideType !== 'none') {
      extraClassList.push(
        TAB_CONFIG[tabType].contentHideType === 'hide' ? 'hide' : 'render-size-zero'
      );
    }
    return extraClassList.join(' ').trim();
  };

  const handleDataSourceClose = () => {
    handleTabClick(null);
  };

  /**
   * 同步查询方案页签
   */
  const handleSyncTab = (data: SyncTabsCallbackData) => {};

  /**
   * 固定值数据源特定场景触发，依赖optimizeMetadata
   */
  const handleChangePage = (pageData: IPageDataInfo) => {};

  /**
   * 数据源变更触发
   */
  const handleChangeDataSource = (dataSource: IDataSourceOrigin) => {
    setDynamicWorkDesignRenderDataByKey('dataSources', dataSource);
  };

  // 数据连接器变更
  const handleChangeDataConnectors = (data = []) => {
    const fieldTreeMap: Map<string, any> = new Map();
    data.map((dataSource) => {
      const name = dataSource.name;
      const fieldTree = dataSource.option?.responseTree;
      fieldTreeMap.set(name, fieldTree ? [fieldTree] : []);
    });
    setDynamicWorkDesignRenderDataByKey('fieldTreeMap', fieldTreeMap);
    setDynamicWorkDesignRenderDataByKey('pageUIElementContent', {
      ...(dynamicWorkDesignRenderData?.pageUIElementContent ?? {}),
      dataConnectors: data,
    });
    globalEventEmitter.emit(
      DynamicWorkDesignEventType.AthChangePageUIElementContent,
      {
        dataConnectors: data,
      },
      { isLoadImmediately: false, dataKey: 'dataConnectors', saveKey: 'useCoreData.dataConnectors', saveType: SaveType.Root }
    );
  };

  /**
   * 变量变更触发
   */
  const handleChangeVariables = (data) => {
    setDynamicWorkDesignRenderDataByKey('pageUIElementContent', {
      ...(dynamicWorkDesignRenderData?.pageUIElementContent ?? {}),
      variables: data,
    });
    globalEventEmitter.emit(
      DynamicWorkDesignEventType.AthChangePageUIElementContent,
      {
        variables: data,
      },
      { isLoadImmediately: false, dataKey: 'variables', saveKey: 'useCoreData.variables', saveType: SaveType.Root }
    );
  };

  /**
   * dataSource名称/actionId变更触发
   */
  const handleUpdateDataSource = (dataSourceName: string) => {
    // if (dataSourceName === dynamicWorkDesignRenderData?.dataSourceName) {
    //   refreshFieldTree();
    // }
  };

  /**
   * 重置触发
   */
  const handleResetPageUIElement = (data: IDataSourceChangePageUIElementCodeParams) => {
    setDynamicWorkDesignRenderDataByKey('pageUIElementContent', null);
  };

  /**
   * 设置了主数据源
   */
  const handleUpdateDataSourceNames = (dataSourceNames: string[]) => {
    setDynamicWorkDesignRenderDataByKey('dataSourceNames', dataSourceNames);
    if (
      dataSourceNames &&
      dataSourceNames.length > 0 &&
      !dynamicWorkDesignRenderData?.pageUIElementContent
    ) {
      globalEventEmitter.emit(DynamicWorkDesignEventType.AthChangePageUIElementContent,
        {
          layout: [],
          operations: [],
          submitActions: [],
          hooks: [],
          gridSettings: [],
        },
        { isLoadImmediately: true });
      // setDynamicWorkDesignRenderDataByKey('pageUIElementContent', {
      //   layout: [],
      //   operations: [],
      //   submitActions: [],
      //   hooks: [],
      //   gridSettings: [],
      // });
    }
  };

  /**
   * 保留触发
   */
  const handleChangePageUIElementCode = (data: IDataSourceChangePageUIElementCodeParams) => {
    console.log('handleChangePageUIElementCode');
    // pageUIElement多tab的逻辑被移除，所以现在触发保留时无需任何逻辑
  };

  return (
    <div
      className={`dynamic-work-design-sidebar ${!isSideBarRivet ? 'not-rivet' : ''} ${
        !currentTabType || TAB_CONFIG[currentTabType]?.contentRenderType === 'lowCode'
          ? 'closed'
          : ''
      }`}
    >
      <div className="tabs-list">
        {(tabTypeShowList || []).map((tabType) => {
          const tabConfig = TAB_CONFIG[tabType];
          return (
            <div
              key={tabType}
              className={`tabs-item ${tabType === currentTabType ? 'actived' : ''} ${
                tabTypeDisableList.includes(tabType) ? 'not-allowed' : ''
              }`}
              onClick={() => {
                handleTabClick(tabType);
              }}
            >
              <Tooltip placement="right" title={t(tabConfig.title)}>
                <Icon className="item-icon" type={tabConfig.icon} />
              </Tooltip>
            </div>
          );
        })}
      </div>
      <div className="tabs-content">
        <div className="tabs-content-header-new">
          {
            currentTabType === TabType.DataConnector? (<div className='title-tab-items'>
              {
                 TAB_CONFIG[currentTabType]?.subTabs.map((item, index) => {
                  return (
                    <div onClick={() => {
                      seTabContentIndex(index)
                    }} key={item.tabName} className={`title-tab-item ${tabContentIndex === index ? 'actived' : ''}`}>{t(item.tabName)}</div>
                  )
                })
              }
            </div>):<div className="title">{t(TAB_CONFIG[currentTabType]?.title)}</div>
          }
          <div className="tools">
            {isSideBarRivet && (
              <i className="tools-icon tools-rivet">
                <svg
                  onClick={() => {
                    setIsSideBarRivet(false);
                  }}
                  fill="currentColor"
                  preserveAspectRatio="xMidYMid meet"
                  width="16"
                  height="16"
                  viewBox="0 0 1024 1024"
                >
                  <path d="M160.256 816.64C116.224 872.448 102.4 921.6 102.4 921.6s49.152-13.824 104.96-57.856c22.016-17.408 128-112.64 200.704-174.08l-73.728-73.728c-61.44 72.704-157.184 178.688-174.08 200.704zM648.704 209.408L442.368 355.328l226.304 226.304 145.92-206.336 15.872 15.872c20.992 20.992 54.784 20.992 75.776 0s20.992-54.784 0-75.776l-197.12-197.12c-20.992-20.992-54.784-20.992-75.776 0-20.992 20.992-20.992 54.784 0 75.776l15.36 15.36zM247.808 334.848c-9.728 2.048-18.944 6.656-26.624 14.336-20.992 20.992-20.992 54.784 0 75.776l377.856 377.856c20.992 20.992 54.784 20.992 75.776 0 7.68-7.68 12.288-16.896 14.336-26.624L247.808 334.848z"></path>
                  <path d="M840.704 879.104c-9.728 0-19.456-3.584-27.136-11.264L155.648 210.432c-14.848-14.848-14.848-39.424 0-54.272 14.848-14.848 39.424-14.848 54.272 0L867.84 814.08c14.848 14.848 14.848 39.424 0 54.272-7.168 7.168-16.896 10.752-27.136 10.752z"></path>
                </svg>
              </i>
            )}
            {!isSideBarRivet && (
              <Icon
                className="tools-icon tools-not-rivet"
                type="icontuding21"
                onClick={() => {
                  setIsSideBarRivet(true);
                }}
              />
            )}
            <Icon
              className="tools-icon tools-close"
              type="iconguanbi"
              onClick={() => {
                handleTabClick(null);
              }}
            />
          </div>
        </div>
        <div className="tabs-content-body">
          {isVersionOne && isRenderTabContent(TabType.DataSource)  && (
            <div
              className={`content-container ${getTabContentContainerExtraClass(
                TabType.DataSource
              )}`}
            >
              <DataSource
                visible={isRenderTabContent(TabType.DataSource)}
                appCode={dynamicWorkDesignInfo?.applicationCode}
                pageCode={dynamicWorkDesignInfo?.pageCode}
                options={dynamicWorkDesignInfo?.config?.sidebarConfig?.dataSourceOptions}
                dataSources={dynamicWorkDesignRenderData?.dataSources ?? []}
                fieldTreeMap={dynamicWorkDesignRenderData?.fieldTreeMap}
                dataSourceNames={dynamicWorkDesignRenderData?.dataSourceNames}
                interceptDataSourceUpdateOperations={
                  dynamicWorkDesignInfo?.config?.sidebarConfig?.interceptDataSourceUpdateOperations
                }
                modelPageType={
                  dynamicWorkDesignInfo?.dynamicWorkDesignConfig?.businessConfig?.modelPageType
                }
                canvasReady={dynamicWorkDesignStatus === DynamicWorkDesignStatus.Ready}
                dataSourcePluginReady={
                  pluginStatusMap?.get('LcdpDataSourcePanelPlugin') === 'ready'
                }
                // fieldSourceMode={dynamicWorkDesignInfo?.config?.sidebarConfig?.fieldSourceMode}
                queryPlanDataSourceMode={
                  dynamicWorkDesignInfo?.config?.sidebarConfig?.queryPlanDataSourceMode
                }
                syncTab={handleSyncTab}
                changeDataSource={handleChangeDataSource}
                updateDataSource={handleUpdateDataSource}
                changePage={handleChangePage}
                resetPageUIElement={handleResetPageUIElement}
                changePageUIElementCode={handleChangePageUIElementCode}
                updateDataSourceNames={handleUpdateDataSourceNames}
                close={handleDataSourceClose}
              />
            </div>
          )}  
          {isVersionTwo && isRenderTabContent(TabType.DataConnector) && (
            <div className={`content-container ${getTabContentContainerExtraClass(TabType.DataConnector)}`}>
              {
                tabContentIndex === 0 ?
                  (<DataConnectors
                    dataConnectors={dynamicWorkDesignRenderData?.pageUIElementContent?.dataConnectors || []}
                    onChange={handleChangeDataConnectors} />) : (
                    <DataVariables
                      variables={dynamicWorkDesignRenderData?.pageUIElementContent?.variables || []}
                      onChange={handleChangeVariables}
                    />)
              }
            </div>
          )}
          {isRenderTabContent(TabType.Rule) && (
            <div className={`content-container ${getTabContentContainerExtraClass(TabType.Rule)}`}>
              <RuleTab />
            </div>
          )}
          {/* {isRenderTabContent(TabType.PERMISSION) && ( */}
          {/*   <div */}
          {/*     className={`content-container ${getTabContentContainerExtraClass(TabType.PERMISSION)}`} */}
          {/*   > */}
          {/*     <PermissionTab */}
          {/*       actionId={dynamicWorkDesignInfo?.code} */}
          {/*       actionName={dynamicWorkDesignInfo?.name} */}
          {/*       iamConditions={dynamicWorkDesignRenderData?.iamCondition ?? []} */}
          {/*       iamConditionsChange={doIamConditionsChange} */}
          {/*     /> */}
          {/*   </div> */}
          {/* )} */}
        </div>
      </div>
    </div>
  );
};

export default DynamicWorkDesignSideBar;
