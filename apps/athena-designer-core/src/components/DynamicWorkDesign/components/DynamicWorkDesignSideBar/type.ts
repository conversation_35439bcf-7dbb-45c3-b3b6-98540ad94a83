export enum TabType {
  DataSource = 'DataSource',
  /**
   * 纯净数据源
   */
  DataConnector = 'DataConnector',
  // Field = 'Field',
  Widget = 'Widget',
  Rule = 'Rule',
  Hook = 'Hook',
  PERMISSION = 'PERMISSION',
  OUTLINE = 'OUTLINE',
}

export interface SubModule {
  title: string; // 子模块菜单标题key(dj-xx)
  icon: string; // 子模块菜单图标
  contentHideType: 'hide' | 'renderSizeZero' | 'none'; // tab对应内容在不展示时的处理方式（隐藏/渲染尺寸为0/不渲染dom）
  contentRenderType: 'sideBar' | 'lowCode'; // tab对应内容渲染方式（侧边栏/低代码编辑器）
  lowCodePanel?: string; // 对应lowcode中的面板，需要用lowcode渲染的tab才需要配置
  subTabs?: {
    tabName: string // 页签名称
  }[] // 子模块内的tab
}

export enum SaveType {
  Root = 'root', // 存储在root节点上
  Config = 'config', // 存储在config中
}
