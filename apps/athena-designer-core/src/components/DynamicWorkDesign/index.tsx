import React, { forwardRef, useEffect } from 'react';
import './index.less';
import {
  DynamicWorkDesignEventType,
  DynamicWorkDesignInfo,
  DynamicWorkDesignRenderData,
  DynamicWorkDesignRenderWholeData,
  DynamicWorkDesignStatus,
} from './config/type';
import { Spin } from 'antd';
import DynamicWorkDesignSideBar from './components/DynamicWorkDesignSideBar';
import DynamicWorkDesignContent from './components/DynamicWorkDesignContent';
import { useGlobalEventEmitter } from '@/common/hooks';
import useDynamicWorkDesignInit from '@components/DynamicWorkDesign/hooks/business/useDynamicWorkDesignInit';
import useDynamicWorkDesignDataSourceInfo from '@components/DynamicWorkDesign/hooks/business/useDynamicWorkDesignDataSourceInfo';
import useDynamicWorkDesignImperativeHandle from '@components/DynamicWorkDesign/hooks/business/useDynamicWorkDesignImperativeHandle';
import useFunctionDeBounce from './hooks/common/useFunctionDeBounce';
import useDynamicWorkDesignStatus from '@components/DynamicWorkDesign/hooks/business/useDynamicWorkDesignStatus';
import { OpenWindowData, OpenWindowDataContent } from './components/DynamicWorkDesignContent/type';
import { transferNewFiledTreeToOld } from './components/DynamicWorkDesignSideBar/tools';
import { useDynamicWorkDesignStore } from './store';
import { DynamicWorkDesignContext } from './context';
/**
 * 关于DynamicWorkDesign的数据
 * -DynamicWorkDesign 使用了分模块的store
 * -DynamicWorkDesign目录下不考虑对外暴露的业务型组件或者关注点分离的业务hooks可以直接操作store
 * -需要对外暴露，或者通用的组件或者hooks，建议不要直接操作store
 * -此外DynamicWorkDesign具有全局事件以及context
 * -DynamicWorkDesign的全局事件主要处理分散的事件通信交互
 * -具有明确的层级结构的数据通信或者交互建议使用 context
 */

export interface DynamicWorkDesignProps {
  version?: string; //  版本
  isPreload?: boolean; // 是否预加载
  dynamicWorkDesignInfo?: DynamicWorkDesignInfo; // 动态作业设计器的基础信息
  dynamicWorkDesignRenderData?: DynamicWorkDesignRenderData; // 动态作业设计器输入的基础渲染数据
  changeDynamicWorkDesignRenderData: (
    dynamicWorkDesignRenderWholeData: DynamicWorkDesignRenderWholeData
  ) => void; // 动态作业设计器的实际渲染数据发生变化
  changeDynamicWorkDesignStatus: (status: DynamicWorkDesignStatus) => void; // 动态作业设计器的状态发生变化
  // editOpenWindow: (openWindowDataContent: OpenWindowDataContent) => void; // 这是临时逻辑，主要现在lowcode中没有定制开窗组件，以后也不会有，以后将不会再有operations
  dwDesignDispatch?: (eventType: string, data: any) => void;
}

const DynamicWorkDesign = forwardRef((props: DynamicWorkDesignProps, ref) => {
  const {
    version,
    isPreload = false,
    dynamicWorkDesignInfo: dynamicWorkDesignInfoOrigin,
    dynamicWorkDesignRenderData: dynamicWorkDesignRenderDataOrigin,
    changeDynamicWorkDesignRenderData,
    changeDynamicWorkDesignStatus,
    dwDesignDispatch,
    // editOpenWindow,
  } = props;

  const { dynamicWorkDesignInfo } = useDynamicWorkDesignStore((state) => ({
    dynamicWorkDesignInfo: state.dynamicWorkDesignInfo,
  }));

  // 动态作业设计器的状态
  const { dynamicWorkDesignStatus } = useDynamicWorkDesignStatus();
  useEffect(() => {
    changeDynamicWorkDesignStatus(dynamicWorkDesignStatus);
  }, [dynamicWorkDesignStatus]);

  // 对外的change方法
  const { functionDeBounce: changeDynamicWorkDesignRenderDataDeBounce } = useFunctionDeBounce(
    changeDynamicWorkDesignRenderData
  );

  // 初始化逻辑
  useDynamicWorkDesignInit(
    version,
    isPreload,
    dynamicWorkDesignInfoOrigin,
    dynamicWorkDesignRenderDataOrigin,
    changeDynamicWorkDesignRenderDataDeBounce,
    dynamicWorkDesignStatus
  );
  // 数据源相关信息处理
  useDynamicWorkDesignDataSourceInfo();
  // ImperativeHandle
  useDynamicWorkDesignImperativeHandle(
    ref,
    dynamicWorkDesignInfoOrigin,
    dynamicWorkDesignRenderDataOrigin,
    dynamicWorkDesignStatus
  );

  // =======事件处理=======
  // 对外暴露设计器最新的数据，如果正在loading初始化数据，则不暴露，说明正在进行新的数据渲染，之前的数据change已经没有意义了
  // useGlobalEventEmitter(
  //   DynamicWorkDesignEventType.LowCodeChangeDynamicWorkDesignRenderData,
  //   (currentDynamicWorkDesignRenderData: DynamicWorkDesignRenderWholeData) => {
  //     if (dynamicWorkDesignStatus === DynamicWorkDesignStatus.Loading) return;
  //     changeDynamicWorkDesignRenderDataDeBounce(currentDynamicWorkDesignRenderData);
  //   }
  // );

  // useGlobalEventEmitter(
  //   DynamicWorkDesignEventType.LowCodeOpenWindowHandle,
  //   (eventData: OpenWindowData) => {
  //     const { type, data } = eventData;
  //     data.fieldTreeData = transferNewFiledTreeToOld(data.fieldTreeData);
  //     for (const [key, value] of data.fieldTreeDataMap) {
  //       data.fieldTreeDataMap.set(key, transferNewFiledTreeToOld(value));
  //     }
  //     if (type === 'edit') {
  //       editOpenWindow(data);
  //     }
  //   }
  // );

  return (
    <div className="dynamic-work-design">
      <Spin
        className={`dynamic-work-design-loading ${dynamicWorkDesignStatus === DynamicWorkDesignStatus.Ready ? 'hide' : ''} ${!dynamicWorkDesignInfo?.dynamicWorkDesignConfig?.commonConfig?.isShowLoading ? 'hide-logo' : ''}`}
      ></Spin>
      <DynamicWorkDesignContext.Provider value={{ dwDesignDispatch }}>
        <DynamicWorkDesignSideBar />
      </DynamicWorkDesignContext.Provider>
      <DynamicWorkDesignContent />
    </div>
  );
});

export default DynamicWorkDesign;
