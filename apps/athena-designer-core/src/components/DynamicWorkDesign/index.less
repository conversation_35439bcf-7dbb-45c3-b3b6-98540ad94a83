@import 'src/assets/global.less';
// TODO: 当前版本 官方提供的 多层子应用方案（tag），依然存在问题，先注释还原，待研究，如果后续版本或者通过其他方案解决，可以移除以下逻辑
// 因为多层micro-app 嵌套问题，导致第二次加载时，第二层里面的less文件会丢失，所以这里直接引入，让第一层 name=business-share 接管
@import 'src/components/PlatformIconModal/index.less';
@import 'src/components/PlatformIconModal/components/index.less';
@import 'src/components/ReportAndBasicDataInputModal/index.less';
@import 'src/components/MonacoEditor/HooksComprehensiveEditor.less';
@import 'src/components/WorkflowSelector/index.less';
@import 'src/components/SubpageSelector/index.less';
@import 'src/components/ActionParams/index.less';
@import 'src/components/ActionParams/components/index.less';
@import 'src/components/OperatePermission/index.less';
@import 'src/components/OperatePermission/SelectPermission.less';
@import 'src/components/MonacoEditor/index.less';
@import 'src/components/MonacoEditor/HooksComprehensiveEditor.less';
@import 'src/components/MonacoEditor/components/ScriptTips/index.less';
@import 'src/components/MonacoEditor/components/Toolbar/index.less';
@import 'src/components/MonacoEditor/components/HooksTemplate/index.less';
@import 'src/components/MonacoEditor/components/HooksForm/DataHooksForm.less';
@import 'src/components/MonacoEditor/components/HooksForm/FieldTreeNode.less';
@import 'src/components/MonacoEditor/components/HooksForm/FormTooltipLabel.less';
@import 'src/components/MonacoEditor/components/HooksForm/index.less';
@import 'src/components/TagEditmodal/index.less';
@import 'src/components/TagEditmodal/select-integration-input/index.less';
@import 'src/components/DynamicWorkDesign/components/DynamicWorkDesignSideBar/PermissionTab/index.less';
@import 'src/components/OperatePermission/PermissionFormModal.less';
@import 'src/components/OpenwindowWorkDesign/index.less';
@import 'src/components/OpenwindowWorkDesign/components/OpenwindowWorkDesignSidebar/index.less';
@import 'src/components/OpenwindowWorkDesign/components/OpenwindowDesign/index.less';
@import 'src/components/OpenwindowWorkDesign/components/OpenwindowDesign/components/OpenwindowColumnSetting/index.less';
@import 'src/components/OpenwindowWorkDesign/components/OpenwindowDesign/components/OpenwindowComponentSetting/index.less';
@import 'src/components/OpenwindowWorkDesign/components/OpenwindowDesign/components/OpenwindowComponentSetting/components/SelectComponentSetting/CustomOptions.less';
@import 'src/components/OpenwindowWorkDesign/components/OpenwindowDesign/components/OpenwindowComponentSetting/components/SelectComponentSetting/MultiTypeInput.less';
@import 'src/components/OpenwindowWorkDesign/components/OpenwindowDesign/components/OpenwindowComponentSetting/components/SelectComponentSetting/OptionModal.less';
@import 'src/components/OpenwindowWorkDesign/components/OpenwindowDesign/components/OpenwindowDesignComponents/index.less';
@import 'src/components/OpenwindowWorkDesign/components/OpenwindowDesign/components/OpenwindowSetting/index.less';
@import 'src/components/OpenwindowWorkDesign/components/OpenwindowDesign/components/TableSetting/index.less';
@import 'src/components/OpenwindowWorkDesignLib/index.less';
@import 'src/components/OpenwindowWorkDesignLib/OpenwindowDesign/index.less';
@import 'src/components/OpenwindowWorkDesignLib/OpenwindowDesign/components/OpenwindowColumnSetting/index.less';
@import 'src/components/OpenwindowWorkDesignLib/OpenwindowDesign/components/OpenwindowComponentSetting/index.less';
@import 'src/components/OpenwindowWorkDesignLib/OpenwindowDesign/components/OpenwindowComponentSetting/components/SelectComponentSetting/CustomOptions.less';
@import 'src/components/OpenwindowWorkDesignLib/OpenwindowDesign/components/OpenwindowComponentSetting/components/SelectComponentSetting/MultiTypeInput.less';
@import 'src/components/OpenwindowWorkDesignLib/OpenwindowDesign/components/OpenwindowComponentSetting/components/SelectComponentSetting/OptionModal.less';
@import 'src/components/OpenwindowWorkDesignLib/OpenwindowDesign/components/OpenwindowDesignComponents/index.less';
@import 'src/components/OpenwindowWorkDesignLib/OpenwindowDesign/components/OpenwindowSetting/index.less';
@import 'src/components/OpenwindowWorkDesignLib/OpenwindowDesign/components/TableSetting/index.less';
// ...
// 结束

.dynamic-work-design {
  position: relative;
  width: 100%;
  height: 100%;
  .dynamic-work-design-loading {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999;
    background: rgba(255, 255, 255, 1);
    & > .ant-spin-dot {
      position: absolute;
      top: calc(50% - 20px);
      left: calc(50% - 20px);
    }
    transition: all 0.3s ease-out;

    &.hide-logo {
      opacity: 0;
    }

    &.hide {
      opacity: 0;
      pointer-events: none;
    }
  }

  display: flex;
}
