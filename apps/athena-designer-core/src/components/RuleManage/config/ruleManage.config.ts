// 迁移之前的规则配置
const RULE_CLASS = [
  { key: 'validate', title: 'dj-校验' },
  { key: 'style', title: 'dj-样式' },
  { key: 'set', title: 'dj-赋值' },
];

const BASE_RULE_TYPE_OPTION_CLASS = [
  { key: 'required', desc: 'dj-必填1', parent: 'validate', linkageSchemas: true },
  { key: 'minLength', desc: 'dj-最小长度', parent: 'validate', parameter: true },
  { key: 'maxLength', desc: 'dj-最大长度', parent: 'validate', parameter: true },
  { key: 'min', desc: 'dj-最小值', parent: 'validate', linkageSchemas: true, parameter: true },
  { key: 'max', desc: 'dj-最大值', parent: 'validate', linkageSchemas: true, parameter: true },
  {
    key: 'pattern',
    desc: 'dj-正则表达式',
    parent: 'validate',
    linkageSchemas: true,
    parameter: true,
  },
  { key: 'repeat', desc: 'dj-数据重复检查', parent: 'validate', linkageSchemas: true },
  // {
  //   key: 'custom',
  //   desc: 'dj-使用js来撰写逻辑',
  //   parent: 'validate',
  //   linkageSchemas: true,
  //   crossFieldOrNot: true,
  // },
  { key: 'color', desc: 'dj-颜色控制', parent: 'style' },
  { key: 'disabled', desc: 'dj-禁止用户操作', parent: 'style', linkageSchemas: true },
  { key: 'style', desc: 'dj-样式控制，目前仅控制color', parent: 'style' },
  // { key: 'hidden', desc: 'dj-隐藏控件', parent: 'style', linkageSchemas: true },
  { key: 'value', desc: 'dj-赋值', parent: 'set', linkageSchemas: true },
  { key: 'defaultValue', desc: 'dj-赋默认值，只会执行一次', parent: 'set' },
  {
    key: 'rowDefaultValue',
    desc: 'dj-行默认值，只存在于表格中',
    parent: 'set',
    valueScript: true,
  },
  // {
  //   key: 'connection',
  //   desc: 'dj-联动控制，操作组件的属性和方法',
  //   parent: 'set',
  //   relationIsLinkage: true,
  //   relations: true,
  // },
  // { key: 'executeScript', desc: 'dj-执行脚本', parent: 'set' },
];

// 移动所需规则
const MOBILE_BASE_RULE_TYPE_OPTION_CLASS = [
  { key: 'required', desc: 'dj-必填1', parent: 'validate', linkageSchemas: true },
  { key: 'minLength', desc: 'dj-最小长度', parent: 'validate', parameter: true },
  { key: 'maxLength', desc: 'dj-最大长度', parent: 'validate', parameter: true },
  { key: 'min', desc: 'dj-最小值', parent: 'validate', linkageSchemas: true, parameter: true },
  { key: 'max', desc: 'dj-最大值', parent: 'validate', linkageSchemas: true, parameter: true },
  {
    key: 'pattern',
    desc: 'dj-正则表达式',
    parent: 'validate',
    linkageSchemas: true,
    parameter: true,
  },
  {
    key: 'custom',
    desc: 'dj-使用js来撰写逻辑',
    parent: 'validate',
    linkageSchemas: true,
    crossFieldOrNot: true,
  },
  { key: 'disabled', desc: 'dj-禁止用户操作', parent: 'style', linkageSchemas: true },
  { key: 'hidden', desc: 'dj-隐藏控件', parent: 'style', linkageSchemas: true },
  { key: 'value', desc: 'dj-赋值', parent: 'set', linkageSchemas: true },
  { key: 'defaultValue', desc: 'dj-赋默认值，只会执行一次', parent: 'set' },
  {
    key: 'connection',
    desc: 'dj-联动控制，操作组件的属性和方法',
    parent: 'set',
    relationIsLinkage: true,
    relations: true,
  },
];

export { RULE_CLASS, BASE_RULE_TYPE_OPTION_CLASS, MOBILE_BASE_RULE_TYPE_OPTION_CLASS };
