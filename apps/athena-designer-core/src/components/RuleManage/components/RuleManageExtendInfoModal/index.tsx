import React, { useEffect, useState } from 'react';
import { useRuleManageExtendInfoModalStore, useRuleManageStore } from '../../store';
import { t } from 'i18next';
import { Button, Flex, Form, Input, Modal, Select, Tooltip, message } from 'antd';
import Icon from '@/components/Icon';
import './index.less';
import { saveExtendField, getAllData, deleteExtendField } from '../../api/request';
import AuthWrapper from '@/components/AuthWrapper';
import { ResourceType } from '@/common/utils/auth-util';

interface RuleManageExtendInfoModalProps {}
const RuleManageExtendInfoModal: React.FC<RuleManageExtendInfoModalProps> = () => {
  const [infoList, setInfoList] = useState<any[]>([]);
  const [selectedIndex, setSelectedIndex] = useState<number>(0);
  const [example, setExample] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [form] = Form.useForm();

  const { isOpen, setIsOpen, primaryKey, setPrimaryKey } = useRuleManageExtendInfoModalStore(
    (state) => ({
      isOpen: state.isOpen,
      setIsOpen: state.setIsOpen,
      primaryKey: state.primaryKey,
      setPrimaryKey: state.setPrimaryKey,
    })
  );

  const { ruleManageOriginData } = useRuleManageStore((state) => ({
    ruleManageOriginData: state.ruleManageOriginData,
  }));

  useEffect(() => {
    updateInfoList();
  }, [primaryKey]);

  useEffect(() => {
    const { name, jsonPath, value, valueType } = infoList[selectedIndex] ?? {};
    form.resetFields();
    form.setFieldsValue({ name, jsonPath, value, valueType });
  }, [infoList, selectedIndex]);

  // 更新列表
  const updateInfoList = async () => {
    if (!primaryKey) return;
    try {
      // 历史逻辑的参数
      const params = {
        primaryKey: primaryKey,
        sceneCode: 'businessRule',
        secondKey: null,
      };
      setIsLoading(true);
      const { data: resData } = await getAllData(params);
      if (resData.code === 0) {
        setExample(resData?.data?.example ?? '');
        setInfoList(resData?.data?.extendFieldList ?? []);
        setSelectedIndex(0);
      } else {
        message.error(resData.msg);
      }
    } catch (error) {
      message.error(error?.response?.data?.msg ?? error.message);
    }
    setIsLoading(false);
  };

  // 新增扩展信息
  const handleAdd = () => {
    setSelectedIndex(infoList.length);
  };

  const handleEdit = (index) => {
    if (!infoList[selectedIndex]) {
      Modal.confirm({
        content: t('dj-当前配置未保存，是否切换？'),
        style: { height: '150px' },
        onOk: async () => {
          setSelectedIndex(index);
        },
      });
      return;
    }
    setSelectedIndex(index);
  };

  const handleDelete = (code) => {
    Modal.confirm({
      content: t('dj-确认删除？'),
      style: { height: '150px' },
      onOk: async () => {
        try {
          setIsLoading(true);
          const { data: resData } = await deleteExtendField({ code: code });
          if (resData.code === 0) {
            message.success(t('dj-删除成功'));
            updateInfoList();
          } else {
            message.error(resData.msg);
          }
        } catch (error) {
          message.error(error?.response?.data?.msg ?? error.message);
        }
        setIsLoading(false);
      },
    });
  };

  const handlecCancel = () => {
    setIsOpen(false);
  };

  const handleSave = async () => {
    try {
      await form.validateFields();
      const formValue = await form.getFieldsValue();

      // 如果oldData不存在，说明是新增
      const baseData = infoList[selectedIndex] ?? {
        application: ruleManageOriginData.applicationCode,
        example,
        primaryKey,
        sceneCode: 'businessRule',
      };

      const params = { ...baseData, ...formValue };
      setIsLoading(true);
      const { data: resData } = await saveExtendField(params);

      if (resData.code === 0) {
        message.success(t('dj-保存成功'));
        updateInfoList();
      } else {
        message.error(resData.msg);
      }
    } catch (error) {
      const messageText =
        error?.response?.data?.msg ??
        error?.message ??
        error?.values?.content?.errorMessage ??
        t('dj-保存失败');
      message.error(messageText);
    }
    setIsLoading(false);
  };

  const handleReset = () => {
    setPrimaryKey('');
  };

  return (
    <Modal
      open={isOpen}
      title={t('dj-扩展信息配置')}
      footer={null}
      onCancel={handlecCancel}
      maskClosable={false}
      destroyOnClose={true}
      width={1000}
      className="rule-manage-extend-info-modal"
      afterClose={handleReset}
    >
      <div className="rule-manage-extend-info-modal-content">
        <div className="info-list">
          <div className="info-list-add" onClick={handleAdd}>
            <Icon type="iconshulidejiahao" />
            <span>{t('dj-添加')}</span>
          </div>
          <div className="info-list-content">
            {infoList?.map((item, index) => {
              return (
                <div
                  key={item.code}
                  className={`info-list-content-item ${index === selectedIndex ? 'selected' : ''}`}
                >
                  <span className="extend-name" onClick={() => handleEdit(index)}>
                    {item.name}
                  </span>
                  <Icon
                    className="extend-delete"
                    type="iconxiaoliebiaoshanchu"
                    onClick={() => handleDelete(item.code)}
                  />
                </div>
              );
            })}
            {!infoList[selectedIndex] && (
              <div key="new" className="info-list-content-item selected">
                <span className="extend-name">{t('dj-新增扩展信息')}</span>
              </div>
            )}
          </div>
        </div>
        <div className="info-form">
          <Form
            labelCol={{ span: 5 }}
            wrapperCol={{ span: 19 }}
            form={form}
            autoComplete="off"
            initialValues={{}}
            className="info-form-content"
          >
            <Form.Item
              name="name"
              label={t('dj-配置名称')}
              colon={false}
              rules={[
                {
                  required: true,
                  message: t('dj-必填'),
                },
              ]}
            >
              <Input disabled={false} placeholder={t('dj-请输入')} />
            </Form.Item>
            <Form.Item
              name="jsonPath"
              label={t('dj-字段存储路径')}
              colon={false}
              rules={[
                {
                  required: true,
                  message: t('dj-必填'),
                },
              ]}
            >
              <Input disabled={false} placeholder={t('dj-请输入')} />
            </Form.Item>
            <Form.Item
              name="valueType"
              label={t('dj-扩展字段数据类型')}
              colon={false}
              rules={[
                {
                  required: true,
                  message: t('dj-必填'),
                },
              ]}
            >
              <Select
                className="content-fields-item-field-select"
                size="small"
                options={[
                  { value: 'Number', label: 'Number' },
                  { value: 'String', label: 'String' },
                  { value: 'Map', label: 'Object' },
                  { value: 'Boolean', label: 'Boolean' },
                ]}
              />
            </Form.Item>
            <Form.Item
              name="value"
              label={t('dj-扩展内容')}
              colon={false}
              rules={[
                {
                  required: true,
                  message: t('dj-必填'),
                },
              ]}
            >
              <Input disabled={false} placeholder={t('dj-请输入')} />
            </Form.Item>
            <Tooltip title={example} className="info-form-content-tooltip">
              <span>{t('dj-参考示例')}</span>
              <Icon type="iconexplain"></Icon>
            </Tooltip>
          </Form>
          <Flex
            className="info-form-foot-bar"
            gap="large"
            wrap="wrap"
            justify="center"
            align="center"
          >
            {/*是否隐藏权限项*/}
            {
              !ruleManageOriginData?.hideAuth ? (
                <AuthWrapper
                  authParams={{
                    action: 'update',
                  }}
                >
                  <Button type="primary" loading={isLoading} onClick={handleSave}>
                    {t('dj-保存')}
                  </Button>
                </AuthWrapper>
              ) : <Button type="primary" loading={isLoading} onClick={handleSave}>
                {t('dj-保存')}
              </Button>
            }
          </Flex>
        </div>
      </div>
    </Modal>
  );
};

export default RuleManageExtendInfoModal;
