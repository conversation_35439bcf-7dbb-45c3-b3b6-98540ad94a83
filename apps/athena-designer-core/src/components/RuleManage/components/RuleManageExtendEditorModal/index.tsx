import React, { useEffect, useState } from 'react';
import JsonEditor, { ContentErrors, JSONValue } from '@/components/JsonEditor';
import { Button, Flex, Modal, message } from 'antd';
import {
  useRuleManageExtendEditorModalStore,
  useRuleManageStore,
  useRuleManageRuleListStore,
} from '../../store';
import { t } from 'i18next';
import './index.less';
import { globalEventEmitter } from '@/common/hooks';
import { saveRule } from '../../api/request';
import AuthWrapper from '@/components/AuthWrapper';
import { ResourceType } from '@/common/utils/auth-util';
import { RuleManageEventType } from '../../config/ruleManage.type';

export interface RuleManageExtendEditorModalProps {}
const RuleManageExtendEditorModal: React.FC<RuleManageExtendEditorModalProps> = (
  props: RuleManageExtendEditorModalProps
) => {
  const [value, setValue] = useState<any>();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const { isOpen, setIsOpen, jsonValue, setJsonValue } = useRuleManageExtendEditorModalStore(
    (state) => ({
      isOpen: state.isOpen,
      setIsOpen: state.setIsOpen,
      jsonValue: state.jsonValue,
      setJsonValue: state.setJsonValue,
    })
  );

  const { ruleManageOriginData } = useRuleManageStore((state) => ({
    ruleManageOriginData: state.ruleManageOriginData,
  }));

  const { ruleList, setRuleList } = useRuleManageRuleListStore((state) => ({
    ruleList: state.ruleList,
    setRuleList: state.setRuleList,
  }));

  useEffect(() => {
    setValue(jsonValue);
  }, [jsonValue]);

  const onChange = (error: ContentErrors, value: JSONValue) => {
    if (error) return;
    setValue(value);
  };

  const handleSave = async () => {
    if (!value) {
      message.error(t('dj-内容不可为空'));
      return;
    }

    const params = {
      ...value,
    };

    if (ruleManageOriginData.isOffline) {
      setIsOpen(false);
      const findIndex = ruleList.findIndex((rule) => {
        return rule.key === params.key;
      });

      if (findIndex >= 0) {
        message.success(t('dj-保存成功'));
        const newRuleList = [...ruleList];
        newRuleList.splice(findIndex, 1, params);
        setRuleList(newRuleList);
        return;
      }

      message.error(t('dj-保存失败'));
      return;
    }

    try {
      setIsLoading(true);
      const { data: resData } = await saveRule(params);
      if (resData.code === 0) {
        message.success(t('dj-保存成功'));
        setIsOpen(false);
        globalEventEmitter.emit(RuleManageEventType.ruleManageReloadList);
      } else {
        message.error(resData.msg);
      }
    } catch (error) {
      const messageText =
        error?.response?.data?.msg ??
        error?.message ??
        error?.values?.content?.errorMessage ??
        t('dj-保存失败');
      message.error(messageText);
    }
    setIsLoading(false);
  };

  const handlecCancel = () => {
    setIsOpen(false);
  };

  const handleReset = () => {
    setJsonValue({});
  };

  return (
    <Modal
      open={isOpen}
      title={t('dj-JSON设置')}
      footer={null}
      maskClosable={false}
      onCancel={handlecCancel}
      destroyOnClose={true}
      closable={false}
      width={640}
      className="rule-manage-extend-editor-modal"
      afterClose={handleReset}
    >
      <Flex className="top-bar" gap="large" wrap="wrap" justify="right">
        <Button onClick={handlecCancel}>{t('dj-取消')}</Button>
        {/*是否隐藏权限项*/}
        {
          !ruleManageOriginData?.hideAuth ? (
            <AuthWrapper
              authParams={{
                action: 'update',
              }}
            >
              <Button type="primary" loading={isLoading} onClick={handleSave}>
                {t('dj-确定')}
              </Button>
            </AuthWrapper>
          ) : <Button type="primary" loading={isLoading} onClick={handleSave}>
            {t('dj-确定')}
          </Button>
        }
      </Flex>
      <JsonEditor json={jsonValue} onChange={onChange} />
    </Modal>
  );
};

export default RuleManageExtendEditorModal;
