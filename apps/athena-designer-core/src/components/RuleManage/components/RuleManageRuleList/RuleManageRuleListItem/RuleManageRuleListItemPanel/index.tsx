import React from 'react';
import { t } from 'i18next';
import {
  useRuleManageExtendEditorModalStore,
  useRuleManageContentModalStore,
  useRuleManageExtendInfoModalStore,
  useRuleManageStore
} from '../../../../store';
import './index.less';
import { Rule } from '../../../../config/ruleManage.type';
import AuthWrapper from '@/components/AuthWrapper';
// import { ResourceType } from '@/common/utils/auth-util';
import useCommonRuleOperation from '@/components/RuleManage/hooks/useCommonRuleOperation';

interface RuleManageRuleListItemPanelProps {
  rule: Rule;
  onClose: () => void;
}
const RuleManageRuleListItemPanel: React.FC<RuleManageRuleListItemPanelProps> = (
  props: RuleManageRuleListItemPanelProps
) => {
  const { ruleManageOriginData } = useRuleManageStore((state) => ({
    ruleManageOriginData: state.ruleManageOriginData,
  }));

  const { rule, onClose } = props;
  const { setIsOpen: extendEditorModalSetIsOpen, setJsonValue: extendEditorModalSetJsonValue } =
    useRuleManageExtendEditorModalStore((state) => ({
      isOpen: state.isOpen,
      setIsOpen: state.setIsOpen,
      setJsonValue: state.setJsonValue,
    }));

  const {
    setIsOpen: contentModalSetIsOpen,
    setType: contentModalSetType,
    setRuleData: contentModalSetRuleData,
  } = useRuleManageContentModalStore((state) => ({
    setIsOpen: state.setIsOpen,
    setType: state.setType,
    setRuleData: state.setRuleData,
  }));

  const { setIsOpen: extendInfoModalSetIsOpen, setPrimaryKey: extendInfoModalSetPrimaryKey } =
    useRuleManageExtendInfoModalStore((state) => ({
      setIsOpen: state.setIsOpen,
      setPrimaryKey: state.setPrimaryKey,
    }));

  const { deleteRule } = useCommonRuleOperation();

  // 打开规则的json编辑面板
  const handleOpenExtendEditorModal = (rule: Rule) => {
    extendEditorModalSetJsonValue(rule);
    extendEditorModalSetIsOpen(true);
    onClose();
  };

  // 删除规则
  const handleDelete = (rule) => {
    deleteRule(rule);
  };

  const handleOpenContentModal = (rule) => {
    contentModalSetType('edit');
    contentModalSetRuleData(rule);
    contentModalSetIsOpen(true);
    onClose();
  };

  // 打开扩展信息
  const handleOpenExtendInfoModal = async (rule) => {
    extendInfoModalSetPrimaryKey(rule.key);
    extendInfoModalSetIsOpen(true);
    onClose();
  };

  return (
    <ul className="rule-manage-rule-list-item-panel">
      <li
        onClick={() => {
          handleOpenExtendEditorModal(rule);
        }}
      >
        <span>{t('dj-扩展')}</span>
      </li>
      {/*是否隐藏权限项*/}
      {
        !ruleManageOriginData?.hideAuth ? (
          <AuthWrapper
            authParams={{
              action: 'update',
            }}
          >
            <li
              onClick={() => {
                handleDelete(props.rule);
              }}
            >
              <span>{t('dj-删除')}</span>
            </li>
          </AuthWrapper>
        ) : <li
          onClick={() => {
            handleDelete(props.rule);
          }}
        >
          <span>{t('dj-删除')}</span>
        </li>
      }
      <li
        onClick={() => {
          handleOpenContentModal(rule);
        }}
      >
        <span>{t('dj-编辑')}</span>
      </li>
      {
        !ruleManageOriginData?.hideShortcut && <li
          onClick={() => {
            handleOpenExtendInfoModal(rule);
          }}
        >
          <span>{t('dj-扩展信息')}</span>
        </li>
      }
    </ul>
  );
};

export default RuleManageRuleListItemPanel;
