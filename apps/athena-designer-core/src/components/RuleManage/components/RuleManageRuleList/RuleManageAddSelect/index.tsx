import React, { useContext } from 'react';
import './index.less';
import { Tabs } from 'antd';
import type { TabsProps } from 'antd';
import RuleTabContent from './RuleTabContent';
import TemplateTabContent from './TemplateTabContent';
import { t } from 'i18next';
import Icon from '@/components/Icon';
import { ToTemplateManager } from '../../../context';

interface RuleManageAddSelectProps {
  //新增规则的基础信息，新增规则时（除模版与收藏），会组装到生成的规则数据中，主要场景是，比如从界面设计器中新增规则，默认会带上 path和schema
  addRuleBaseInfo?: any;
  contextDataSourceName?: string;
  hideShortcut?: boolean;
}
const RuleManageAddSelect: React.FC<RuleManageAddSelectProps> = (
  props: RuleManageAddSelectProps
) => {
  const { addRuleBaseInfo, contextDataSourceName, hideShortcut } = props;
  const toTemplateManager = useContext(ToTemplateManager);
  const hiddenTabs = hideShortcut ? ['private', 'public'] : [];
  const items: TabsProps['items'] = [
    {
      key: 'rule',
      label: t('dj-规则'),
      children: (
        <RuleTabContent
          addRuleBaseInfo={addRuleBaseInfo}
          contextDataSourceName={contextDataSourceName}
        ></RuleTabContent>
      ),
    },
    {
      key: 'private',
      label: t('dj-我的模板'),
      children: (
        <TemplateTabContent
          type="mine"
          contextDataSourceName={contextDataSourceName}
        ></TemplateTabContent>
      ),
    },
    {
      key: 'public',
      label: t('dj-其他模板'),
      children: (
        <TemplateTabContent
          type="other"
          contextDataSourceName={contextDataSourceName}
        ></TemplateTabContent>
      ),
    },
  ].filter(s => !hiddenTabs.includes(s.key));

  const handleToTemplateManager = () => {
    toTemplateManager();
  };

  return (
    <div className="rule-manage-add-select">
      <Tabs className="select-tab" defaultActiveKey="rule" items={items} />
      {
        !hideShortcut && <div className="template-button" onClick={handleToTemplateManager}>
          {t('dj-模板管理')}
          <Icon className="item-icon" type="iconbackMenu"/>
        </div>
      }
    </div>
  );
};

export default RuleManageAddSelect;
