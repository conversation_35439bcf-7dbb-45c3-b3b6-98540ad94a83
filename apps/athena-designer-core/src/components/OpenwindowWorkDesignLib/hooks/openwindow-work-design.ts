import { useMemo } from 'react';
import { TransformCallbackFN } from '@components/DataSource/types/syncTabs';
import { isArray } from 'lodash';
import { t } from 'i18next';

function useGetter<T>(callback: TransformCallbackFN<T>, deps: unknown[]): T {
  const transformData: T = useMemo(() => {
    return callback(...deps);
  }, [...deps]);

  return transformData;
}

export type OpenWindowDataSourceType = 'ESP' | 'SD' | 'QUERYPLAN';

export interface FORMAT_NODE {
  arr: any | any[];
  fullPath: boolean;
  prefix?: string;
  onlyShowFirstLayer: boolean;
  layer?: number;
}

export { useGetter };
