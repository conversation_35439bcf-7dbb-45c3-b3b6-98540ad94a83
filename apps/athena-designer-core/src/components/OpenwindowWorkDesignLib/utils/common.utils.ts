/**
 * 将数组中的元素从 fromIndex 移动到 toIndex（直接修改原数组）
 * @param array 目标数组
 * @param fromIndex 起始位置
 * @param toIndex 目标位置
 */
export function moveItemInArray<T>(array: T[], fromIndex: number, toIndex: number): void {
  const from = clamp(fromIndex, array.length - 1);
  const to = clamp(toIndex, array.length - 1);

  if (from === to) {
    return;
  }

  const target = array[from]; // 保存待移动的元素
  const delta = to < from ? -1 : 1; // 移动方向：左移(-1)或右移(1)

  // 逐步覆盖元素，腾出目标位置
  for (let i = from; i !== to; i += delta) {
    array[i] = array[i + delta];
  }

  array[to] = target; // 将元素放入目标位置
}

/** 限制数值在 [0, max] 范围内 */
function clamp(value: number, max: number): number {
  return Math.max(0, Math.min(max, value));
}
