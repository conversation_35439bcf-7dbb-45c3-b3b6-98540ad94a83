.openwindow-work-design-drawer {
  .ant-drawer-content-wrapper {
    width: 96% !important;
  }
  .ant-drawer-body {
    padding: 0 !important;
    .ant-spin-nested-loading {
      height: 100%;
    }
  }
  .work-design {
    height: 100%;
    overflow: hidden;
    border-top: 1px solid #eeeeee;
    .work-header {
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      .header-title {
        flex: 1;
        padding-left: 10px;
        font-size: 15px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: calc(50% - 96px);
        color: rgba(0, 0, 0, 0.85);
      }
      .header-title-tpl {
        flex: 1;
        font-size: 15px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: rgba(0, 0, 0, 0.85);
      }
      .header-menu {
        flex: 1;
        flex-grow: 0;
        .table-tab-list {
          width: 100%;
          height: 28px;
          border-radius: 4px;
          display: flex;
          border: 1px solid #aab1d5;
          .tab {
            height: 100%;
            line-height: 28px;
            text-align: center;
            flex: 1;
            width: 96px;
            font-size: 12px;
            color: #6868ae;
            cursor: pointer;
            text-overflow: ellipsis;
            overflow: hidden;
            word-wrap: break-word;
            word-break: break-all;
            white-space: nowrap;
            &:not(:first-of-type) {
              border-left: 1px solid #aab1d5;
            }
            &.active {
              background-color: #d6d5ec;
              font-weight: bold;
              color: #383878;
            }
          }
        }
      }
      .header-btn {
        flex: 1;
        text-align: right;
        padding-right: 20px;
        .closeIntro {
          margin-left: 20px;
          color: rgba(0, 0, 0, 0.45);
          font-size: 12px;
        }
        .more {
          font-size: 20px;
          font-weight: bolder;
          margin-left: 10px;
        }
      }
      .primary {
        width: 76px;
        height: 28px;
        color: #fff !important;
        background: #6868ae;
        &:hover {
          background: #5d5d9c !important;
        }
      }
      .disabled {
        width: 76px;
        height: 28px;
        cursor: not-allowed !important;
        border-color: #d9d9d9 !important;
        color: rgba(0, 0, 0, 0.25) !important;
        background: rgba(0, 0, 0, 0.04) !important;
        box-shadow: none !important;
      }
    }
    .work-body {
      border-top: 1px solid #eeeeee;
      height: calc(100% - 42px);
      position: relative;
      display: flex;
    }
    .multi-terminal {
      display: flex;
      justify-content: center;
      margin-left: 20px;
    }
    .systermIcon {
      padding: 3px 10px;

      i {
        font-size: 16px;
        color: rgb(148, 148, 148);
      }
    }
  }

  .ant-select-selector {
    height: 28px !important;
    .ant-select-selection-overflow {
      // margin-top: -5px !important;
    }
    .ant-select-selection-item {
      font-size: 12px !important;
      height: 22px !important;
      line-height: 22px !important;
    }
    .ant-select-arrow {
      font-size: 10px !important;
    }
  }

  .ant-select-tree-title {
    font-size: 12px !important;
  }

  #appInputWrapper .ant-input-affix-wrapper {
    height: 28px;
  }

  .ant-checkbox-wrapper .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #6868ae !important;
    border-color: #6868ae !important;
  }

  .ant-checkbox-checked::after {
    // background-color: #6868ae !important;
  }

  .ant-tree-select-dropdown .ant-select-tree-checkbox-checked .ant-select-tree-checkbox-inner {
    background-color: #6868ae !important;
    border-color: #6868ae !important;
  }

  .ant-tree-select-dropdown
    .ant-select-tree-checkbox-indeterminate
    .ant-select-tree-checkbox-inner:after {
    background-color: #6868ae !important;
  }

  .ant-input-outlined:hover {
    border-color: #9175ff !important;
  }

  .ant-select-selector:hover {
    border-color: #9175ff !important;
  }

  .ant-btn-default:hover {
    border-color: #9175ff !important;
  }

  .ant-checkbox-wrapper.ant-checkbox-wrapper-in-form-item input[type='checkbox']:hover {
    border-color: #9175ff !important;
  }

  .ant-radio-wrapper .ant-radio-checked .ant-radio-inner {
    background-color: #6868ae !important;
  }
}
