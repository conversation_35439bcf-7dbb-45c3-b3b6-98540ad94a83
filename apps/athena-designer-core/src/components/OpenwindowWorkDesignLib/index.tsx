import { <PERSON><PERSON>, <PERSON>er, <PERSON><PERSON>, Spin, Tooltip } from 'antd';
import React, { forwardRef, useEffect, useRef, useState } from 'react';
import MFHOC from '../MFHOC';
import { useActiveMenu } from './store/activeMenu';
import { useWorkData } from './store/workData';
import { t } from 'i18next';
import './index.less';
import { Icon } from '@/components';
import OpenwindowDesign from './OpenwindowDesign';
import { MonacoEditor } from '@components/MonacoEditor';
import { SupportedEditorType } from '@components/MonacoEditor/enum';
import { cloneDeep, isEmpty, isEqual } from 'lodash';
import Operation from 'antd/lib/transfer/operation';

interface IOpenwindowWorkDesignLibProps {
  workVisible: boolean;
  workData: any;
  isPrivatization: boolean;
  close: () => void;
  onOk: (operation: any, fields: any[]) => void;
}

export const OpenwindowWorkDesignLib = forwardRef<any>(
  (props: IOpenwindowWorkDesignLibProps, ref) => {
    const [loading, setLoading] = useState<boolean>(false); // 页面loading
    const [designLoading, setDesignLoading] = useState<boolean>(false); // 界面设计loading
    const { activeMenu, initActiveMenu } = useActiveMenu((state) => state);
    const menus: any[] = [
      {
        name: t('dj-界面设计'),
        code: 'design',
        icon: 'iconjiemiansheji',
      },
      {
        name: t('dj-代码'),
        code: 'pageView',
        icon: 'iconkuozhan',
      },
    ];
    const [showPageView, setShowPageView] = useState<boolean>(false); // 界面配置扩展
    const [showConfirm, setShowConfirm] = useState<boolean>(false); // 关闭再确认框

    // 开窗代码
    const {
      initWorkData,
      workData,
      activeData,
      cloneActiveData,
      updateActiveData,
      openWindowDefine,
      updateOpenWindowDefine,
      fields,
      updateFields,
    } = useWorkData((state) => state);

    /**
     * 初始化构建数据
     */
    useEffect(() => {
      if (props.workVisible) {
        console.log('lib 开窗数据', props);
        initWorkData(props.workData);
        designRef.current?.setOpenwindowSelectedInfo();
      }
    }, [props.workData, props.workVisible]);

    const { workVisible, isPrivatization, close, onOk } = props;

    /**
     * 关闭抽屉
     */
    const handleClose = () => {
      if (!isEqual(activeData(), cloneActiveData) && openWindowDefine()?.dataConnectorId) {
        // 存在内容修改，打开再确认开窗
        setShowConfirm(true);
      } else {
        close();
      }
    };

    /**
     * 关闭：继续编辑
     */
    const handleContinue = (): void => {
      setShowConfirm(false);
    };

    /**
     * 关闭：直接关闭
     */
    const handleSureClose = (): void => {
      setShowConfirm(false);
      close();
    };

    /**
     * 关闭：保存并退出
     */
    const handleSureSave = (): void => {
      setShowConfirm(false);
      handleSave();
    };

    /**
     * 保存
     */
    const handleSave = async () => {
      if (designRef?.current) {
        if (!(await designRef.current?.handleValidSetting())) {
          return;
        }
      }
      const newOpenWindowDefine = cloneDeep(openWindowDefine());
      if (isEmpty(openWindowDefine()?.title)) {
        newOpenWindowDefine['title'] = '开窗';
        newOpenWindowDefine['lang'] = {
          title: {
            en_US: 'open window',
            zh_CN: '开窗',
            zh_TW: '開窗',
          },
        };
        updateOpenWindowDefine(newOpenWindowDefine);
      }
      onOk({ ...activeData(), openWindowDefine: newOpenWindowDefine }, fields());
    };

    const handleToggleMenu = (menu: string) => {
      if (menu !== activeMenu) {
        if (menu !== 'pageView') {
          initActiveMenu(menu);
        } else {
          setShowPageView(true);
        }
      }
    };

    const designSidebarRef = useRef(null);
    const handleSetSource = () => {
      if (designSidebarRef.current) {
        designSidebarRef.current.setSource();
      }
    };
    const designRef = useRef(null);

    const handlePageView = (e: any) => {
      const { buttons } = openWindowDefine();
      const backFills = [...(buttons?.[0]?.actions?.[0].backFills ?? [])]; // 原来的回填数据

      const newActiveData = JSON.parse(e) ?? {};
      const newBackFills =
        newActiveData?.openWindowDefine?.buttons?.[0]?.actions?.[0].backFills ?? []; // 现在的回填数据

      let newFields = cloneDeep(fields() ?? []);
      const deletedBackFills = backFills.filter((item) =>
        newBackFills.find((backFill) => backFill.key !== item.key)
      ); // 原来有但是新的里面没有的数据(需要处理)

      const addedBackFills = newBackFills.filter((item) =>
        backFills.find((backFill) => backFill.key !== item.key)
      ); // 新的里面有但是原来的里面没有的数据

      newFields = newFields.filter(
        (item) => !deletedBackFills.find((backFill) => backFill.key === item.schema)
      ); // 删除（保留非删除的数据）

      newFields = [
        ...newFields,
        ...addedBackFills.map((item) => ({ schema: item.key, show: false })),
      ];

      updateFields(newFields);

      updateActiveData(newActiveData);
      setShowPageView(false);
    };

    return (
      <>
        <Drawer
          getContainer={() => document.getElementsByTagName('micro-app-body')?.[1] || document.body}
          rootClassName="openwindow-work-design-drawer"
          styles={{
            body: {
              overflow: 'auto',
            },
          }}
          zIndex={999}
          maskClosable={false}
          open={workVisible}
          closeIcon={false}
        >
          <Spin spinning={loading}>
            <div className="work-design">
              <div className="work-header">
                <div className="header-title">
                  <Tooltip title={t('dj-开窗')}>{t('dj-开窗')}</Tooltip>
                </div>
                <div className="header-menu">
                  <div
                    className="table-tab-list"
                    style={{ width: 96 * (menus?.length || 0) + 'px' }}
                  >
                    {menus.map((menu, index) => (
                      <div
                        key={index}
                        className={`tab ${activeMenu === menu.code && 'active'}`}
                        onClick={() => handleToggleMenu(menu.code)}
                      >
                        {menu.name}
                      </div>
                    ))}
                  </div>
                </div>
                <div className="header-btn">
                  <Button
                    className={`button primary-btn ${!openWindowDefine()?.dataConnectorId ? 'disabled' : 'primary'}`}
                    type="primary"
                    shape="default"
                    disabled={!openWindowDefine()?.dataConnectorId}
                    onClick={handleSave}
                  >
                    {t('dj-保存')}
                  </Button>
                  {/* </AuthWrapper> */}
                  <Icon
                    type="icondanchuxiaoxiguanbi"
                    className="closeIntro iconfont"
                    onClick={handleClose}
                  />
                </div>
              </div>
              {!!activeMenu && !loading && (
                <div className="work-body">
                  {activeMenu === 'design' && (
                    <OpenwindowDesign
                      ref={designRef}
                      designLoading={designLoading}
                      isPrivatization={isPrivatization}
                      setSource={() => handleSetSource()}
                    ></OpenwindowDesign>
                  )}
                </div>
              )}
            </div>
          </Spin>
        </Drawer>
        {showPageView && (
          <MonacoEditor
            title={t('dj-代码')}
            type={SupportedEditorType.JSON}
            value={JSON.stringify(activeData(), null, 2)}
            visible={showPageView}
            onOk={(e) => handlePageView(e)}
            onCancel={() => {
              setShowPageView(false);
            }}
            loading
          />
        )}
        <Modal
          title={null}
          open={showConfirm}
          onCancel={handleContinue}
          footer={[
            <Button onClick={handleContinue}>{t('dj-继续编辑')}</Button>,
            <Button type="primary" onClick={handleSureSave}>
              {t('dj-保存并退出')}
            </Button>,
            <Button onClick={handleSureClose}>{t('dj-直接关闭')}</Button>,
          ]}
        >
          <p style={{ textAlign: 'center', padding: '20px', fontSize: '14px' }}>
            {t('dj-请确认是否已保存所有数据')}
          </p>
        </Modal>
      </>
    );
  }
);

export default MFHOC<IOpenwindowWorkDesignLibProps>(OpenwindowWorkDesignLib);
