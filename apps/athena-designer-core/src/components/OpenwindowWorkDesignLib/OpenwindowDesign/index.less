:host {
  flex-grow: 1;
}
.openwindow-area {
  width: 100%;
  height: 100%;
  margin: 0 !important;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
  position: relative;
  .no-dataSource {
    width: 100%;
    justify-content: center;
    .set-source {
      color: #6a4cff;
      cursor: pointer;
    }
  }

  .draging-center {
    flex: 1;
    overflow-y: hidden;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    border: 16px solid #edeff3;
    border-bottom: 0;
    padding: 0px 12px;
    display: flex;
    align-items: center;
    justify-content: center;

    .component-operates {
      display: none;
      position: absolute;
      right: 0px;
      top: -26px;
      z-index: 2;
      .title,
      .operation {
        height: 22px;
        background: #6868ae;
        border-radius: 5px;
        line-height: 22px;
        padding: 0px 5px;
        color: #ffffff;
        font-size: 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 120px;
      }
      .title {
        text-wrap: nowrap;
        margin-right: 1px;
      }
      .operation {
        font-size: 12px;
        padding-top: 2px;
        // margin-left: 4px;
      }
      .operation-drag {
        border-radius: 4px 0 0 4px;
      }
      .operation-remove {
        border-radius: 0 4px 4px 0;
      }
      .operation-drag2 {
        height: 21px;
        line-height: 21px;
        border-radius: 4px 0 0 4px;
      }
      .operation-remove2 {
        height: 21px;
        line-height: 21px;
        border-radius: 0 0 4px 0;
      }
    }

    .draging-container {
      background: #fff;
      width: calc(96vw - 740px);
      height: 420px;
      padding: 16px;
      position: relative;

      &.drag-component-selected {
        border: 2px solid #2012d9 !important;

        .component-operates {
          display: flex;
        }
      }

      .drag-header {
        pointer-events: none;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 30px;
        position: relative;
        font-size: 14px;
        font-family:
          Source Han Sans CN,
          Source Han Sans CN-Medium;
        font-weight: 500;
        color: #333333;
        margin-bottom: 20px;
        .closeIcon {
          position: absolute;
          font-size: 12px;
          color: #666666;
          right: 0px;
          top: 9px;
        }
      }
      .drag-table-search {
        pointer-events: none;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 20px;

        > * {
          margin-right: 10px;
        }

        // ::ng-deep .ant-select {
        //   width: calc((100% - 230px) / 3);
        //   max-width: 200px;
        //   min-width: unset !important;
        // }
        // ::ng-deep .ant-input-group-wrapper:not(.no-used) {
        //   width: calc((100% - 230px) / 3) !important;
        //   max-width: 200px;
        //   min-width: unset !important;

        //   .ant-input-wrapper {
        //     border: 1px solid #d9d9d9;
        //     border-radius: 4px;
        //     input {
        //       border: 0 !important;
        //       color: #ccc !important;
        //       height: 30px;
        //     }
        //   }

        //   .ant-input-search .ant-input {
        //     height: 30px;
        //     border: none;
        //     color: #cccccc !important;
        //     &:hover {
        //       border: none;
        //     }
        //   }

        //   .ant-input-group-addon {
        //     background: #f9f9f9;
        //     color: #cccccc !important;
        //     border: 1px solid #d9d9d9 !important;
        //     border-left: 0 !important;
        //     border-radius: 0 !important;
        //     padding-right: 10px !important;
        //   }

        //   .ant-input-group-addon:last-child {
        //     border: 0 !important;
        //   }
        // }

        // .appSelectWrapper .ant-select {
        //   width: 200px;
        // }

        input.ant-input {
          height: 28px !important;
        }
        .ant-input-group-addon {
          button {
            height: 28px !important;
            font-size: 10px;
          }
        }

        // .ant-btn {
        //   width: 76px;
        // }

        .anticon {
          color: #cccccc;
        }

        .adp-btn {
          height: 30px;
        }
      }

      .drag-table-footer {
        pointer-events: none;
        margin-top: 50px;
        display: flex;
        align-items: center;
        justify-content: center;

        .adp-btn {
          width: 76px;
          height: 30px;
          &:first-child {
            margin-right: 16px;
          }
        }
      }

      .draging-com {
        opacity: 0.4;
        * {
          opacity: 0.4;
        }
      }
      .drag-over-container {
        border: 1px dashed #2012d9 !important;
      }
      .drag-over-left {
        border-left: 8px solid #2012d9 !important;
      }
      .drag-over-right {
        border-right: 8px solid #2012d9 !important;
      }
      .drag-over-up {
        border-top: 4px solid #2012d9 !important;
      }
      .drag-over-down {
        border-bottom: 4px solid #2012d9 !important;
      }

      .drag-table-container {
        // height: 140px;
        width: 100%;
        position: relative;
        .component-operates {
          display: none;
        }
        &.drag-component-selected {
          border: 2px solid #2012d9 !important;

          .component-operates {
            display: flex;
          }

          .drag-table:hover {
            border: none !important;
          }
        }
        .drag-table-pagination {
          position: absolute;
          right: 0px;
          pointer-events: none;
        }
        .ant-pagination .ant-pagination-item {
          height: 30px;
        }
      }

      .drag-table {
        height: 200px;
        width: 100%;
        overflow-x: auto;
        display: flex;
        padding: 30px 8px;

        &:hover {
          border: 1px dashed #2012d9;
        }

        .td-check {
          pointer-events: none;
          width: 34px;
          border: 1px solid #dddddd;

          .drag-th {
            text-align: center;
          }
          .drag-tr {
            overflow: hidden;
          }
        }

        .drag-td {
          width: 160px;
          min-width: 160px;
          border-right: 1px solid #dddddd;
          border-top: 1px solid #dddddd;
          border-bottom: 1px solid #dddddd;
          position: relative;
          &:hover {
            border: 1px dashed #2012d9;
          }
          .component-operates {
            display: none;
          }
          &.drag-component-selected {
            border: 2px solid #2012d9 !important;

            .component-operates {
              display: flex;
            }
          }
        }

        .drag-th {
          background: #cdd0e3;
          height: 34px;
          line-height: 34px;
          border-bottom: 1px solid #dddddd;
          padding: 0px 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .drag-tr {
          height: calc(100% - 34px);
          overflow-y: auto;
          padding: 8px;

          .drag-component {
            width: 100%;
            min-height: 32px;
            line-height: 32px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            position: relative;
            &:hover {
              border: 1px dashed #2012d9;
            }
            .component-operates {
              display: none;
              top: 0px;
            }
            &.drag-component-selected {
              border: 2px solid #2012d9 !important;

              .component-operates {
                display: flex;
              }
            }
          }
        }
      }
    }
  }
  .toggle-attr {
    position: absolute;
    right: 0;
    width: 32px;
    line-height: 44px;
    background: white;
    text-align: center;
    .iconfont {
      font-size: 12px;
      &:hover {
        color: #6a4cff;
      }
    }
    &.hide-attr-panel {
      background: #ffffff;
      border-radius: 4px 0 0 4px;
      box-shadow: -5px 5px 10px 0 rgba(0, 0, 0, 0.06);
    }
  }
  .attr-panel {
    width: 296px;
    height: 100%;
  }
}
