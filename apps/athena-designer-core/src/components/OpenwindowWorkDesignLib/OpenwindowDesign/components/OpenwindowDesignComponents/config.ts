export const treeNodes = [
  {
    key: 'all',
    title: '全部',
    children: [
      {
        sid: 287566002573888,
        id: '500',
        name: '品管部',
        levelSid: 287577370108480,
        levelId: 'A_001',
        levelName: '部门核决层',
        directorId: 'igp-24',
        directorName: '物流员2',
        parentSid: 0,
        corpSid: 287566449762880,
        corpId: 'Cm01',
        corpName: 'Cm01公司',
        type: 0,
        company: false,
        expanded: true,
        title: '品管部',
        key: '287566002573888',
        children: [
          {
            isLeaf: true,
            key: '287564934808128',
            title: '设备工程師',
            checked: false,
            sid: 287564934808128,
            id: 'pem01',
            name: 'ERP设备工程師',
            status: true,
            deptSid: 287566002573888,
            deptId: '500',
            deptName: '品管部',
            corpId: 'Cm01',
            corpName: 'Cm01公司',
            userId: 'pem01',
            userName: '设备工程師',
            trial: false,
            duties: [],
            groups: [],
          },
          {
            isLeaf: true,
            key: '287564934918720',
            title: '项目经理',
            checked: false,
            sid: 287564934918720,
            id: 'pm01',
            name: '张经理',
            status: true,
            deptSid: 287566002573888,
            deptId: '500',
            deptName: '品管部',
            corpId: 'Cm01',
            corpName: 'Cm01公司',
            userId: 'pm01',
            userName: '项目经理',
            trial: false,
            duties: [],
            groups: [],
          },
          {
            isLeaf: true,
            key: '349603504370240',
            title: '副总经理',
            checked: false,
            sid: 349603504370240,
            id: 'pm13',
            name: '张总经理',
            status: true,
            deptSid: 287566002573888,
            deptId: '500',
            deptName: '品管部',
            corpId: 'Cm01',
            corpName: 'Cm01公司',
            userId: 'pmm013',
            userName: '副总经理',
            trial: false,
            duties: [],
            groups: [],
          },
          {
            isLeaf: true,
            key: '556259136787008',
            title: '王思敏',
            checked: false,
            sid: 556259136787008,
            id: 'wsm',
            name: 'wsm',
            status: false,
            deptSid: 287566002573888,
            deptId: '500',
            deptName: '品管部',
            corpId: 'Cm01',
            corpName: 'Cm01公司',
            userId: '<EMAIL>',
            userName: '王思敏',
            trial: false,
            duties: [],
            groups: [],
          },
          {
            isLeaf: true,
            key: '566846545343040',
            title: '张小皊',
            checked: false,
            sid: 566846545343040,
            id: '<EMAIL>',
            name: '张小皊',
            status: true,
            deptSid: 287566002573888,
            deptId: '500',
            deptName: '品管部',
            corpId: 'Cm01',
            corpName: 'Cm01公司',
            userId: '<EMAIL>',
            userName: '张小皊',
            trial: false,
            duties: [],
            groups: [],
          },
        ],
        checked: false,
      },
      {
        sid: 287566002512448,
        id: '900',
        name: '工程部',
        levelSid: 287577370108480,
        levelId: 'A_001',
        levelName: '部门核决层',
        directorId: '00749',
        directorName: 'ERP张华',
        parentSid: 0,
        corpSid: 287566449762880,
        corpId: 'Cm01',
        corpName: 'Cm01公司',
        type: 0,
        company: false,
        expanded: true,
        title: '工程部',
        key: '287566002512448',
        children: [
          {
            isLeaf: true,
            key: '287564934820416',
            title: '电气工程師',
            checked: false,
            sid: 287564934820416,
            id: 'pem02',
            name: 'ERP电气工程師',
            status: true,
            deptSid: 287566002512448,
            deptId: '900',
            deptName: '工程部',
            corpId: 'Cm01',
            corpName: 'Cm01公司',
            userId: 'pem02',
            userName: '电气工程師',
            trial: false,
            duties: [],
            groups: [],
          },
          {
            isLeaf: true,
            key: '427718397768256',
            title: '技术员',
            checked: false,
            sid: 427718397768256,
            id: 'tech01',
            name: '技术员',
            status: true,
            deptSid: 287566002512448,
            deptId: '900',
            deptName: '工程部',
            corpId: 'Cm01',
            corpName: 'Cm01公司',
            userId: 'WF4001',
            userName: '技术员',
            trial: false,
            duties: [],
            groups: [],
          },
          {
            isLeaf: true,
            key: '427718618423872',
            title: '技术主管',
            checked: false,
            sid: 427718618423872,
            id: 'tech02',
            name: '技术主管',
            status: true,
            deptSid: 287566002512448,
            deptId: '900',
            deptName: '工程部',
            corpId: 'Cm01',
            corpName: 'Cm01公司',
            userId: 'WF4002',
            userName: '技术主管',
            trial: false,
            duties: [],
            groups: [],
          },
        ],
        checked: false,
      },
      {
        sid: 287566002684480,
        id: 'A001',
        name: '生管部',
        levelSid: 287577370108480,
        levelId: 'A_001',
        levelName: '部门核决层',
        directorId: 'Em08',
        directorName: '采购员C',
        parentSid: 0,
        corpSid: 287566449762880,
        corpId: 'Cm01',
        corpName: 'Cm01公司',
        type: 0,
        company: false,
        expanded: true,
        title: '生管部',
        key: '287566002684480',
        children: [
          {
            sid: 518294726685248,
            id: '100',
            name: '营销部',
            levelSid: 0,
            directorId: '00749',
            directorName: 'ERP张华',
            parentSid: 287566002684480,
            corpSid: 287566449762880,
            corpId: 'Cm01',
            corpName: 'Cm01公司',
            type: 0,
            company: false,
            expanded: true,
            title: '营销部',
            key: '518294726685248',
            checked: false,
          },
          {
            sid: 287566002831936,
            id: '300',
            name: '业务部',
            levelSid: 287577370108480,
            levelId: 'A_001',
            levelName: '部门核决层',
            directorId: 'CGZG01',
            directorName: 'ERP采购主管A',
            parentSid: 287566002684480,
            corpSid: 287566449762880,
            corpId: 'Cm01',
            corpName: 'Cm01公司',
            type: 0,
            company: false,
            expanded: true,
            title: '业务部',
            key: '287566002831936',
            children: [
              {
                sid: 287566002405952,
                id: '200',
                name: '采购部',
                levelSid: 287577370108480,
                levelId: 'A_001',
                levelName: '部门核决层',
                directorId: '00749',
                directorName: 'ERP张华',
                parentSid: 287566002831936,
                corpSid: 287566449762880,
                corpId: 'Cm01',
                corpName: 'Cm01公司',
                type: 0,
                company: false,
                expanded: true,
                title: '采购部',
                key: '287566002405952',
                children: [
                  {
                    sid: 287577389924928,
                    id: '200_1',
                    name: '采购一部',
                    levelSid: 287577370108480,
                    levelId: 'A_001',
                    levelName: '部门核决层',
                    directorId: 'pcm04',
                    directorName: 'ERP品质经理',
                    parentSid: 287566002405952,
                    corpSid: 287566449762880,
                    corpId: 'Cm01',
                    corpName: 'Cm01公司',
                    type: 0,
                    company: false,
                    expanded: true,
                    title: '采购一部',
                    key: '287577389924928',
                    children: [
                      {
                        isLeaf: true,
                        key: '409124462690880',
                        title: '物流员2',
                        checked: false,
                        sid: 409124462690880,
                        id: 'igp-24',
                        name: '物流员2',
                        status: true,
                        deptSid: 287577389924928,
                        deptId: '200_1',
                        deptName: '采购一部',
                        corpId: 'Cm01',
                        corpName: 'Cm01公司',
                        userId: 'logist002',
                        userName: '物流员2',
                        trial: false,
                        duties: [],
                        groups: [],
                      },
                      {
                        isLeaf: true,
                        key: '336044414231104',
                        title: '计划员2',
                        checked: false,
                        sid: 336044414231104,
                        id: 'plan02',
                        name: '计划员2',
                        status: true,
                        deptSid: 287577389924928,
                        deptId: '200_1',
                        deptName: '采购一部',
                        corpId: 'Cm01',
                        corpName: 'Cm01公司',
                        userId: 'plan02',
                        userName: '计划员2',
                        trial: false,
                        duties: [],
                        groups: [],
                      },
                      {
                        isLeaf: true,
                        key: '475567603962432',
                        title: '营销总监',
                        checked: false,
                        sid: 475567603962432,
                        id: 'yxzj-01',
                        name: '营销总监erp',
                        status: true,
                        deptSid: 287577389924928,
                        deptId: '200_1',
                        deptName: '采购一部',
                        corpId: 'Cm01',
                        corpName: 'Cm01公司',
                        userId: 'YXZJ001',
                        userName: '营销总监',
                        trial: false,
                        duties: [],
                        groups: [],
                      },
                    ],
                    checked: false,
                  },
                  {
                    isLeaf: true,
                    key: '310261104464448',
                    title: '张华',
                    checked: false,
                    sid: 310261104464448,
                    id: '00749',
                    name: 'ERP张华',
                    status: true,
                    deptSid: 287566002405952,
                    deptId: '200',
                    deptName: '采购部',
                    corpId: 'Cm01',
                    corpName: 'Cm01公司',
                    userId: 'zhanghuab',
                    userName: '张华',
                    trial: false,
                    duties: [],
                    groups: [],
                  },
                  {
                    isLeaf: true,
                    key: '308783962591808',
                    title: '采购主管',
                    checked: true,
                    sid: 308783962591808,
                    id: 'CGZG01',
                    name: 'ERP采购主管A',
                    status: true,
                    deptSid: 287566002405952,
                    deptId: '200',
                    deptName: '采购部',
                    corpId: 'Cm01',
                    corpName: 'Cm01公司',
                    userId: 'ppm06',
                    userName: '采购主管',
                    trial: false,
                    duties: [],
                    groups: [],
                  },
                  {
                    isLeaf: true,
                    key: '475567603925568',
                    title: '采购总监',
                    checked: false,
                    sid: 475567603925568,
                    id: 'cgzj-01',
                    name: '采购总监erp',
                    status: true,
                    deptSid: 287566002405952,
                    deptId: '200',
                    deptName: '采购部',
                    corpId: 'Cm01',
                    corpName: 'Cm01公司',
                    userId: 'CGZJ001',
                    userName: '采购总监',
                    trial: false,
                    duties: [],
                    groups: [],
                  },
                  {
                    isLeaf: true,
                    key: '516223656493632',
                    title: '小采',
                    checked: false,
                    sid: 516223656493632,
                    id: 'EM010',
                    name: '小采',
                    status: true,
                    deptSid: 287566002405952,
                    deptId: '200',
                    deptName: '采购部',
                    corpId: 'Cm01',
                    corpName: 'Cm01公司',
                    userId: 'ppm12',
                    userName: '小采',
                    trial: false,
                    duties: [],
                    groups: [],
                  },
                  {
                    isLeaf: true,
                    key: '310256470798912',
                    title: '小采C',
                    checked: false,
                    sid: 310256470798912,
                    id: 'pmm11-001',
                    name: 'ERP小采C',
                    status: true,
                    deptSid: 287566002405952,
                    deptId: '200',
                    deptName: '采购部',
                    corpId: 'Cm01',
                    corpName: 'Cm01公司',
                    userId: 'pmm11',
                    userName: '小采C',
                    trial: false,
                    duties: [],
                    groups: [],
                  },
                  {
                    isLeaf: true,
                    key: '472067024867904',
                    title: '采购主管A',
                    checked: false,
                    sid: 472067024867904,
                    id: 'ppm06',
                    name: '采购主管A',
                    status: true,
                    deptSid: 287566002405952,
                    deptId: '200',
                    deptName: '采购部',
                    corpId: 'Cm01',
                    corpName: 'Cm01公司',
                    userId: 'AthenaWF$ppm06',
                    userName: '采购主管A',
                    trial: false,
                    duties: [],
                    groups: [],
                  },
                  {
                    isLeaf: true,
                    key: '287564934791744',
                    title: '採購主管B',
                    checked: false,
                    sid: 287564934791744,
                    id: 'ppm07',
                    name: 'ERP采购主管B',
                    status: true,
                    deptSid: 287566002405952,
                    deptId: '200',
                    deptName: '采购部',
                    corpId: 'Cm01',
                    corpName: 'Cm01公司',
                    userId: 'ppm07',
                    userName: '採購主管B',
                    trial: false,
                    duties: [],
                    groups: [],
                  },
                  {
                    isLeaf: true,
                    key: '300999035200064',
                    title: '小採A',
                    checked: false,
                    sid: 300999035200064,
                    id: 'ppm08',
                    name: 'ERP小采A',
                    status: true,
                    deptSid: 287566002405952,
                    deptId: '200',
                    deptName: '采购部',
                    corpId: 'Cm01',
                    corpName: 'Cm01公司',
                    userId: 'ppm08',
                    userName: '小採A',
                    trial: false,
                    duties: [],
                    groups: [],
                  },
                  {
                    isLeaf: true,
                    key: '300999124570688',
                    title: '小采B',
                    checked: false,
                    sid: 300999124570688,
                    id: 'ppm09',
                    name: 'ERP小采B',
                    status: true,
                    deptSid: 287566002405952,
                    deptId: '200',
                    deptName: '采购部',
                    corpId: 'Cm01',
                    corpName: 'Cm01公司',
                    userId: 'ppm09',
                    userName: '小采B',
                    trial: false,
                    duties: [],
                    groups: [],
                  },
                ],
                checked: false,
              },
              {
                isLeaf: true,
                key: '308515231457856',
                title: '小采A',
                checked: false,
                sid: 308515231457856,
                id: 'Em08',
                name: '采购员C',
                status: true,
                deptSid: 287566002831936,
                deptId: '300',
                deptName: '业务部',
                corpId: 'Cm01',
                corpName: 'Cm01公司',
                userId: 'pur001',
                userName: '小采A',
                trial: false,
                duties: [],
                groups: [],
              },
              {
                isLeaf: true,
                key: '300999287259712',
                title: '销售1',
                checked: false,
                sid: 300999287259712,
                id: 'ppm11',
                name: '业务员2',
                status: true,
                deptSid: 287566002831936,
                deptId: '300',
                deptName: '业务部',
                corpId: 'Cm01',
                corpName: 'Cm01公司',
                userId: 'pcc001',
                userName: '销售1',
                trial: false,
                duties: [],
                groups: [],
              },
              {
                isLeaf: true,
                key: '552718905537088',
                title: '采购主管1',
                checked: false,
                sid: 552718905537088,
                id: 'pur003',
                name: 'pur003',
                status: true,
                deptSid: 287566002831936,
                deptId: '300',
                deptName: '业务部',
                corpId: 'Cm01',
                corpName: 'Cm01公司',
                userId: 'pur003',
                userName: '采购主管1',
                trial: false,
                duties: [],
                groups: [],
              },
            ],
            checked: false,
          },
          {
            isLeaf: true,
            key: '459165576413760',
            title: '核价员1',
            checked: false,
            sid: 459165576413760,
            id: 'erp-price1',
            name: '核价员1',
            status: true,
            deptSid: 287566002684480,
            deptId: 'A001',
            deptName: '生管部',
            corpId: 'Cm01',
            corpName: 'Cm01公司',
            userId: 'price001',
            userName: '核价员1',
            trial: false,
            duties: [],
            groups: [],
          },
          {
            isLeaf: true,
            key: '409124348801600',
            title: '物流主管',
            checked: false,
            sid: 409124348801600,
            id: 'igp-23',
            name: '物流主管',
            status: true,
            deptSid: 287566002684480,
            deptId: 'A001',
            deptName: '生管部',
            corpId: 'Cm01',
            corpName: 'Cm01公司',
            userId: 'logist001',
            userName: '物流主管',
            trial: false,
            duties: [],
            groups: [],
          },
          {
            isLeaf: true,
            key: '409124532617792',
            title: '物流员2',
            checked: false,
            sid: 409124532617792,
            id: 'igp-25',
            name: '物流员',
            status: true,
            deptSid: 287566002684480,
            deptId: 'A001',
            deptName: '生管部',
            corpId: 'Cm01',
            corpName: 'Cm01公司',
            userId: 'logist003',
            userName: '物流员2',
            trial: false,
            duties: [],
            groups: [],
          },
          {
            isLeaf: true,
            key: '287564934844992',
            title: '品质经理',
            checked: false,
            sid: 287564934844992,
            id: 'pcm04',
            name: 'ERP品质经理',
            email: '<EMAIL>',
            status: true,
            deptSid: 287566002684480,
            deptId: 'A001',
            deptName: '生管部',
            corpId: 'Cm01',
            corpName: 'Cm01公司',
            userId: 'pcm04',
            userName: '品质经理',
            trial: false,
            duties: [],
            groups: [],
          },
          {
            isLeaf: true,
            key: '336044358828608',
            title: '计划员1',
            checked: false,
            sid: 336044358828608,
            id: 'plan01',
            name: '计划员1',
            status: true,
            deptSid: 287566002684480,
            deptId: 'A001',
            deptName: '生管部',
            corpId: 'Cm01',
            corpName: 'Cm01公司',
            userId: 'plan01',
            userName: '计划员1',
            trial: false,
            duties: [],
            groups: [],
          },
          {
            isLeaf: true,
            key: '287564934861376',
            title: '車間主管',
            checked: false,
            sid: 287564934861376,
            id: 'pmm03',
            name: 'ERP车间主管',
            email: '<EMAIL>',
            status: true,
            deptSid: 287566002684480,
            deptId: 'A001',
            deptName: '生管部',
            corpId: 'Cm01',
            corpName: 'Cm01公司',
            userId: 'pmm03',
            userName: '車間主管',
            trial: false,
            duties: [],
            groups: [],
          },
          {
            isLeaf: true,
            key: '308786393641536',
            title: '生管主管',
            checked: false,
            sid: 308786393641536,
            id: 'pmm05-01',
            name: 'ERP生管主管',
            status: true,
            deptSid: 287566002684480,
            deptId: 'A001',
            deptName: '生管部',
            corpId: 'Cm01',
            corpName: 'Cm01公司',
            userId: 'pmm05',
            userName: '生管主管',
            trial: false,
            duties: [],
            groups: [],
          },
        ],
        checked: false,
      },
    ],
    expanded: true,
    checked: false,
  },
];
export const checkedTreeNodes = [
  {
    sid: 287566002573888,
    id: '500',
    name: '品管部',
    levelSid: 287577370108480,
    levelId: 'A_001',
    levelName: '部门核决层',
    directorId: 'igp-24',
    directorName: '物流员2',
    parentSid: 0,
    corpSid: 287566449762880,
    corpId: 'Cm01',
    corpName: 'Cm01公司',
    type: 0,
    company: false,
    expanded: true,
    title: '品管部',
    key: '287566002573888',
    children: [
      {
        isLeaf: true,
        key: '287564934808128',
        title: '设备工程師',
        checked: true,
        sid: 287564934808128,
        id: 'pem01',
        name: 'ERP设备工程師',
        status: true,
        deptSid: 287566002573888,
        deptId: '500',
        deptName: '品管部',
        corpId: 'Cm01',
        corpName: 'Cm01公司',
        userId: 'pem01',
        userName: '设备工程師',
        trial: false,
        duties: [],
        groups: [],
      },
    ],
  },
  {
    sid: 287566002512448,
    id: '900',
    name: '工程部',
    levelSid: 287577370108480,
    levelId: 'A_001',
    levelName: '部门核决层',
    directorId: '00749',
    directorName: 'ERP张华',
    parentSid: 0,
    corpSid: 287566449762880,
    corpId: 'Cm01',
    corpName: 'Cm01公司',
    type: 0,
    company: false,
    expanded: true,
    title: '工程部',
    key: '287566002512448',
    children: [
      {
        isLeaf: true,
        key: '427718397768256',
        title: '技术员',
        checked: false,
        sid: 427718397768256,
        id: 'tech01',
        name: '技术员',
        status: true,
        deptSid: 287566002512448,
        deptId: '900',
        deptName: '工程部',
        corpId: 'Cm01',
        corpName: 'Cm01公司',
        userId: 'WF4001',
        userName: '技术员',
        trial: false,
        duties: [],
        groups: [],
      },
    ],
    checked: true,
  },
];
export const checkedSingleTreeNodes = [
  {
    sid: 287566002573888,
    id: '500',
    name: '品管部',
    levelSid: 287577370108480,
    levelId: 'A_001',
    levelName: '部门核决层',
    directorId: 'igp-24',
    directorName: '物流员2',
    parentSid: 0,
    corpSid: 287566449762880,
    corpId: 'Cm01',
    corpName: 'Cm01公司',
    type: 0,
    company: false,
    expanded: true,
    title: '品管部',
    key: '287566002573888',
    children: [
      {
        isLeaf: true,
        key: '287564934808128',
        title: '设备工程師',
        checked: true,
        sid: 287564934808128,
        id: 'pem01',
        name: 'ERP设备工程師',
        status: true,
        deptSid: 287566002573888,
        deptId: '500',
        deptName: '品管部',
        corpId: 'Cm01',
        corpName: 'Cm01公司',
        userId: 'pem01',
        userName: '设备工程師',
        trial: false,
        duties: [],
        groups: [],
      },
    ],
  },
];
export const defaultCheckedKeys = ['287564934808128', '427718397768256'];
export const defaultSingleCheckedKeys = ['287564934808128'];
