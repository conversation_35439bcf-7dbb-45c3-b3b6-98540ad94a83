import { ATHENA_TYPES } from '@/components/OpenwindowWorkDesignLib/utils/fields.utils';
import { Button, Checkbox, DatePicker, Input, InputNumber, Radio, TimePicker, Tree } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import './index.less';
import { t } from 'i18next';
import { Icon } from '@/components';
import { checkedSingleTreeNodes, defaultSingleCheckedKeys, treeNodes } from './config';
const { Search } = Input;

interface OpenwindowDesignComponentsProps {
  component: any;
}

const OpenwindowDesignComponents = (props: OpenwindowDesignComponentsProps) => {
  const { component } = props;
  const [optionList, setOptionList] = useState([]);

  useEffect(() => {
    setOptionList([]);
    const newOptionList = [];
    if (props.component.type === 'RADIO_GROUP') {
      (props.component.options || []).forEach((item) => {
        newOptionList.push({
          label: item.lang?.title?.[t('dj-LANG')],
          value: item.value,
        });
      });
      setOptionList(newOptionList);
    }
  }, [props.component]);

  return (
    <>
      {/* 日期选择 */}
      {[ATHENA_TYPES.DATEPICKER].includes(component.type) && (
        <div className={`textfield-content ${component.disabled ? 'disabled' : ''}`}>
          <span className="textfield-info">{component.placeholder ?? t('请选择日期')}</span>
          <Icon type="icona-005-riqixuanze" />
        </div>
      )}

      {/* 时间选择 */}
      {[ATHENA_TYPES.TIMEPICKER].includes(component.type) && (
        <div className={`textfield-content ${component.disabled ? 'disabled' : ''}`}>
          <span className="textfield-info">{component.placeholder ?? t('请选择时间')}</span>
          <Icon type="icona-006-shijianxuanze" />
        </div>
      )}

      {/* 文本输入 */}
      {[ATHENA_TYPES.INPUT].includes(component.type) && component.dataType !== 'numeric' && (
        <div className={`textfield-content ${component.disabled ? 'disabled' : ''}`}>
          <span className="textfield-info">{component.placeholder ?? t('请输入')}</span>
          <Icon type="iconcharu" />
        </div>
      )}

      {/* 数字输入 */}
      {[ATHENA_TYPES.INPUT].includes(component.type) && component.dataType === 'numeric' && (
        <div className="textfield-content">
          <div>
            <Icon type="iconcharu" />
            <span className="textfield-info" style={{ marginLeft: '6px' }}>
              {component.placeholder ?? t('请输入')}
            </span>
          </div>
          <div className="icon-top-bottom">
            <Icon type="icontopMenu" />
            <Icon style={{ marginTop: '-6px' }} type="iconbottomMechanism" />
          </div>
        </div>
      )}

      {/* 表格附件 */}
      {[ATHENA_TYPES.FILE_UPLOAD].includes(component.type) && component.editable && (
        <div className="textfield-content" style={{ height: '40px' }}>
          <span className="textfield-info">{t('请上传')}</span>
        </div>
      )}
      {[ATHENA_TYPES.FILE_UPLOAD].includes(component.type) && !component.editable && <div>—</div>}

      {/* 百分比 */}
      {[ATHENA_TYPES.PERCENT_INPUT].includes(component.type) && (
        <div className="textfield-content textfield-content-precent">
          <span className="textfield-info">{component.placeholder ?? t('请输入')}</span>%
        </div>
      )}

      {/* 金额 */}
      {[ATHENA_TYPES.AMOUNT_INPUT].includes(component.type) && (
        <div className="textfield-content textfield-content-amount">
          <div>
            <Icon type="iconcharu" />
            <span className="textfield-info">{t('RMB')}</span>
          </div>
          <span className="textfield-info">{component.placeholder ?? t('请输入')}</span>
        </div>
      )}

      {/* 计量组件 */}
      {[ATHENA_TYPES.MEASURE].includes(component.type) && (
        <div className="textfield-content textfield-content-measuer">
          <Icon type="iconcharu" />
          <div>
            <span className="textfield-info">{t('请输入')}</span>
            <span className="textfield-PCS">{t('PCS')}</span>
          </div>
        </div>
      )}

      {/* 差异值 */}
      {[ATHENA_TYPES.DIFFERENCE_CALCULATION].includes(component.type) && (
        <div className="difference">
          <div className="diff-container">
            <div className="left">{component.headerName ?? t('选择框')}</div>
            <div className="right">
              {(component?.fields ?? []).map((item, i) => {
                return (
                  <div className="detail">
                    <div className="icon">
                      {i === 0 && <img src="/assets/img/new.png" className="athena-icon" alt="" />}
                      {i === 1 && <img src="/assets/img/old.png" className="athena-icon" alt="" />}
                    </div>
                    <div className="num">
                      {i === 0 && <>20</>}
                      {i === 1 && <>10</>}
                    </div>
                    <div className="unit">{'PCS'}</div>
                  </div>
                );
              })}
              <div className="summary">
                {t('dj-c-差异')}
                {`${t('dj-c-加量')}10PCS`}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 文本 */}
      {[ATHENA_TYPES.LABEL].includes(component.type) && (
        <>
          {component.className === 'background-grey' && (
            <img className="horn" src="/assets/img/horn.png" />
          )}
          <span
            className={`ellipsis ${component.important}?'important':'' ${component.className === 'background-grey' ? 'orange' : ''}`}
          >
            {component.lang?.headerName?.[t('dj-LANG')]}
          </span>
        </>
      )}

      {/* 多选框 */}
      {[ATHENA_TYPES.SELECT_MULTIPLE].includes(component.type) && (
        <div className={`textfield-content ${component.disabled ? 'disabled' : ''}`}>
          <span className="textfield-info">{component.placeholder ?? t('请选择')}</span>
          <Icon type="iconbottomMechanism" />
        </div>
      )}

      {/* 文本输入、文本域 */}
      {[ATHENA_TYPES.TEXTAREA].includes(component.type) && component.dataType !== 'numeric' && (
        <div className="textfield-content">
          <span className="textfield-info">{component.placeholder ?? t('请输入')}</span>
          <Icon type="iconcharu" />
        </div>
      )}

      {/* 收获地址 */}
      {[ATHENA_TYPES.ADDRESS].includes(component.type) && <> </>}

      {/* 方案选择、选择框 */}
      {[ATHENA_TYPES.PLAN_SELECT, ATHENA_TYPES.SELECT].includes(component.type) && (
        <div className="textfield-content">
          <span className="textfield-info">{component.placeholder ?? t('请选择')}</span>
          <Icon type="iconbottomMechanism" />
        </div>
      )}

      {/* 动态按钮 */}
      {[ATHENA_TYPES.BUTTON].includes(component.type) && (
        <div className="dynamic-btn-content">
          <Button
            className="dynamic-btn"
            type={component.athType ?? 'default'}
            size={component.sizeType ?? 'large'}
            disabled={component.disabled}
          >
            {component.beforeIcon && component.beforeIcon !== '' && (
              <Icon type={component.beforeIcon} />
            )}
            {component.title}
            {component.afterIcon && component.afterIcon !== '' && (
              <Icon type={component.afterIcon} />
            )}
          </Button>
        </div>
      )}

      {/* 人员选择 */}
      {[ATHENA_TYPES.PERSON_SELECT].includes(component.type) && (
        <div className="lcdp-dynamic-person">
          <div className="dynamic-person-part">
            <div className="panel-title">{t('all')}</div>
            <div className="panel-content">
              <Search disabled placeholder={t('dj-请输入关键字')} style={{ width: '100%' }} />
              <Tree
                treeData={treeNodes}
                checkable={!component.disabled}
                checkedKeys={defaultSingleCheckedKeys}
                expandedKeys={['all', '287566002573888', '287566002512448', '287566002684480']}
              />
            </div>
          </div>
          <div className="dynamic-person-part">
            <div className="panel-title">
              <span>{t('已选')}</span>
              {!component.disabled && <span className="reset-btn">{t('重置')}</span>}
            </div>
            <div className="panel-content">
              <Tree treeData={checkedSingleTreeNodes} expandedKeys={['287566002573888']} />
            </div>
          </div>
        </div>
      )}

      {/* 复选框 */}
      {[ATHENA_TYPES.CHECKBOX].includes(component.type) && (
        <Checkbox disabled={component.disabled}></Checkbox>
      )}

      {/* 单选框 */}
      {[ATHENA_TYPES.RADIO_GROUP].includes(component.type) && (
        <div className="radio-group-component">
          {optionList.slice(0, 2).map((item) => {
            return <Radio disabled={component.disabled}>{item.label}</Radio>;
          })}
        </div>
      )}
    </>
  );
};

export default OpenwindowDesignComponents;
