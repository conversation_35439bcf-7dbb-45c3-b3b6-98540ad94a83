// :host {
.textfield-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 4px;
  .textfield-info {
    color: #999;
    font-size: 13px;
  }
  .icon-top-bottom {
    display: flex;
    flex-direction: column;
  }
}
.disabled {
  background: #f5f5f5 !important;
  border-radius: 4px !important;
}
.text-disabled {
  color: #dedede !important;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 12px;
}
.important {
  margin-bottom: 15px;
  font-weight: bold;
  font-size: 14px !important;
}
.horn {
  margin-right: 4px;
  width: 16px;
}
.orange {
  color: #fe7b1d;
}
.upload-text {
  color: #999;
  font-size: 13px;
  display: flex;
  align-items: center;
  .upload-text-info {
    margin-left: 4px;
    color: #6868ae;
    font-size: 14px;
  }
}
.textfield-content-precent {
  justify-content: flex-end;
  .textfield-info {
    color: #999;
    font-size: 13px;
    margin-right: 6px;
  }
}
.textfield-content-amount,
.textfield-content-measuer {
  .textfield-info {
    color: #999;
    font-size: 13px;
    margin-left: 6px;
  }
}
.radio-group-component {
  pointer-events: none;
  display: flex;
  flex-direction: column;
}
.lcdp-dynamic-person {
  width: 100%;
  height: 400px;
  max-height: 400px;
  display: flex;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  overflow: hidden;
  text-align: left;

  flex-direction: row;
  .dynamic-person-part {
    width: 50%;
    height: 100%;
    &:first-child {
      border-right: 1px solid #f2f2f2;
    }

    .panel-title {
      display: flex;
      justify-content: space-between;
      cursor: default;
      line-height: 32px;
      font-size: 13px;
      font-weight: bold;
      padding: 0 9px;
      background: #f2f2f2;

      .reset-btn {
        min-width: fit-content;
        color: #6a4cff;
      }
    }
    .panel-content {
      overflow-y: auto;
      padding: 4px 12px;
      max-height: calc(100% - 32px);
      nz-tree {
        pointer-events: none;
      }
      .ant-input-group > .ant-input:first-child {
        height: 32px;
      }
    }
  }
}
.difference {
  .diff-container {
    display: flex;
  }
  .left {
    width: 70px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    font-size: 13px;
    text-align: right;
    padding-right: 5px;
  }
  .right {
    flex: 1;
    .detail {
      display: flex;
      height: 23px !important;
      .icon {
        margin-right: 8px;
        .athena-icon {
          height: 22px;
          width: auto;
        }
      }
      .num {
        margin-right: 4px;
      }
    }
    .summary {
      font-size: 12px;
      color: red;
      margin-top: 5px;
      height: 23px !important;
    }
  }
}
.dynamic-btn-content {
  display: flex;
  justify-content: center;
}
//   ::ng-deep .ant-checkbox-wrapper {
//     pointer-events: none;
//   }
// }
