import React, { useEffect, useState } from 'react';
import { Input, InputNumber, Select } from 'antd';

import './MultiTypeInput.less';
import { MultiTypeInputProps, ValueType } from './types';

function MultiTypeInput(props: MultiTypeInputProps) {
  const { value, placeholder, changeValue } = props;
  const [valueType, setValueType] = useState<ValueType>('string');

  useEffect(() => {
    const type = Object.prototype.toString.call(value);
    if (['[object Null]', '[object Undefined]'].includes(type)) return;
    const data =
      type === '[object Number]' ? 'number' : type === '[object Boolean]' ? 'boolean' : 'string';
    setValueType(data);
  }, [value]);

  const typeChange = (e: ValueType) => {
    setValueType(e);
    changeValue(null);
  };

  return (
    <div className="multi-type-input">
      <Select
        className="select-type"
        defaultValue="string"
        value={valueType}
        onChange={typeChange}
        options={[
          { value: 'string', label: 'string' },
          { value: 'number', label: 'number' },
          { value: 'boolean', label: 'boolean' },
        ]}
      />
      {valueType === 'string' && (
        <Input
          className="value-enter"
          value={value}
          onChange={(e: any) => {
            changeValue(e.target.value);
          }}
          placeholder={placeholder}
        />
      )}
      {valueType === 'number' && (
        <InputNumber
          className="value-enter value-number"
          value={value}
          onChange={(e: number) => {
            changeValue(e);
          }}
          placeholder={placeholder}
        />
      )}
      {valueType === 'boolean' && (
        <Select
          className="value-enter value-select"
          value={value}
          onChange={(e: boolean) => {
            changeValue(e);
          }}
          options={[
            { value: true, label: 'true' },
            { value: false, label: 'false' },
          ]}
        />
      )}
    </div>
  );
}

export { MultiTypeInput };
