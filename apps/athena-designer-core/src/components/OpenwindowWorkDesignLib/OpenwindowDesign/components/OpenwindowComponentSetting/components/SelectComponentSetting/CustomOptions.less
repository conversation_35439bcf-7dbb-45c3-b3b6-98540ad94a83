.drop-list {
  width: 500px;
  max-width: 100%;
  // border: solid 1px #ccc;
  // min-height: 60px;
  display: block;
  overflow: hidden;
  margin-top: 8px;
}

.drop-box {
  height: 30px;
  padding: 10px;
  margin-bottom: 8px;
  // border: solid 1px #d9d9d9;
  color: #333333;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  font-size: 12px;
  border-radius: 4px;
  background: #f1f2fb;
  // background: rgb(248, 250, 253);
  .back-fields-info {
    width: 190px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-wrap: break-word;
    word-break: break-all;
    white-space: nowrap;
  }
  .drop-operate {
    // width: 28px;
    display: flex;
    justify-content: space-between;
    text-align: right;
    align-items: center;
    .back-fields-info {
      color: #999999;
    }
    .anticon {
      // color: #6868ae;
      margin-left: 4px;
    }
    a {
      display: flex;
      align-items: center;
    }
    .iconfont {
      font-size: 14px;
      color: #6868ae;
      cursor: pointer;
      margin-left: 4px;
    }

    .button-move {
      cursor: move;
      // svg {
      //   cursor: move !important;
      // }
    }
  }
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow:
    0 5px 5px -3px rgba(0, 0, 0, 0.2),
    0 8px 10px 1px rgba(0, 0, 0, 0.14),
    0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.example-box:last-child {
  border: none;
}

.example-list.cdk-drop-list-dragging .example-box:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.select-content {
  margin-bottom: 8px;
  .setter-title {
    width: 100%;
    line-height: 28px;
    font-size: 13px;
    color: #333;
    font-weight: 700;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    span {
      float: left;
      color: #333333;
      font-size: 13px;
      font-weight: Medium;
    }

    span:nth-of-type(2) {
      color: #6a4cff;
      margin-left: 13px;
      cursor: pointer;
    }
  }
}

.icon-del {
  color: #333333;
  margin-right: 4px;
}

.operation-btn {
  background-color: #6868ae;
  width: 100%;
}

.option-value-input {
  ::ng-deep {
    .ant-input-affix-wrapper {
      width: calc(100% - 5px) !important;
      .ant-input {
        width: 100% !important;
      }
    }
    .number-type {
      width: 100% !important;
    }
    .ant-input-number {
      width: 100% !important;
    }
    .athena-input-box {
      width: 100% !important;
    }
    .boolean-type {
      width: calc(100% - 5px) !important;
    }
  }
}

#appInputWrapper {
  .ant-input-affix-wrapper {
    background: #fff;
  }
}
