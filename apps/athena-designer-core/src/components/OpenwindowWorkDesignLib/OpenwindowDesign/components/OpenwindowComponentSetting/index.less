.openwindow-setting {
  height: calc(100% - 20px);
  .breadcrumb-nav {
    height: 48px;
    display: flex;
    flex-wrap: wrap;
    border-bottom: 1px solid rgba(31, 56, 88, 0.1);
    font-size: 12px;
    padding: 6px 16px;
    align-items: center;
    width: 100%;
    .breadcrumb {
      background-color: #fff;
      margin-bottom: 0 !important;
      padding: 0;

      .breadcrumb-item.active {
        a {
          cursor: text;
        }
      }
      .breadcrumb-text-ellipsis {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        min-width: 20px;
      }

      .breadcrumb-item + .breadcrumb-item::before {
        float: left;
        padding-right: 0.5rem;
        color: #6c757d;
        content: '>';
      }
    }
  }
  .set-form-container {
    height: calc(100% - 40px);
    padding: 16px;
    overflow-y: auto;
    .exten-info {
      color: #2012d9;
      cursor: pointer;
    }
  }
}
