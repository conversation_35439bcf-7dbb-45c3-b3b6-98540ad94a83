.multi-type-input {
  display: flex;
  align-items: center;
  .ant-select {
    height: 28px;
  }
  .select-type {
    width: 100px;
  }
  .value-enter {
    width: calc(100% - 98px);
    height: 28px;
    margin-left: 8px;
  }
  .ant-input-number {
    border-radius: 2px;
  }
  .value-number {
    .ant-input-number-input {
      padding: 2px 8px;
      height: 28px;
      font-size: 13px;
      border-radius: 2px;
      &::-webkit-input-placeholder {
        color: rgb(153, 153, 153);
        font-size: 13px;
      }
    }
  }
}
