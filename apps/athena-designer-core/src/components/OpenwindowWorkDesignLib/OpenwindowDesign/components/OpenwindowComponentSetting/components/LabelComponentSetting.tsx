import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
// import './index.less';
import { t } from 'i18next';
import { ATHENA_TYPES } from '@/components/OpenwindowWorkDesignLib/utils/fields.utils';
import { Checkbox, Form, Input, InputNumber, Select } from 'antd';
import { dslI18n, getComponentList } from '../../../config';
import AppLangInput from '@/components/AppLangInput';
import { cloneDeep, debounce } from 'lodash';
import { useWorkData } from '@/components/OpenwindowWorkDesignLib/store/workData';

const { Option } = Select;

interface LabelComponentSettingProps {
  data: any;
  //   columnType: string;
  change: (data: any) => void;
}

const LabelComponentSetting = (props: LabelComponentSettingProps) => {
  const [dataTypeOptions, setDataTypeOptions] = useState<string[]>([]); // 数据类型
  const [componentForm] = Form.useForm(); // 表单 [文本输入、日期选择、时间选择、数字输入、百分比]
  useEffect(() => {
    handleInit();
  }, [props.data]);

  /**
   * 初始化
   */
  const handleInit = () => {
    const {
      type,
      id,
      headerName,
      schema,
      path,
      important,
      className,
      editable,
      isFocusDisplay,
      dataType,
      lang,
    } = props.data;
    setDataTypeOptions(getComponentList(type, dataType));
    const status = editable ? 'editable' : 'disabled';
    componentForm.setFieldsValue({
      id, // 唯一标识
      headerName: lang?.headerName ?? dslI18n, // 标题
      schema, // schema
      path, // path
      // 是否重要
      important,
      // class
      className,
      status, // 状态
      isFocusDisplay, // 是否启用标题在内样式
      dataType, // 数据类型
    });
  };

  /**
   * 多语言输入框回调
   * @param key
   * @param data
   */
  const handlePatchLang = (key: any, data: any) => {
    if (['headerName'].includes(key)) {
      componentForm.setFieldsValue({
        [key]: { ...data, label: `${data?.[t('dj-LANG')]}` },
        ['label']: { ...data, label: `${data?.[t('dj-LANG')]}` },
      });
    } else {
      componentForm.setFieldsValue({
        [key]: { ...data, label: `${data?.[t('dj-LANG')]}` },
      });
    }
    onChange();
  };

  // 创建防抖函数并保持引用不变
  const debouncedSearch = useCallback(
    debounce((key, data) => handlePatchLang(key, data), 500),
    [] // 依赖项为空，确保防抖函数只创建一次
  );

  // 输入框变化事件
  const handleChange = (key: any, data: any) => {
    debouncedSearch(key, data); // 触发防抖函数
  };

  /**
   * 是否启用标题在内样式变更
   * @param e
   */
  const handleChangeIsFocusDisplay = (e) => {
    componentForm.setFieldsValue({ isFocusDisplay: e.target.checked });
    onChange();
  };

  /**
   * 数据类型变更
   * @param e
   */
  const handleChangeDataType = (e) => {
    componentForm.setFieldsValue({ dataType: e });
    onChange();
  };

  const handleChangeAttr = (key, value) => {
    componentForm.setFieldsValue({ [key]: value });
    onChange();
  };

  /**
   * 回填
   */
  const onChange = () => {
    console.log(componentForm.getFieldsValue());
    const { headerName, important, className, status, isFocusDisplay, dataType } =
      componentForm.getFieldsValue();
    const editable = status === 'editable';
    const disabled = !editable;
    let data = {
      ...props.data,
      headerName: headerName[t('dj-LANG')],
      label: headerName[t('dj-LANG')],
      lang: {
        headerName,
        label: headerName,
      },
      important,
      className,
      editable,
      disabled,
      isFocusDisplay,
      dataType,
    };
    props.change({
      isControl: true,
      value: data,
    });
  };

  return (
    <div className="component-form">
      <Form
        className="form-info"
        form={componentForm}
        name="openwindow-setting-form"
        layout="vertical"
        labelCol={{
          span: 24,
        }}
        wrapperCol={{
          span: 24,
        }}
      >
        {/* 唯一标识 */}
        <Form.Item label={t('dj-唯一标识')} name="id">
          <Input disabled />
        </Form.Item>
        {/* 标题 */}
        <Form.Item label={t('dj-标题')} name="headerName">
          <AppLangInput
            required
            size="small"
            onChange={(value) => handleChange('headerName', value)}
          />
        </Form.Item>
        {/* schema */}
        <Form.Item label="schema" name="schema">
          <Input disabled />
        </Form.Item>
        {/* path */}
        <Form.Item label="path" name="path">
          <Input disabled />
        </Form.Item>
        {/* 是否重要 */}
        <Form.Item label={null} name="important" valuePropName="checked">
          <Checkbox onChange={(e) => handleChangeAttr('important', e.target.checked)}>
            {t('是否重要')}
          </Checkbox>
        </Form.Item>
        {/* class */}
        <Form.Item label={t('样式类名')} name="className" className="prop-select">
          <Select>
            <Select.Option value="none">{t('无')}</Select.Option>
            <Select.Option value="background-grey">background-grey</Select.Option>
          </Select>
        </Form.Item>
        {/* 状态 */}
        <Form.Item label={t('dj-状态')} name="status">
          <Select disabled>
            <Select.Option value="editable">{t('普通')}</Select.Option>
            <Select.Option value="disabled">{t('禁用')}</Select.Option>
          </Select>
        </Form.Item>
        {/* 是否启用标题在内样式 */}
        <Form.Item label={null} name="isFocusDisplay" valuePropName="checked">
          <Checkbox onChange={(e) => handleChangeIsFocusDisplay(e)}>
            {t('dj-是否启用标题在内样式')}
          </Checkbox>
        </Form.Item>
        {/* 数据类型 */}
        <Form.Item label={t('dj-数据类型')} name="dataType">
          <Select onChange={(e) => handleChangeDataType(e)}>
            {dataTypeOptions.map((option) => {
              return <Select.Option value={option}>{option}</Select.Option>;
            })}
          </Select>
        </Form.Item>
      </Form>
    </div>
  );
};

export default LabelComponentSetting;
