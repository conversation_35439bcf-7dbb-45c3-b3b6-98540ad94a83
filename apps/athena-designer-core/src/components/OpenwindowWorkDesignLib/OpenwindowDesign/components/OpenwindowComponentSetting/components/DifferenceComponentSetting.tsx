import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
// import './index.less';
import { t } from 'i18next';
import { ATHENA_TYPES } from '@/components/OpenwindowWorkDesignLib/utils/fields.utils';
import { Checkbox, Form, Input, InputNumber, Select } from 'antd';
import { dslI18n, getComponentList } from '../../../config';
import AppLangInput from '@/components/AppLangInput';
import { cloneDeep, debounce } from 'lodash';
import { useWorkData } from '@/components/OpenwindowWorkDesignLib/store/workData';

const { Option } = Select;

interface DifferenceComponentSettingProps {
  data: any;
  //   columnType: string;
  change: (data: any) => void;
}

const DifferenceComponentSetting = (props: DifferenceComponentSettingProps) => {
  const isInitialized = useRef(false);
  const [componentForm] = Form.useForm(); // 表单 [文本输入、日期选择、时间选择、数字输入、百分比]
  useEffect(() => {
    handleInit();
  }, [props.data]);

  /**
   * 初始化
   */
  const handleInit = () => {
    const { type, id, headerName, schema, path, editable, isFocusDisplay, lang, fields } =
      props.data;
    const { schema: newSchema, unit: newUnit } = fields?.[0] ?? {};
    const { schema: oldSchema, unit: oldUnit } = fields?.[1] ?? {};
    const status = editable ? 'editable' : 'disabled';
    componentForm.setFieldsValue({
      id, // 唯一标识
      headerName: lang?.headerName ?? dslI18n, // 标题 修改标题时候
      schema, // schema
      path, // path
      status, // 状态
      // 新值schema
      newSchema,
      // 新值单位schema
      newUnit,
      // 旧值schema
      oldSchema,
      // 旧值单位schema
      oldUnit,
      isFocusDisplay, // 是否启用标题在内样式
    });
  };

  /**
   * 多语言输入框回调
   * @param key
   * @param data
   */
  const handlePatchLang = (key: any, data: any) => {
    if (['headerName'].includes(key)) {
      componentForm.setFieldsValue({
        [key]: { ...data, label: `${data?.[t('dj-LANG')]}` },
        ['label']: { ...data, label: `${data?.[t('dj-LANG')]}` },
      });
    } else {
      componentForm.setFieldsValue({
        [key]: { ...data, label: `${data?.[t('dj-LANG')]}` },
      });
    }
    onChange();
  };

  // 创建防抖函数并保持引用不变
  const debouncedSearch = useCallback(
    debounce((key, data) => handlePatchLang(key, data), 500),
    [] // 依赖项为空，确保防抖函数只创建一次
  );

  // 输入框变化事件
  const handleChange = (key: any, data: any) => {
    debouncedSearch(key, data); // 触发防抖函数
  };

  /**
   * 是否启用标题在内样式变更
   * @param e
   */
  const handleChangeIsFocusDisplay = (e) => {
    componentForm.setFieldsValue({ isFocusDisplay: e.target.checked });
    onChange();
  };

  const handleChangeAttr = (key, value) => {
    componentForm.setFieldsValue({ [key]: value });
    onChange();
  };

  /**
   * 回填
   */
  const onChange = () => {
    console.log(componentForm.getFieldsValue());
    const { headerName, status, isFocusDisplay, newSchema, newUnit, oldSchema, oldUnit } =
      componentForm.getFieldsValue();
    const editable = status === 'editable';
    const disabled = !editable;
    const fields = [
      { schema: newSchema, icon: props.data.fields?.[0]?.icon ?? 'new', unit: newUnit },
      { schema: oldSchema, icon: props.data.fields?.[1]?.icon ?? 'old', unit: oldUnit },
    ];
    let data = {
      ...props.data,
      headerName: headerName[t('dj-LANG')],
      label: headerName[t('dj-LANG')],
      lang: {
        headerName,
        label: headerName,
      },
      editable,
      disabled,
      isFocusDisplay,
      fields,
    };
    props.change({
      isControl: true,
      value: data,
    });
  };

  return (
    <div className="component-form">
      <Form
        className="form-info"
        form={componentForm}
        name="openwindow-setting-form"
        layout="vertical"
        labelCol={{
          span: 24,
        }}
        wrapperCol={{
          span: 24,
        }}
      >
        {/* 唯一标识 */}
        <Form.Item label={t('dj-唯一标识')} name="id">
          <Input disabled />
        </Form.Item>
        {/* 标题 */}
        <Form.Item label={t('dj-标题')} name="headerName">
          <AppLangInput
            required
            size="small"
            onChange={(value) => handleChange('headerName', value)}
          />
        </Form.Item>
        {/* schema */}
        <Form.Item label="schema" name="schema">
          <Input disabled />
        </Form.Item>
        {/* path */}
        <Form.Item label="path" name="path">
          <Input disabled />
        </Form.Item>
        {/* 上传目录 */}
        <Form.Item label={t('dj-新值schema')} name="newSchema">
          <Input
            placeholder={t('dj-新值schema')}
            onChange={(e) => handleChangeAttr('newSchema', e.target.value)}
          />
        </Form.Item>
        {/* 上传目录 */}
        <Form.Item label={t('dj-新值单位schema')} name="newUnit">
          <Input
            placeholder={t('dj-新值单位schema')}
            onChange={(e) => handleChangeAttr('newUnit', e.target.value)}
          />
        </Form.Item>
        {/* 上传目录 */}
        <Form.Item label={t('dj-旧值schema')} name="oldSchema">
          <Input
            placeholder={t('dj-旧值schema')}
            onChange={(e) => handleChangeAttr('oldSchema', e.target.value)}
          />
        </Form.Item>
        {/* 上传目录 */}
        <Form.Item label={t('dj-旧值单位schema')} name="oldUnit">
          <Input
            placeholder={t('dj-旧值单位schema')}
            onChange={(e) => handleChangeAttr('oldUnit', e.target.value)}
          />
        </Form.Item>
        {/* 状态 */}
        <Form.Item label={t('dj-状态')} name="status">
          <Select disabled>
            <Select.Option value="editable">{t('普通')}</Select.Option>
            <Select.Option value="disabled">{t('禁用')}</Select.Option>
          </Select>
        </Form.Item>
        {/* 是否启用标题在内样式 */}
        <Form.Item label={null} name="isFocusDisplay" valuePropName="checked">
          <Checkbox onChange={(e) => handleChangeIsFocusDisplay(e)}>
            {t('dj-是否启用标题在内样式')}
          </Checkbox>
        </Form.Item>
      </Form>
    </div>
  );
};

export default DifferenceComponentSetting;
