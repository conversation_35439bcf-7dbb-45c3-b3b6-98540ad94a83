import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
// import './index.less';
import { t } from 'i18next';
import { ATHENA_TYPES } from '@/components/OpenwindowWorkDesignLib/utils/fields.utils';
import { Checkbox, Form, Input, InputNumber, Select } from 'antd';
import { dslI18n, getComponentList } from '../../../config';
import AppLangInput from '@/components/AppLangInput';
import { cloneDeep, debounce } from 'lodash';
import { useWorkData } from '@/components/OpenwindowWorkDesignLib/store/workData';

const { Option } = Select;

interface UploadComponentSettingProps {
  data: any;
  //   columnType: string;
  change: (data: any) => void;
}

const UploadComponentSetting = (props: UploadComponentSettingProps) => {
  const isInitialized = useRef(false);
  const [componentForm] = Form.useForm(); // 表单 [文本输入、日期选择、时间选择、数字输入、百分比]
  useEffect(() => {
    console.log('2222', props.data);
    handleInit();
  }, [props.data]);

  const { openWindowDefine, updateOpenWindowDefine, columnType, activeData } = useWorkData(
    (state) => state
  );

  /**
   * 初始化
   */
  const handleInit = () => {
    const { type, id, headerName, schema, path, editable, lang, attribute = {} } = props.data;
    const { fileCount, fileMaxSize, fileExtensions, uploadCategory, draggable } = attribute;
    const status = editable ? 'editable' : 'disabled';
    componentForm.setFieldsValue({
      id, // 唯一标识
      headerName: lang?.headerName ?? dslI18n, // 标题
      schema, // schema
      path, // path
      status, // 状态
      // 最大上传文件个数
      fileCount,
      // 单文件最大上传大小
      fileMaxSize,
      // 上传文件类型
      fileExtensions: fileExtensions.join(','),
      // 上传目录
      uploadCategory,
      // 拖拽上传
      draggable,
    });
  };

  /**
   * 多语言输入框回调
   * @param key
   * @param data
   */
  const handlePatchLang = (key: any, data: any) => {
    if (['headerName'].includes(key)) {
      // 联动处理
      componentForm.setFieldsValue({
        [key]: { ...data, label: `${data?.[t('dj-LANG')]}` },
        ['label']: { ...data, label: `${data?.[t('dj-LANG')]}` },
      });
    } else {
      componentForm.setFieldsValue({
        [key]: { ...data, label: `${data?.[t('dj-LANG')]}` },
      });
    }
    onChange();
  };

  // 创建防抖函数并保持引用不变
  const debouncedSearch = useCallback(
    debounce((key, data) => handlePatchLang(key, data), 500),
    [] // 依赖项为空，确保防抖函数只创建一次
  );

  // 输入框变化事件
  const handleChange = (key: any, data: any) => {
    debouncedSearch(key, data); // 触发防抖函数
  };

  const handleChangeAttr = (key, value) => {
    componentForm.setFieldsValue({ [key]: value });
    onChange();
  };

  /**
   * 回填
   */
  const onChange = () => {
    console.log(componentForm.getFieldsValue());
    const {
      headerName,
      status,
      fileCount,
      fileMaxSize,
      fileExtensions,
      uploadCategory,
      draggable,
    } = componentForm.getFieldsValue();
    const editable = status === 'editable';
    const disabled = !editable;
    let data = {
      ...props.data,
      headerName: headerName[t('dj-LANG')],
      label: headerName[t('dj-LANG')],
      lang: {
        headerName,
        label: headerName,
      },
      editable,
      disabled,
      attribute: {
        ...(props.data.attribute ?? {}),
        fileCount,
        fileMaxSize,
        fileExtensions: fileExtensions.split(','),
        uploadCategory,
        deleteCategory: [uploadCategory],
        readCategory: [uploadCategory],
        draggable,
      },
    };

    props.change({
      isControl: true,
      value: data,
    });
  };

  return (
    <div className="component-form">
      <Form
        className="form-info"
        form={componentForm}
        name="openwindow-setting-form"
        layout="vertical"
        labelCol={{
          span: 24,
        }}
        wrapperCol={{
          span: 24,
        }}
      >
        {/* 唯一标识 */}
        <Form.Item label={t('dj-唯一标识')} name="id">
          <Input disabled />
        </Form.Item>
        {/* 标题 */}
        <Form.Item label={t('dj-标题')} name="headerName">
          <AppLangInput
            required
            size="small"
            onChange={(value) => handleChange('headerName', value)}
          />
        </Form.Item>
        {/* schema */}
        <Form.Item label="schema" name="schema">
          <Input disabled />
        </Form.Item>
        {/* path */}
        <Form.Item label="path" name="path">
          <Input disabled />
        </Form.Item>
        {/* 状态 */}
        <Form.Item label={t('dj-状态')} name="status">
          <Select disabled>
            <Select.Option value="editable">{t('普通')}</Select.Option>
            <Select.Option value="disabled">{t('禁用')}</Select.Option>
          </Select>
        </Form.Item>
        {/* 最大上传文件个数 */}
        <Form.Item label={t('最大上传文件个数')} name="fileCount">
          <InputNumber
            placeholder={t('dj-请输入')}
            defaultValue={props.data.attribute?.fileCount}
            value={componentForm.getFieldValue('fileCount')}
            onChange={(value) => handleChangeAttr('fileCount', value)}
          />
        </Form.Item>
        {/* 单文件最大上传大小 */}
        <Form.Item label={t('最大上传文件个数')} name="fileMaxSize">
          <InputNumber
            placeholder={t('dj-请输入')}
            defaultValue={props.data.attribute?.fileMaxSize}
            value={componentForm.getFieldValue('fileMaxSize')}
            onChange={(value) => handleChangeAttr('fileMaxSize', value)}
          />
        </Form.Item>
        {/* 上传文件类型 */}
        <Form.Item label={t('上传文件类型(多个以英文逗号 , 分隔)')} name="fileExtensions">
          <Input
            placeholder={t('uploadTypePlaceholder')}
            onChange={(e) => handleChangeAttr('fileExtensions', e.target.value)}
          />
        </Form.Item>
        {/* 上传目录 */}
        <Form.Item label={t('dj-上传目录')} name="uploadCategory">
          <Input
            placeholder={t('dj-上传目录')}
            onChange={(e) => handleChangeAttr('uploadCategory', e.target.value)}
          />
        </Form.Item>
        {/* 拖拽上传 */}
        <Form.Item label={null} name="draggable" valuePropName="checked">
          <Checkbox onChange={(e) => handleChangeAttr('draggable', e.target.checked)}>
            {t('dj-拖拽上传')}
          </Checkbox>
        </Form.Item>
      </Form>
    </div>
  );
};

export default UploadComponentSetting;
