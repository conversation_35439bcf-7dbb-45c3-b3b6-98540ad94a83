import { ILangInfo } from '@/components/ActionModal/api';

export interface ILcdpOptionsItemData {
  value: any;
  title: string;
  lang: ILangInfo;
  [propName: string]: any;
}

export interface IOptionsModalProps {
  visible: boolean;
  onChange: (data: ILcdpOptionsItemData) => void;
  onCancel: () => void;
  data: ILcdpOptionsItemData | null;
}

export type ValueType = 'string' | 'number' | 'boolean';

export interface MultiTypeInputProps {
  value: string | number | boolean;
  placeholder: string;
  changeValue: (value: ValueType) => void;
}

export interface ILangDetailInfo {
  en_US: string;
  zh_CN: string;
  zh_TW: string;
}
