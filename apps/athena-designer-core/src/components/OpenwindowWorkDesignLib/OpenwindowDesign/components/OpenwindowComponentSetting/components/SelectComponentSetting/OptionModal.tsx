import React, { useEffect, useState } from 'react';
import { Form, Modal } from 'antd';
import { useTranslation } from 'react-i18next';

import i18n from 'i18next';
import './OptionModal.less';
import AppLangInput from '@/components/AppLangInput';
import { MultiTypeInput } from './MultiTypeInput';
import { ILangDetailInfo } from './types';
import { isEmpty } from 'lodash';

const FormItem = Form.Item;

function OptionModal(props: any) {
  const { visible, data, index, onChange, onCancel } = props;

  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [lang, setLang] = useState<any>(data?.lang?.title);
  const [optionValue, setOptionValue] = useState(data?.value);

  useEffect(() => {
    if (visible && data) {
      form?.setFieldsValue({
        ...data,
      });
      setLang(data?.lang?.title);
      setOptionValue(data?.value);
    } else if (!visible) {
      form?.resetFields();
      setLang(undefined);
    }
  }, [visible, data]);

  const doOk = async () => {
    try {
      const value = await form.validateFields();
      value.title = lang[i18n?.language || 'zh_CN'] ?? '';
      onChange(value);
    } catch (error) {
      console.log(error);
    }
  };

  const doLangChange = async (info: any) => {
    form.setFieldValue(['lang', 'title'], info);
    setLang(info);
  };

  const doValueChange = (info: any) => {
    form.setFieldValue(['value'], info);
    setOptionValue(info);
    form.validateFields();
  };

  return (
    <Modal
      destroyOnClose
      classNames={{
        body: 'options-modal-body',
      }}
      title={index > -1 ? t('dj-修改选项') : t('dj-增加选项')}
      open={visible}
      width={440}
      okText={t('dj-确定')}
      onOk={doOk}
      cancelText={t('dj-取消')}
      onCancel={onCancel}
    >
      <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
        <FormItem
          name={['lang', 'title']}
          rules={[
            {
              required: true,
              validator(_, value: ILangDetailInfo, callback) {
                if (!value?.['zh_CN'] || !value?.['zh_TW']) {
                  callback(`${t('dj-请输入')}`);
                } else {
                  callback();
                }
              },
            },
          ]}
          label={t('dj-标题')}
          colon={false}
        >
          <AppLangInput
            className={'ath-lang-input'}
            size={'small'}
            title={t('dj-显示值')}
            onChange={doLangChange}
            value={lang}
            required={true}
            placeholder={t('dj-请输入')}
          ></AppLangInput>
        </FormItem>
        <FormItem
          name="value"
          rules={[{ required: true, message: t('dj-请输入') }]}
          label={t('dj-选项值')}
          colon={false}
        >
          <MultiTypeInput
            value={optionValue}
            placeholder={t('dj-请输入')}
            changeValue={doValueChange}
          />
        </FormItem>
      </Form>
    </Modal>
  );
}

export { OptionModal };
