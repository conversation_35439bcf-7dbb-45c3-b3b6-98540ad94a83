import { Button, Modal } from 'antd';
import { t } from 'i18next';
import React from 'react';

interface ConfirmModalProps {
  visible: boolean;
  dataConnectorId: string;
  onConfirm: (value: string, clear: boolean) => void;
  onCancel: () => void;
}

const ConfirmModal: React.FC<ConfirmModalProps> = (props: ConfirmModalProps) => {
  return (
    <Modal
      title={null}
      open={props.visible}
      onCancel={() => {
        props.onCancel();
      }}
      footer={[
        <Button
          type="primary"
          onClick={() => {
            props.onConfirm(props.dataConnectorId, true);
          }}
        >
          {t('dj-清空')}
        </Button>,
        <Button
          type="primary"
          onClick={() => {
            props.onConfirm(props.dataConnectorId, false);
          }}
        >
          {t('dj-保留')}
        </Button>,
        <Button
          onClick={() => {
            props.onCancel();
          }}
        >
          {t('dj-取消')}
        </Button>,
      ]}
    >
      <p style={{ textAlign: 'center', padding: '20px', fontSize: '14px' }}>
        {t('dj-更换数据源可能会导致配置无效，是否需要清空相关配置？')}
      </p>
    </Modal>
  );
};

export default ConfirmModal;
