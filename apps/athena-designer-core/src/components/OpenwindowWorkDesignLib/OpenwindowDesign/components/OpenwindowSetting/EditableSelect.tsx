import React, { useState } from 'react';
import { AutoComplete, Input, Tag } from 'antd';
import { t } from 'i18next';

interface EditableSelectProps {
  valueScriptList: any[]; // 建议替换具体类型
  value?: any; // 表单注入的 value
  onChange?: (value: any) => void; // 表单注入的 onChange
}

const EditableSelect: React.FC<EditableSelectProps> = (props: EditableSelectProps) => {
  // 处理输入变化
  const handleSearch = (text: string) => {
    props.onChange?.(text);
  };

  // 处理选择或确认输入
  const handleSelect = (value: string) => {
    props.onChange?.(value);
  };

  // 自定义渲染选项
  const renderOptions = () => {
    return props.valueScriptList.map((opt) => ({
      value: opt.code,
      label: (
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <span>{opt.name}</span>
        </div>
      ),
    }));
  };

  return (
    <div className="editable-select">
      <AutoComplete
        value={props.value}
        options={renderOptions()}
        style={{ width: '100%' }}
        onSelect={handleSelect}
        onChange={handleSearch}
        // 允许自定义输入
        filterOption={(value, option) =>
          option!.value.toUpperCase().indexOf(props.value.toUpperCase()) !== -1
        }
      >
        <Input.Search placeholder={t('dj-请输入')} onSearch={handleSelect} />
      </AutoComplete>
    </div>
  );
};

export default EditableSelect;
