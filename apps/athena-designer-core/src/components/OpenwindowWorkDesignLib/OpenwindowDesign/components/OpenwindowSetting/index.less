.openwindow-setting {
  height: calc(100% - 20px);
  .breadcrumb-nav {
    height: 48px;
    display: flex;
    flex-wrap: wrap;
    border-bottom: 1px solid rgba(31, 56, 88, 0.1);
    font-size: 12px;
    padding: 6px 16px;
    align-items: center;
    width: 100%;
    .breadcrumb {
      background-color: #fff;
      margin-bottom: 0 !important;
      padding: 0;

      .breadcrumb-item.active {
        a {
          cursor: text;
          color: #6c757d;
        }
      }
      .breadcrumb-text-ellipsis {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        min-width: 20px;
      }

      .breadcrumb-item + .breadcrumb-item::before {
        float: left;
        padding-right: 0.5rem;
        color: #6c757d;
        content: '>';
      }
    }
  }
  .set-form-container {
    height: calc(100% - 40px);
    padding: 16px;
    overflow-y: auto;

    .ant-form-item {
      margin-bottom: 8px;
      .ant-form-item-label > label {
        font-size: 12px;
        color: #0009;
      }

      .ant-select,
      .ant-tree-select {
        width: 100%;
      }

      .exten-info {
        color: #2012d9;
        cursor: pointer;
      }

      .condition-item {
        display: flex;
        align-items: center;
        height: 32px;
        margin-bottom: 8px;
        justify-content: space-between;
        background: #f1f2fb;
        border-radius: 2px;
        padding: 0px 8px;
        .title {
          max-width: 118px;
          color: #333333;
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .actions {
          text-align: right;
          .back-fields-info {
            color: #999999;
          }
          .anticon {
            color: #6868ae;
            margin-left: 4px;
          }
        }
      }

      .condition-item.dragging {
        // opacity: 0.5;
        background-color: #f1f2fb;
      }

      .drag-handle {
        cursor: move;
        svg {
          cursor: move !important;
        }
      }

      .condition-btn {
        .add-btn {
          width: 100%;
          background: rgba(104, 104, 174, 0) !important;
          color: #6868ae !important;

          &:hover {
            background: #f4f5ff !important;
          }
        }
      }
    }
    .form-item-noerror {
      margin-bottom: 0px;
    }
    .required-label {
      color: #ea3d46;
    }
    .ant-input-affix-wrapper {
      width: 100% !important;
    }
  }
}
.back-fills .ant-dropdown-menu {
  max-height: 200px;
  overflow-y: auto;
}
.tree-select {
  ath-tree-node-title {
    overflow: visible !important;
  }
}
.cdk-drag-preview {
  display: flex;
  align-items: center;
  height: 32px;
  justify-content: space-between;
  background: #f1f2fb;
  border-radius: 2px;
  padding: 0px 8px;
  .title {
    width: 124px;
    color: #333333;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.cdk-drag-placeholder {
  opacity: 0;
}
.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.req-node {
  white-space: nowrap;
}

.field-modal {
  .ant-modal-title {
    font-size: 14px;
  }
  .ant-modal-body {
    padding: 24px 0;
  }
}

.editable-select {
  .ant-btn {
    display: none;
  }
}