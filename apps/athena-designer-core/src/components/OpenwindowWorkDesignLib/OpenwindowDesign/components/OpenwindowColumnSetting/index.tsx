import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react';
import './index.less';
import { t } from 'i18next';
import { Form, Input, InputNumber } from 'antd';
import AppLangInput from '@/components/AppLangInput';
import { debounce } from 'lodash';
import { dslI18n } from '../../config';

interface OpenwindowColumnSettingProps {
  data: any;
  columnType: string;
  change: (data: any) => void;
}

const OpenwindowColumnSetting = (props: OpenwindowColumnSettingProps) => {
  const [columnForm] = Form.useForm();
  useEffect(() => {
    handleInit();
  }, [props.data]);

  /**
   * 初始化
   */
  const handleInit = () => {
    console.log(props.data);
    const { id, width, lang } = props.data ?? {};
    columnForm.setFieldsValue({
      id, // 唯一标识
      width,
      headerName: lang?.headerName ?? dslI18n, // 标题
    });
  };

  // 创建防抖函数并保持引用不变
  const debouncedSearch = useCallback(
    debounce((key, data) => handlePatchLang(key, data), 500),
    [] // 依赖项为空，确保防抖函数只创建一次
  );

  // 输入框变化事件
  const handleChange = (key: any, data: any) => {
    debouncedSearch(key, data); // 触发防抖函数
  };

  /**
   * 多语言输入框回调
   * @param key
   * @param data
   */
  const handlePatchLang = (key: any, data: any) => {
    columnForm.setFieldsValue({
      [key]: { ...data, label: `${data?.[t('dj-LANG')]}` },
    });
    onChange();
  };

  /**
   * 输入框、选择框回调
   * @param key
   * @param data
   */
  const handleChangeWidth = (value: any) => {
    columnForm.setFieldsValue({ width: value });
    onChange();
  };

  /**
   * 回填
   */
  const onChange = () => {
    const { headerName, width } = columnForm.getFieldsValue();
    let data = {
      ...props.data,
      headerName: headerName[t('dj-LANG')],
      width,
      lang: {
        ...(props.data?.lang ?? {}),
        headerName,
      },
    };
    props.change({
      isControl: true,
      value: data,
    });
  };

  return (
    <>
      <div className="openwindow-setting">
        <div className="breadcrumb-nav">
          <nav aria-label="breadcrumb">
            <ol
              className="breadcrumb"
              style={{ display: 'flex', flexWrap: 'nowrap', maxWidth: '240px' }}
            >
              <li className="breadcrumb-item breadcrumb-text-ellipsis" style={{ minWidth: '12px' }}>
                <a>
                  <span className="bi bi-folder" style={{ marginRight: 0 }}></span>
                </a>
              </li>
              <li className="breadcrumb-item breadcrumb-text-ellipsis active">
                <a>{props.data?.lang?.label?.[t('dj-LANG')]}</a>
              </li>
            </ol>
          </nav>
        </div>
        {/* 字段属性 */}
        <div className="set-form-container">
          <Form
            className="form-info"
            form={columnForm}
            name="openwindow-setting-form"
            layout="vertical"
            labelCol={{
              span: 24,
            }}
            wrapperCol={{
              span: 24,
            }}
          >
            {/* 唯一标识 */}
            <Form.Item label={t('dj-唯一标识')} name="id">
              <Input disabled />
            </Form.Item>
            {/* 标题 */}
            <Form.Item label={t('dj-标题')} name="headerName">
              <AppLangInput
                required
                size="small"
                onChange={(value) => handleChange('headerName', value)}
              />
            </Form.Item>
            {/* 宽度 */}
            <Form.Item className="colnum-width" label={t('宽度')} name="width">
              <InputNumber
                defaultValue={props.data?.width}
                value={columnForm.getFieldValue('width')}
                onChange={handleChangeWidth}
              />
              px
            </Form.Item>
          </Form>
        </div>
      </div>
    </>
  );
};

export default OpenwindowColumnSetting;
