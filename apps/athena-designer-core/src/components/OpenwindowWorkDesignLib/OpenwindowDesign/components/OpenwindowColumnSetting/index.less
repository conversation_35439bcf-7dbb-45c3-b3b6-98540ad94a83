.openwindow-setting {
  height: calc(100% - 20px);
  .breadcrumb-nav {
    height: 48px;
    display: flex;
    flex-wrap: wrap;
    border-bottom: 1px solid rgba(31, 56, 88, 0.1);
    font-size: 12px;
    padding: 6px 16px;
    align-items: center;
    width: 100%;
    .breadcrumb {
      background-color: #fff;
      margin-bottom: 0 !important;
      padding: 0;

      .breadcrumb-item.active {
        a {
          cursor: text;
        }
      }
      .breadcrumb-text-ellipsis {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        min-width: 20px;
      }

      .breadcrumb-item + .breadcrumb-item::before {
        float: left;
        padding-right: 0.5rem;
        color: #6c757d;
        content: '>';
      }
    }
  }
  .set-form-container {
    height: calc(100% - 40px);
    padding: 16px;
    overflow-y: auto;

    // ::ng-deep .ant-form-item {
    //   margin-bottom: 8px;
    //   .ant-form-item-label > label {
    //     font-size: 12px;
    //     color: #0009;
    //   }

    //   .ant-select,
    //   .ant-tree-select {
    //     width: 100%;
    //   }

    //   .exten-info {
    //     color: #2012d9;
    //     cursor: pointer;
    //   }

    //   .condition-item {
    //     display: flex;
    //     align-items: center;
    //     height: 32px;
    //     margin-bottom: 8px;
    //     justify-content: space-between;
    //     background: #f1f2fb;
    //     border-radius: 2px;
    //     padding: 0px 8px;
    //     .title {
    //       width: 130px;
    //       color: #333333;
    //       font-weight: 500;
    //       overflow: hidden;
    //       text-overflow: ellipsis;
    //       white-space: nowrap;
    //     }
    //     .actions {
    //       text-align: right;
    //       .back-fields-info {
    //         color: #999999;
    //       }
    //       .anticon {
    //         color: #6868ae;
    //         margin-left: 4px;
    //       }
    //     }
    //   }
    // }
    .required-label {
      color: #ea3d46;
    }
    .ant-input-number {
      width: 100%;
    }
    .colnum-width {
      .ant-input-number {
        width: calc(100% - 20px);
        margin-right: 4px;
      }
    }
  }
  //   ::ng-deep .ant-input-number {
  //     width: 100% !important;
  //   }
}
