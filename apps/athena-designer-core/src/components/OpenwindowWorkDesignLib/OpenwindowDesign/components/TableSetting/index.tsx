import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import './index.less';
import { t } from 'i18next';
import { Form, Input } from 'antd';

interface TableSettingProps {
  data: any;
  columnType: string;
  change: (data: any) => void;
}

const TableSetting = (props: TableSettingProps) => {
  const [tableForm] = Form.useForm();
  useEffect(() => {
    handleInit();
  }, [props.data]);

  /**
   * 初始化
   */
  const handleInit = () => {
    const { id } = props.data;
    tableForm.setFieldsValue({
      id, // 唯一标识
    });
  };

  return (
    <>
      <div className="openwindow-setting">
        <div className="breadcrumb-nav">
          <nav aria-label="breadcrumb">
            <ol
              className="breadcrumb"
              style={{ display: 'flex', flexWrap: 'nowrap', maxWidth: '240px' }}
            >
              <li className="breadcrumb-item breadcrumb-text-ellipsis" style={{ minWidth: '12px' }}>
                <a>
                  <span className="bi bi-folder" style={{ marginRight: 0 }}></span>
                </a>
              </li>
              <li className="breadcrumb-item breadcrumb-text-ellipsis">
                <a style={{ color: 'rgb(33, 37, 41)' }}>{t('dj-开窗')}</a>
              </li>
              <li
                className="breadcrumb-item breadcrumb-text-ellipsis active"
                style={{ paddingLeft: '8px' }}
              >
                <a>{t('dj-表格')}</a>
              </li>
            </ol>
          </nav>
        </div>
        {/* 字段属性 */}
        <div className="set-form-container">
          <Form
            className="form-info"
            form={tableForm}
            name="openwindow-setting-form"
            layout="vertical"
            labelCol={{
              span: 24,
            }}
            wrapperCol={{
              span: 24,
            }}
          >
            {/* 唯一标识 */}
            <Form.Item label={t('dj-唯一标识')} name="id">
              <Input disabled />
            </Form.Item>
          </Form>
        </div>
      </div>
    </>
  );
};

export default TableSetting;
