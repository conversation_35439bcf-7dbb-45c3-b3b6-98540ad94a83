import { ATHENA_TYPES, AthenaDataType } from '../utils/fields.utils';
import { v4 as uuid } from 'uuid';

/**
 * 获取可选dataType的类型
 * @param type
 * @param dataType
 * @returns
 */
export function getComponentList(type: string, dataType: string): string[] {
  if (type === ATHENA_TYPES.INPUT && (dataType as AthenaDataType) === AthenaDataType.NUMERIC) {
    return componentList.find((c) => ATHENA_TYPES.INPUT_NUMBER === c.type)?.dataTypeList ?? [];
  }
  return componentList.find((c) => c.type === type)?.dataTypeList ?? [];
}

/**
 * 控件可选类型
 */
export const componentList = [
  {
    name: '日期选择',
    type: ATHENA_TYPES.DATEPICKER,
    dataTypeList: [AthenaDataType.DATE, AthenaDataType.DATETIME],
  },
  { name: '时间选择', type: ATHENA_TYPES.TIMEPICKER, dataTypeList: [AthenaDataType.TIME] },
  {
    name: '文本输入',
    type: ATHENA_TYPES.INPUT,
    dataTypeList: [AthenaDataType.STRING, AthenaDataType.TIME],
  },
  { name: '数字输入', type: ATHENA_TYPES.INPUT_NUMBER, dataTypeList: [AthenaDataType.NUMERIC] },
  // { name: '表格附件', type: ATHENA_TYPES.FILE_UPLOAD, dataTypeList: [AthenaDataType.OBJECT] },
  // { name: '百分比', type: ATHENA_TYPES.PERCENT_INPUT, dataTypeList: [AthenaDataType.NUMERIC] },
  // { name: '金额', type: ATHENA_TYPES.AMOUNT_INPUT, dataTypeList: [AthenaDataType.NUMERIC] },
  // { name: '计量组件', type: ATHENA_TYPES.MEASURE, dataTypeList: [AthenaDataType.NUMERIC] },
  // { name: '差异值', type: ATHENA_TYPES.DIFFERENCE_CALCULATION },
  {
    name: '文本',
    type: ATHENA_TYPES.LABEL,
    dataTypeList: [AthenaDataType.STRING, AthenaDataType.TIME],
  },
  { name: '下拉多选', type: ATHENA_TYPES.SELECT_MULTIPLE, dataTypeList: [AthenaDataType.ARRAY] },
  {
    name: '文本域',
    type: ATHENA_TYPES.TEXTAREA,
    dataTypeList: [AthenaDataType.STRING, AthenaDataType.TIME],
  },
  // { name: '表单附件', type: ATHENA_TYPES.FORM_UPLOAD, dataTypeList: [AthenaDataType.OBJECT] },
  {
    name: '收货地址',
    type: ATHENA_TYPES.ADDRESS,
    dataTypeList: [AthenaDataType.STRING, AthenaDataType.TIME],
  },
  // {
  //   name: '方案选择',
  //   type: ATHENA_TYPES.PLAN_SELECT,
  //   dataTypeList: [AthenaDataType.STRING, AthenaDataType.TIME],
  // },
  // {
  //   name: '动态按钮',
  //   type: ATHENA_TYPES.BUTTON,
  //   dataTypeList: [AthenaDataType.STRING, AthenaDataType.TIME],
  // },
  // { name: '人员选择', type: ATHENA_TYPES.PERSON_SELECT, dataTypeList: [AthenaDataType.ARRAY] },
  {
    name: '下拉单选',
    type: ATHENA_TYPES.SELECT,
    dataTypeList: [AthenaDataType.STRING, AthenaDataType.TIME],
  },
  { name: '复选框', type: ATHENA_TYPES.CHECKBOX, dataTypeList: [AthenaDataType.BOOLEAN] },
  {
    name: '单选框',
    type: ATHENA_TYPES.RADIO_GROUP,
    dataTypeList: [AthenaDataType.STRING, AthenaDataType.TIME],
  },
];

/**
 * 默认dsl控件多语言
 * @returns
 */
export const dslI18n = { label: '', zh_CN: '', zh_TW: '', en_US: '' };

/**
 * 获取dsl控件的国际化名称
 * @param ctype 控件类型
 * @param type 国际化字段
 * @returns
 */
export function getDslComponentI18n(ctype: string, type?: string): any {
  switch (ctype) {
    case ATHENA_TYPES.DATEPICKER:
      if (type === 'placeholder') {
        return { label: 'yyyyMMdd', zh_CN: 'yyyyMMdd', zh_TW: 'yyyyMMdd', en_US: 'yyyyMMdd' };
      }
      return { label: '日期选择', zh_CN: '日期选择', zh_TW: '日期選擇', en_US: 'DatePicker' };
    case ATHENA_TYPES.TIMEPICKER:
      if (type === 'placeholder') {
        return { label: 'hhmmss', zh_CN: 'hhmmss', zh_TW: 'hhmmss', en_US: 'hhmmss' };
      }
      return { label: '时间选择', zh_CN: '时间选择', zh_TW: '時間選擇', en_US: 'TimePicker' };
    case ATHENA_TYPES.INPUT:
      return { label: '文本输入', zh_CN: '文本输入', zh_TW: '文本輸入', en_US: 'Input' };
    case ATHENA_TYPES.INPUT_NUMBER:
      return { label: '数字输入', zh_CN: '数字输入', zh_TW: '數字輸入', en_US: 'Number' };
    // case ATHENA_TYPES.FILE_UPLOAD:
    //   return { label: '表格附件', zh_CN: '表格附件', zh_TW: '附檔', en_US: 'Table Upload' };
    // case ATHENA_TYPES.PERCENT_INPUT:
    //   return { label: '百分比', zh_CN: '百分比', zh_TW: '百分比', en_US: 'Percent' };
    // case ATHENA_TYPES.AMOUNT_INPUT:
    //   return { label: '金额', zh_CN: '金额', zh_TW: '金額', en_US: 'Amount' };
    // case ATHENA_TYPES.MEASURE:
    //   return { label: '计量组件', zh_CN: '计量组件', zh_TW: '計量組件', en_US: 'Measure' };
    // case ATHENA_TYPES.DIFFERENCE_CALCULATION:
    //   return { label: '差异值', zh_CN: '差异值', zh_TW: '差异值', en_US: 'Difference calculation' };
    case ATHENA_TYPES.LABEL:
      return { label: '文本', zh_CN: '文本', zh_TW: '文本', en_US: 'Text' };
    case ATHENA_TYPES.SELECT_MULTIPLE:
      return { label: '多选框', zh_CN: '多选框', zh_TW: '多選框', en_US: 'Multiple choice' };
    case ATHENA_TYPES.TEXTAREA:
      return { label: '文本域', zh_CN: '文本域', zh_TW: '文本域', en_US: 'Textarea' };
    // case ATHENA_TYPES.FORM_UPLOAD:
    //   return { label: '表单附件', zh_CN: '表单附件', zh_TW: '表格附檔', en_US: 'Form attachment' };
    case ATHENA_TYPES.ADDRESS:
      return {
        label: '收货地址',
        zh_CN: '收货地址',
        zh_TW: '收貨地址',
        en_US: 'Receiving Address',
      };
    // case ATHENA_TYPES.PLAN_SELECT:
    //   return { label: '方案选择', zh_CN: '方案选择', zh_TW: '方案選擇', en_US: 'Paln Select' };
    // case ATHENA_TYPES.BUTTON:
    //   return { label: '动态按钮', zh_CN: '动态按钮', zh_TW: '動態按鈕', en_US: 'Dynamic Button' };
    // case ATHENA_TYPES.PERSON_SELECT:
    //   return { label: '人员选择', zh_CN: '人员选择', zh_TW: '人員選擇', en_US: 'person_select' };
    case ATHENA_TYPES.SELECT:
      return { label: '选择框', zh_CN: '选择框', zh_TW: '選擇框', en_US: 'Select' };
    case ATHENA_TYPES.CHECKBOX:
      return { label: '复选框', zh_CN: '复选框', zh_TW: '複選框', en_US: 'checkbox' };
    case ATHENA_TYPES.RADIO_GROUP:
      return { label: '单选框', zh_CN: '单选框', zh_TW: '單選框', en_US: 'Radio' };
    default:
      return dslI18n;
  }
}

/**
 * 构建layout数据 (自动生成时使用)
 * @param data
 * @returns
 */
export function combinLayout(field: any): any {
  return [combinDslComponent(field, ATHENA_TYPES.ATHENA_TABLE)];
}

/**
 * 构建Dsl单元格
 * @param component
 * @returns
 */
export function combinDslColumnDef(component) {
  return {
    headerName: component?.headerName ?? component?.title,
    path: component.path,
    level: 0,
    columns: [component],
    width: 160,
    lang: {
      headerName: component.lang?.headerName ?? component.lang?.title,
    },
    id: uuid().replace(/-/g, '').toLocaleUpperCase(),
  };
}

/**
 * 构建dsl标准控件
 * @param field
 * @param type
 * @param serviceAttr
 * @returns
 */
export function combinDslStandardComponent(
  field: any,
  type: string,
  serviceAttr: { language: string } = { language: 'zh_CN' }
): any {
  const { data_name = '', fullPath = '', description = {} } = field ?? {};
  const { language = 'zh_CN' } = serviceAttr;
  const i18n = getDslComponentI18n(type);
  const placeholderI18n = getDslComponentI18n(type, 'placeholder');
  description['label'] = description?.[language] ?? i18n[language];
  description['zh_CN'] = description?.['zh_CN'] ?? description?.[language] ?? i18n['zh_CN'];
  description['zh_TW'] = description?.['zh_TW'] ?? description?.[language] ?? i18n['zh_TW'];
  description['en_US'] = description?.['en_US'] ?? description?.[language] ?? i18n['en_US'];
  const pDescription = {
    label: description?.[language] ?? placeholderI18n[language],
    zh_CN: description?.['zh_CN'] ?? description?.[language] ?? placeholderI18n['zh_CN'],
    zh_TW: description?.['zh_TW'] ?? description?.[language] ?? placeholderI18n['zh_TW'],
    en_US: description?.['en_US'] ?? description?.[language] ?? placeholderI18n['en_US'],
  };
  return {
    type,
    schema: data_name,
    path: fullPath.substring(0, fullPath.lastIndexOf('.')),
    dataType: AthenaDataType.STRING,
    editable: false, // 默认禁用
    disabled: true,
    headerName: description[language],
    label: description[language],
    placeholder: pDescription[language],
    lang: {
      headerName: { ...description },
      placeholder: { ...pDescription },
      label: { ...description },
    },
    isFocusDisplay: false,
    id: uuid().replace(/-/g, '').toLocaleUpperCase(),
    sortable: true,
    filterable: true,
  };
}

/**
 * 构建dsl控件
 * @param field
 * @param type
 * @param serviceAttr
 * @returns
 */
export function combinDslComponent(
  field: any,
  type: string,
  serviceAttr: { language: string } = { language: 'zh_CN' }
): any {
  switch (type) {
    case ATHENA_TYPES.ATHENA_TABLE:
      return {
        type,
        path: '',
        dataType: AthenaDataType.ARRAY,
        checkbox: false,
        rowDelete: false,
        rowIndex: false,
        rowSpanTree: false,
        schema: field.data_name,
        id: uuid().replace(/-/g, '').toLocaleUpperCase(),
        columnDefs: (field.field ?? []).map((f) =>
          combinDslColumnDef(combinDslComponent(f, ATHENA_TYPES.INPUT, serviceAttr))
        ),
        allFields: [],
        setting: {
          orderFields: [],
          hideDefaultToolbar: [],
          options: [],
        },
        hideDefaultToolbar: [],
        saveColumnsWidth: true,
        suppressAutoAddRow: false,
        disabledUserDefined: true,
        tableTitle: '',
        lang: {
          tableTitle: {
            zh_CN: '',
            zh_TW: '',
            en_US: '',
          },
        },
        checkboxOperation: false,
        height: null,
        rowHeight: null,
        openRowHeight: true,
      };
    case ATHENA_TYPES.DATEPICKER:
      return {
        ...combinDslStandardComponent(field, type, serviceAttr),
        dataType: AthenaDataType.DATE,
      };
    case ATHENA_TYPES.TIMEPICKER:
      return {
        ...combinDslStandardComponent(field, type, serviceAttr),
        dataType: AthenaDataType.TIME,
        rowGroupable: false,
        width: 83,
        level: 1,
      };
    case ATHENA_TYPES.INPUT:
      return combinDslStandardComponent(field, type, serviceAttr);
    case ATHENA_TYPES.INPUT_NUMBER:
      return {
        ...combinDslStandardComponent(field, type, serviceAttr),
        type: ATHENA_TYPES.INPUT,
        dataType: AthenaDataType.NUMERIC,
        dataPrecision: {},
        min: undefined,
        max: undefined,
        step: 1,
      };
    case ATHENA_TYPES.FILE_UPLOAD:
      const fileUpload = {
        ...combinDslStandardComponent(field, type, serviceAttr),
        dataType: AthenaDataType.OBJECT,
        rowGroupable: false,
        width: 83,
        level: 1,
        attribute: {
          editable: true,
          fileCount: 100,
          fileMaxSize: 104857600,
          fileExtensions: [],
          uploadEnable: true,
          // uploadCategory: application,
          deleteEnable: true,
          // deleteCategory: [application],
          readEnable: true,
          // readCategory: [application],
          draggable: false,
        },
      };
      Reflect.deleteProperty(fileUpload.lang, 'placeholder');
      Reflect.deleteProperty(fileUpload, 'placeholder');
      return fileUpload;
    case ATHENA_TYPES.PERCENT_INPUT:
      return {
        ...combinDslStandardComponent(field, type, serviceAttr),
        dataType: AthenaDataType.NUMERIC,
        rowGroupable: false,
        width: 83,
        level: 1,
      };
    case ATHENA_TYPES.AMOUNT_INPUT:
      return {
        ...combinDslStandardComponent(field, type, serviceAttr),
        dataType: AthenaDataType.NUMERIC,
        rowGroupable: false,
        width: 83,
        level: 1,
        thousandthPercentile: true,
        showTitleIcon: 'N',
        relationSchemas: [field.data_name],
        important: false,
        isNavigate: false,
        currencyField: '',
        queryCurrencyApi: {
          tmAction: {
            sequence: 0,
            type: 'ESP',
            actionId: 'esp_currency.parameter.data.get',
            actionParams: [
              {
                name: 'currency_data.currency_no',
                type: 'RAW_ARRAY_PARAS',
                value: 'currency',
                required: false,
              },
            ],
            override: false,
          },
        },
        queryCurrencyAPIReturnFields: {
          query_field: '',
          decimal_places_type: 'decimal_places_type',
          decimal_places: 'amount_decimal_places',
        },
        displayCurrency: true,
        cutZero: false,
      };
    case ATHENA_TYPES.MEASURE:
      return {
        ...combinDslStandardComponent(field, type, serviceAttr),
        dataType: AthenaDataType.NUMERIC,
        rowGroupable: false,
        width: 83,
        level: 1,
        thousandthPercentile: true,
        showTitleIcon: 'N',
        relationSchemas: [field.data_name],
        important: false,
        isNavigate: false,
        queryCurrencyApi: {
          tmAction: {
            sequence: 0,
            type: 'ESP',
            actionId: 'esp_unit.decimal.places.get',
            actionParams: [
              {
                name: 'unit_data.unit_no',
                type: 'RAW_ARRAY_PARAS',
                value: 'unit',
                required: false,
              },
            ],
            override: false,
          },
        },
        queryCurrencyAPIReturnFields: {
          decimal_places: 'unit_decimal_places',
        },
        cutZero: false,
        displayUnit: true,
        currencyField: undefined,
        unitSchema: undefined,
      };
    case ATHENA_TYPES.DIFFERENCE_CALCULATION:
      const differenceCalculation = {
        ...combinDslStandardComponent(field, type, serviceAttr),
        dataType: undefined,
        rowGroupable: false,
        width: 83,
        level: 1,
        fields: [
          {
            schema: 'after_change_qty',
            icon: 'new',
            unit: 'unit_no',
          },
          {
            schema: 'before_change_qty',
            icon: 'old',
            unit: 'unit_no',
          },
        ],
      };
      Reflect.deleteProperty(differenceCalculation.lang, 'placeholder');
      Reflect.deleteProperty(differenceCalculation, 'placeholder');
      return differenceCalculation;
    case ATHENA_TYPES.LABEL:
      const label = {
        ...combinDslStandardComponent(field, type, serviceAttr),
        rowGroupable: false,
        width: 83,
        level: 1,
        important: undefined,
        className: undefined,
      };
      Reflect.deleteProperty(label.lang, 'placeholder');
      Reflect.deleteProperty(label, 'placeholder');
      return label;
    case ATHENA_TYPES.SELECT_MULTIPLE:
      return {
        ...combinDslStandardComponent(field, type, serviceAttr),
        dataType: AthenaDataType.ARRAY,
        rowGroupable: false,
        width: 83,
        level: 1,
        options: [
          {
            value: 'option1',
            title: '选项一',
            lang: {
              title: {
                zh_CN: '选项一',
                zh_TW: '選項一',
                en_US: 'option1',
              },
            },
          },
          {
            value: 'option2',
            title: '选项二',
            lang: {
              title: {
                zh_CN: '选项二',
                zh_TW: '選項二',
                en_US: 'option2',
              },
            },
          },
          {
            value: 'option3',
            title: '选项三',
            lang: {
              title: {
                zh_CN: '选项三',
                zh_TW: '選項三',
                en_US: 'option3',
              },
            },
          },
        ],
        dictionaryId: null,
        dictionaryKey: '',
        enumKey: '',
        connectType: 'dataConnector',
        dataConnectorId: null,
      };
    case ATHENA_TYPES.TEXTAREA:
      return {
        ...combinDslStandardComponent(field, type, serviceAttr),
        rowGroupable: false,
        width: 83,
        level: 1,
      };
    case ATHENA_TYPES.ADDRESS:
      const address = {
        ...combinDslStandardComponent(field, type, serviceAttr),
        rowGroupable: false,
        width: 83,
        level: 1,
      };
      Reflect.deleteProperty(address.lang, 'placeholder');
      Reflect.deleteProperty(address, 'placeholder');
      return address;
    case ATHENA_TYPES.PLAN_SELECT:
      return {
        ...combinDslStandardComponent(field, type, serviceAttr),
        rowGroupable: false,
        width: 83,
        level: 1,
        options: [
          {
            value: 'option1',
            title: '选项一',
            lang: {
              title: {
                zh_CN: '选项一',
                zh_TW: '選項一',
                en_US: 'option1',
              },
            },
            approve: false,
          },
        ],
        dictionaryId: null,
        dictionaryKey: '',
        enumKey: '',
        connectType: 'dataConnector',
        dataConnectorId: null,
      };
    case ATHENA_TYPES.BUTTON:
      const buttonDsl = {
        ...combinDslStandardComponent(field, type, serviceAttr),
        rowGroupable: false,
        width: 83,
        level: 1,
        athType: 'default',
        sizeType: 'large',
        debounce: true,
        debounceTime: 300,
        beforeIcon: '',
        afterIcon: '',
        align: 'center',
        readonly: false,
      };
      const buttonTitle = buttonDsl.headerName;
      const buttonLang = {
        ...buttonDsl.lang,
        title: buttonDsl.lang.headerName,
      };
      Reflect.deleteProperty(buttonDsl, 'headerName');
      Reflect.deleteProperty(buttonLang, 'headerName');
      Reflect.deleteProperty(buttonDsl, 'placeholder');
      Reflect.deleteProperty(buttonLang, 'placeholder');
      Reflect.deleteProperty(buttonDsl, 'isFocusDisplay');
      return {
        ...buttonDsl,
        title: buttonTitle,
        lang: buttonLang,
      };
    case ATHENA_TYPES.PERSON_SELECT:
      const personSelectDsl = {
        ...combinDslStandardComponent(field, type, serviceAttr),
        dataType: AthenaDataType.ARRAY,
        width: 83,
        level: 1,
        enableSearch: true,
      };
      Reflect.deleteProperty(personSelectDsl, 'placeholder');
      Reflect.deleteProperty(personSelectDsl.lang, 'placeholder');
      Reflect.deleteProperty(personSelectDsl, 'isFocusDisplay');
      Reflect.deleteProperty(personSelectDsl, 'sortable');
      Reflect.deleteProperty(personSelectDsl, 'filterable');
      return personSelectDsl;
    case ATHENA_TYPES.SELECT:
      return {
        ...combinDslStandardComponent(field, type, serviceAttr),
        rowGroupable: false,
        width: 83,
        level: 1,
        options: [
          {
            value: 'option1',
            title: '选项一',
            lang: {
              title: {
                zh_CN: '选项一',
                zh_TW: '選項一',
                en_US: 'option1',
              },
            },
          },
        ],
        dictionaryId: null,
        dictionaryKey: '',
        enumKey: '',
        connectType: 'dataConnector',
        dataConnectorId: null,
      };
    case ATHENA_TYPES.CHECKBOX:
      return {
        ...combinDslStandardComponent(field, type, serviceAttr),
        dataType: AthenaDataType.BOOLEAN,
        rowGroupable: false,
        width: 83,
        level: 1,
        options: [],
      };
    case ATHENA_TYPES.RADIO_GROUP:
      return {
        ...combinDslStandardComponent(field, type, serviceAttr),
        rowGroupable: false,
        width: 83,
        level: 1,
        options: [
          {
            value: 'option1',
            title: '选项一',
            lang: {
              title: {
                zh_CN: '选项一',
                zh_TW: '選項一',
                en_US: 'option1',
              },
            },
          },
          {
            value: 'option2',
            title: '选项二',
            lang: {
              title: {
                zh_CN: '选项二',
                zh_TW: '選項二',
                en_US: 'option2',
              },
            },
          },
          {
            value: 'option3',
            title: '选项三',
            lang: {
              title: {
                zh_CN: '选项三',
                zh_TW: '選項三',
                en_US: 'option3',
              },
            },
          },
        ],
        connectType: 'dataConnector',
        dataConnectorId: null,
      };
    default:
      break;
  }
  return;
}
