import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import './index.less';
import NoData from '@/components/NoData';
import { t } from 'i18next';
import { useDataSources } from '../../store/dataSources';
import Icon from '@/components/Icon';
import { Button, Checkbox, Input, Modal, Pagination, Radio, Select, Spin } from 'antd';
import { useWorkData } from '../../store/workData';
import AppSelect from '@components/AppSelect';
import { ATHENA_TYPES } from '../../utils/fields.utils';
import { combinDslColumnDef, combinDslComponent, combinLayout, componentList } from './config';
import { cloneDeep } from 'lodash';
import { moveItemInArray } from '../../utils/common.utils';
import OpenwindowDesignComponents from './components/OpenwindowDesignComponents';
import OpenwindowSetting from './components/OpenwindowSetting';
import OpenwindowComponentSetting from './components/OpenwindowComponentSetting';
import TableSetting from './components/TableSetting';
import OpenwindowColumnSetting from './components/OpenwindowColumnSetting';
const { Search } = Input;

interface OpenwindowDesignProps {
  designLoading: boolean;
  applicationCode: string;
  isPrivatization: boolean;
  setSource: () => void;
}

const OpenwindowDesign = forwardRef<any>((props: OpenwindowDesignProps, ref) => {
  useImperativeHandle(ref, () => {
    return {
      setOpenwindowSelectedInfo: () => {
        handleSetSelectedInfo(undefined, undefined, 'openwindow', activeData());
      },
      handleValidSetting: async () => {
        let valid = true;
        if (opwSettingRef?.current) {
          if (!(await opwSettingRef.current?.handleValidate())) {
            valid = false;
          }
        }
        return valid;
      },
    };
  }, []);
  const { dataSources, dataSourceNames = [], updateDataSources } = useDataSources((state) => state);
  const { openWindowDefine, updateOpenWindowDefine, columnType, activeData, updateActiveData } =
    useWorkData((state) => state);

  const [showAttrPanel, setShowAttrPanel] = useState(false); // 属性面板

  // 拖拽参数
  const [dragType, setDragType] = useState<string>(null);
  const [dragData, setDragData] = useState<any>(null);
  const [dragIndex, setDragIndex] = useState<number>(null);
  const [dropType, setDropType] = useState<string>(null); // 拖拽字段到 table、td、tr、component
  const [dropField, setDropField] = useState<any>(null); // 拖拽字段
  const [dropColumnDefIndex, setDropColumnDefIndex] = useState<number>(null);
  const [dropColumnsIndex, setDropColumnsIndex] = useState<number>(null);

  // 选中参数
  const [setType, setSetType] = useState<string>(null); // 设置的属性面板类型: openwindow、table、column、component
  const [setData, setSetData] = useState<any>(null);
  const [setIndex, setSetIndex] = useState<number>(null);
  const [setComIndex, setSetComIndex] = useState<number>(null);

  // 选择组件开窗
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [componentType, setComponentType] = useState<string>('INPUT');

  const opwSettingRef = useRef(null);

  const defaultOptions = componentList.map((component) => {
    return {
      label: t(`${component.type}`),
      value: component.type,
    };
  });

  useEffect(() => {
    if (setType === 'openwindow') {
      // 数据源变更，更新开窗标题
      setSetData(activeData());
    }
  }, [activeData(), openWindowDefine()]);

  /**
   * 移除选中样式
   */
  const handleRemoveSelectedClass = () => {
    Array.from(document.getElementsByClassName('drag-component-selected'))?.forEach((item) => {
      item?.classList?.remove('drag-component-selected');
    });
  };

  /**
   * 默认选中开窗
   * @param e
   */
  const handleClickDraw = (e) => {
    e?.stopPropagation();
    handleRemoveSelectedClass();
    const layout = openWindowDefine();
    if (!layout) {
      return;
    }
    const container = document.getElementsByClassName('draging-container')?.[0];
    container && container?.classList?.add('drag-component-selected');
    handleSetSelectedInfo(undefined, undefined, 'openwindow', activeData);
  };

  /**
   * 选中表格
   * @param e
   */
  const handleClickTable = (e) => {
    e.stopPropagation();
    handleRemoveSelectedClass();
    const element = e.target.closest('.drag-table-container');
    element?.classList?.add('drag-component-selected');
    const newOpenWindowDefine = cloneDeep(openWindowDefine());
    if (!newOpenWindowDefine?.layout) {
      newOpenWindowDefine['layout'] = combinLayout([]);
    }
    handleSetSelectedInfo(undefined, undefined, 'table', newOpenWindowDefine?.layout?.[0]);
  };

  /**
   * 选中单元格
   * @param e
   * @param i
   */
  const handleClickTd = (e, index) => {
    e.stopPropagation();
    handleRemoveSelectedClass();
    const element = e.target.closest('.drag-td');
    element?.classList?.add('drag-component-selected');
    handleSetSelectedInfo(
      index,
      undefined,
      'column',
      openWindowDefine()?.layout?.[0]?.columnDefs?.[index]
    );
  };

  /**
   * 选中单元格中的组件
   * @param e
   * @param i
   * @param j
   */
  const handleClickComponent = (e, colIndex, comIndex) => {
    e.stopPropagation();
    handleRemoveSelectedClass();
    const element = e.target.closest('.drag-component');
    element?.classList?.add('drag-component-selected');
    handleSetSelectedInfo(
      colIndex,
      comIndex,
      'component',
      openWindowDefine()?.layout?.[0]?.columnDefs?.[colIndex]?.columns?.[comIndex]
    );
  };

  /**
   * 删除单元格 默认选中开窗
   * @param index
   */
  const handleRemoveColumn = (index: number): void => {
    const neOpenWindowDefine = openWindowDefine();
    neOpenWindowDefine.layout[0].columnDefs.splice(index, 1);
    updateOpenWindowDefine(neOpenWindowDefine);
    handleSetSelectedInfo(undefined, undefined, 'openwindow', activeData);
  };

  /**
   * 删除单元格中的组建，默认选中开窗
   * @param colIndex
   * @param comIndex
   */
  const handleRemoveComponent = (colIndex: number, comIndex: number): void => {
    const neOpenWindowDefine = openWindowDefine();
    neOpenWindowDefine.layout[0].columnDefs[colIndex].columns.splice(comIndex, 1);
    updateOpenWindowDefine(neOpenWindowDefine);
    handleSetSelectedInfo(undefined, undefined, 'openwindow', activeData);
  };

  /**
   * 设置拖进时element的样式
   * @param e
   * @returns
   */
  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.target.closest('.td-check')) return;
    let dom;
    // component
    dom = e.target.closest('.drag-component');
    if (dom) {
      if (dragType === 'column') return;
      if (dragData?.id === dom?.dataset?.id) return;

      const rect = dom.getBoundingClientRect();
      const center = rect.top + rect.height / 2;
      const y = e.clientY;
      if (y < center) {
        // 组建上面
        dom?.classList?.remove('drag-over-down');
        dom?.classList?.add('drag-over-up');
      } else {
        // 组建下面
        dom?.classList?.remove('drag-over-up');
        dom?.classList?.add('drag-over-down');
      }
    }
    // tr
    dom = e.target.closest('.drag-tr');
    if (dom) {
      if (dragType === 'column') return;
      dom?.classList?.add('drag-over-container');
      return;
    }
    // td
    dom = e.target.closest('.drag-td');
    if (dom) {
      if (dragType === 'column' && dragData?.id === dom?.dataset?.id) return;

      const rect = dom.getBoundingClientRect();
      const center = rect.left + rect.width / 2;
      const x = e.clientX;
      if (x < center) {
        // td左边
        dom?.classList?.remove('drag-over-right');
        dom?.classList?.add('drag-over-left');
      } else {
        // td右边
        dom?.classList?.remove('drag-over-left');
        dom?.classList?.add('drag-over-right');
      }
      return;
    }
    // table
    if (e.target?.classList?.contains('drag-table')) {
      e.target?.classList?.add('drag-over-container');
    }
  };

  /**
   * 获取离开时的element并移除样式
   * @param e
   */
  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    const element =
      e.target.closest('.drag-component') ||
      e.target.closest('.drag-tr') ||
      e.target.closest('.drag-td') ||
      e.target.closest('.drag-table');
    handleRemoveClass(element);
  };

  /**
   * 移除element的拖拽样式
   * @param element
   */
  const handleRemoveClass = (element) => {
    if (element) {
      element?.classList?.remove('drag-over-container');
      element?.classList?.remove('drag-over-left');
      element?.classList?.remove('drag-over-right');
      element?.classList?.remove('drag-over-up');
      element?.classList?.remove('drag-over-down');
    }
  };

  /**
   * td 开始拖动
   * @param e
   * @param type
   * @param col
   * @param index
   */
  const handleDragStart = (e, type, col, index) => {
    console.log('5');
    e.stopPropagation();
    e.dataTransfer.setData('text/plain', JSON.stringify({ [type]: col }));
    setDragType(type);
    setDragData(col);
    setDragIndex(index);
    e.target?.classList?.add('draging-com');
  };

  /**
   * td 结束拖动
   * @param e
   */
  const handleDragEnd = (e) => {
    console.log('6');
    e.stopPropagation();
    e.target?.classList?.remove('draging-com');
  };

  /**
   * 确认选择退拽后的控件
   */
  const handleOk = () => {
    setIsModalOpen(false);
    const newOpenWindowDefine = cloneDeep(openWindowDefine());
    if (!newOpenWindowDefine?.layout) {
      newOpenWindowDefine['layout'] = combinLayout([]);
    }
    if (['table', 'td'].includes(dropType)) {
      const newCol = combinDslColumnDef(
        combinDslComponent(dropField, componentType, {
          application: props.applicationCode,
          language: t('dj-LANG'),
        })
      );
      if (dropType === 'table') {
        newOpenWindowDefine.layout[0].columnDefs.push(newCol);
      } else {
        newOpenWindowDefine.layout[0].columnDefs.splice(dropColumnDefIndex, 0, newCol);
      }
      updateOpenWindowDefine(newOpenWindowDefine);
      handleSetSelectedInfo(
        dropType === 'table'
          ? newOpenWindowDefine.layout[0].columnDefs.length - 1
          : dropColumnDefIndex,
        undefined,
        'column',
        newCol
      );
    }
    if (['tr', 'component'].includes(dropType)) {
      if (!newOpenWindowDefine.layout[0].columnDefs[dropColumnDefIndex]) {
        newOpenWindowDefine.layout[0].columnDefs[dropColumnDefIndex] = [];
      }
      const newCom = combinDslComponent(dropField, componentType, {
        application: props.applicationCode,
        language: t('dj-LANG'),
      });
      if (dropType === 'tr') {
        newOpenWindowDefine.layout[0].columnDefs[dropColumnDefIndex].columns.push(newCom);
      } else {
        newOpenWindowDefine.layout[0].columnDefs[dropColumnDefIndex].columns.splice(
          dropColumnsIndex,
          0,
          newCom
        );
      }
      updateOpenWindowDefine(newOpenWindowDefine);
      handleSetSelectedInfo(
        dropColumnDefIndex,
        dropType === 'tr'
          ? newOpenWindowDefine.layout[0].columnDefs[dropColumnDefIndex].columns.length - 1
          : dropColumnsIndex,
        'component',
        newCom
      );
    }
  };

  /**
   * 设置选中参数
   * @param index
   * @param comIndex
   * @param type
   * @param data
   */
  const handleSetSelectedInfo = (index: number, comIndex: number, type: string, data: any) => {
    setSetIndex(index);
    setSetComIndex(comIndex);
    setSetType(type);
    setSetData(data);
    setShowAttrPanel(true);
  };

  /**
   * 关闭开窗
   */
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  /**
   * 选择控件
   * @param value
   */
  const onChange = (value: string) => {
    setComponentType(value);
  };

  /**
   * 拖放到table内
   * @param e
   */
  const handleDrop = (e): void => {
    console.log('1');
    e.preventDefault();
    e.stopPropagation();
    const data = JSON.parse(e.dataTransfer.getData('text/plain') || '{}');
    const { key, field, column } = data;
    if (key === 'setting') {
      handleRemoveClass(e.target.closest('.drag-table'));
      return;
    }
    console.log({ field, column });
    if (field) {
      setComponentType('INPUT');
      setDropType('table');
      setDropField(field);
      setIsModalOpen(true);
    } else if (column) {
      const newOpenWindowDefine = cloneDeep(openWindowDefine());
      moveItemInArray(
        newOpenWindowDefine.layout[0].columnDefs,
        dragIndex,
        newOpenWindowDefine.layout[0].columnDefs.length - 1
      );
      updateOpenWindowDefine(newOpenWindowDefine);
    }
    handleRemoveClass(e.target.closest('.drag-table'));
  };

  /**
   * 拖放到td内
   * @param e
   * @param index
   */
  const handleTDDrop = (e, index): void => {
    console.log('2');
    e.preventDefault();
    e.stopPropagation();
    const data = JSON.parse(e.dataTransfer.getData('text/plain') || '{}');
    const element = e.target.closest('.drag-td');
    const { key, field, column } = data;
    if (key === 'setting') {
      handleRemoveClass(element);
      return;
    }
    console.log({ field, column });
    const targetIndex = element?.classList?.contains('drag-over-right') ? index + 1 : index;
    if (field) {
      setComponentType('INPUT');
      setDropType('td');
      setDropField(field);
      setDropColumnDefIndex(targetIndex);
      setIsModalOpen(true);
    } else {
      const newOpenWindowDefine = cloneDeep(openWindowDefine());
      moveItemInArray(newOpenWindowDefine.layout[0].columnDefs, dragIndex, index);
      updateOpenWindowDefine(newOpenWindowDefine);
    }
    handleRemoveClass(element);
  };

  /**
   * 拖放到tr内
   * @param e
   * @param index
   */
  const handleTRDrop = (e, index): void => {
    console.log('3');
    e.preventDefault();
    e.stopPropagation();
    const data = JSON.parse(e.dataTransfer.getData('text/plain') || '{}');
    const { key, field } = data;
    const element = e.target.closest('.drag-tr');
    if (key === 'setting') {
      handleRemoveClass(element);
      return;
    }
    console.log({ field });
    if (field) {
      setComponentType('INPUT');
      setDropType('tr');
      setDropField(field);
      setDropColumnDefIndex(index);
      setIsModalOpen(true);
    }
    handleRemoveClass(element);
  };

  /**
   * 拖放到component内
   * @param e
   * @param columnIndex
   * @param index
   */
  const handleComponentDrop = (e, columnIndex, index): void => {
    console.log('4');
    e.preventDefault();
    e.stopPropagation();
    const data = JSON.parse(e.dataTransfer.getData('text/plain') || '{}');
    const { key, field } = data;
    const element = e.target.closest('.drag-component');
    if (key === 'setting') {
      handleRemoveClass(element);
      return;
    }
    console.log({ field });
    let targetIndex = element?.classList?.contains('drag-over-down') ? index + 1 : index;
    if (field) {
      setComponentType('INPUT');
      setDropType('component');
      setDropField(field);
      setDropColumnDefIndex(columnIndex);
      setDropColumnsIndex(targetIndex);
      setIsModalOpen(true);
    }
    handleRemoveClass(element);
  };

  /**
   * 设置开窗配置
   * @param e
   */
  const handleSetting = (e) => {
    if (e.isControl) {
      updateActiveData(e.value);
      setSetData(e.value);
    }
  };

  /**
   * 设置表格配置
   * @param e
   */
  const handleSettingTable = (e) => {
    if (e.isControl) {
      const newOpenWindowDefine = cloneDeep(openWindowDefine());

      newOpenWindowDefine.layout[0] = e.value;
      updateOpenWindowDefine(newOpenWindowDefine);
      setSetData(e.value);
    }
  };

  /**
   * 设置单元格配置
   * @param e
   */
  const handleSettingColumn = (e) => {
    if (e.isControl) {
      const newOpenWindowDefine = cloneDeep(openWindowDefine());
      newOpenWindowDefine.layout[0].columnDefs[setIndex] = e.value;
      updateOpenWindowDefine(newOpenWindowDefine);
      setSetData(e.value);
    }
  };

  /**
   * 设置单元格中的组件配置
   * @param e
   */
  const handleSettingComponent = (e) => {
    if (e.isControl) {
      const newOpenWindowDefine = cloneDeep(openWindowDefine());
      newOpenWindowDefine.layout[0].columnDefs[setIndex].columns[setComIndex] = e.value;
      updateOpenWindowDefine(newOpenWindowDefine);
      setSetData(e.value);
    }
  };

  return (
    <div className="openwindow-area">
      {/* 暂无数据源 */}
      {dataSourceNames.length === 0 && (
        <NoData className="no-dataSource">
          <div>
            <span>{t('dj-没有设定数据源，')}</span>
            <span className="set-source" onClick={() => props.setSource()}>
              {t('dj-前往设定')}
            </span>
          </div>
        </NoData>
      )}
      {/* 有数据源 */}
      {dataSourceNames.length > 0 && (
        <div className="draging-center">
          <div
            className={`draging-container ${setType === 'openwindow' ? 'drag-component-selected' : ''}`}
            onClick={(e) => handleClickDraw(e)}
          >
            <Spin spinning={props.designLoading}>
              <div className="component-operates" style={{ top: '-42px', right: '-18px' }}>
                <div className="title">{t('dj-开窗')}</div>
              </div>
              <div className="drag-header">
                {openWindowDefine()?.lang?.title?.[t('dj-LANG')]}
                <Icon type="icondanchuxiaoxiguanbi" className="closeIcon"></Icon>
              </div>
              <div className="drag-table-search">
                <AppSelect
                  disabled
                  animateLabel
                  placeholder={t('dj-栏位')}
                  value={'全部'}
                  options={[
                    {
                      value: '全部',
                      label: t('dj-全部'),
                    },
                  ]}
                />
                <AppSelect
                  disabled
                  animateLabel
                  placeholder={t('dj-公式')}
                  value={'包含'}
                  options={[
                    {
                      value: '包含',
                      label: t('dj-包含'),
                    },
                  ]}
                />
                <Search disabled placeholder={t('dj-搜索')} style={{ width: 200 }} />
                <Icon type="icondelete2" className="iconfont delete"></Icon>
                <Button style={{ width: '76px', backgroundColor: 'rgb(250, 250, 250)' }} disabled>
                  {t('dj-重置')}
                </Button>
                <Button
                  style={{ width: '76px', backgroundColor: 'rgb(204, 204, 204)' }}
                  type="primary"
                  disabled
                >
                  {t('dj-搜索')}
                </Button>
              </div>
              <div
                className={`drag-table-container ${setType === 'table' ? 'drag-component-selected' : ''}`}
                onClick={(e) => handleClickTable(e)}
              >
                <div className="component-operates">
                  <div className="title">{t('dj-表格')}</div>
                </div>
                <div
                  className="drag-table"
                  onDragOver={(e) => handleDragEnter(e)}
                  onDragLeave={(e) => handleDragLeave(e)}
                  onDrop={(e) => handleDrop(e)}
                >
                  <div className="td-check">
                    <div className="drag-th">
                      {columnType() !== 'OPERATION_EDITOR' && <Checkbox></Checkbox>}
                    </div>
                    <div className="drag-tr">
                      {columnType() !== 'OPERATION_EDITOR' && <Checkbox></Checkbox>}
                      {columnType() === 'OPERATION_EDITOR' && <Radio></Radio>}
                    </div>
                  </div>
                  {(openWindowDefine().layout?.[0]?.columnDefs || []).map((col, i) => {
                    return (
                      <div
                        key={col.id}
                        className={`drag-td ${setType === 'column' && setIndex === i && setData.id === col.id ? 'drag-component-selected' : ''}`}
                        data-id={col.id}
                        onClick={(e) => handleClickTd(e, i)}
                        style={{ minWidth: `${col.width || 160}px` }}
                        onDrop={(e) => handleTDDrop(e, i)}
                        draggable={true}
                        onDragStart={(e) => handleDragStart(e, 'column', col, i)}
                        onDragEnd={(e) => handleDragEnd(e)}
                      >
                        <div className="component-operates">
                          <div className="title">{col.lang?.headerName?.[t('dj-LANG')]}</div>
                          <Icon
                            type="icontuozhuaiziduan"
                            className="icon operation operation-drag"
                          />
                          <Icon
                            type="iconyichuziduan"
                            className="icon operation operation-remove"
                            onClick={() => handleRemoveColumn(i)}
                          />
                        </div>
                        <div className="drag-th">{col.lang?.headerName?.[t('dj-LANG')]}</div>
                        <div className="drag-tr" onDrop={(e) => handleTRDrop(e, i)}>
                          {(col.columns || []).map((com, j) => {
                            return (
                              <div
                                key={com.id}
                                className={`drag-component ${setType === 'component' && setIndex === i && setComIndex === j && setData.id === com.id ? 'drag-component-selected' : ''}`}
                                data-id={com.id}
                                onClick={(e) => handleClickComponent(e, i, j)}
                                onDrop={(e) => handleComponentDrop(e, i, j)}
                                draggable={true}
                                onDragStart={(e) => handleDragStart(e, 'component', com, i)}
                                onDragEnd={(e) => handleDragEnd(e)}
                              >
                                <div className="component-operates">
                                  <Icon
                                    type="icontuozhuaiziduan"
                                    className="icon operation operation-drag"
                                  />
                                  <Icon
                                    type="iconyichuziduan"
                                    className="icon operation operation-remove"
                                    onClick={() => handleRemoveComponent(i, j)}
                                  />
                                </div>
                                <OpenwindowDesignComponents
                                  component={com}
                                ></OpenwindowDesignComponents>
                                {/* <div>{com.schema}</div> */}
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    );
                  })}
                </div>
                {openWindowDefine()?.useHasNext && (
                  <Pagination
                    className="drag-table-pagination"
                    style={{ marginTop: '8px' }}
                    defaultCurrent={1}
                    total={50}
                    // disabled
                  />
                )}
              </div>
              <div className="drag-table-footer">
                <Button
                  style={{
                    width: '76px',
                    marginRight: '16px',
                    backgroundColor: 'rgb(250, 250, 250)',
                  }}
                  disabled
                >
                  {t('dj-取消')}
                </Button>
                <Button
                  style={{ width: '76px', backgroundColor: 'hsl(0, 0.00%, 80.00%)' }}
                  type="primary"
                  disabled
                >
                  {t('dj-确定')}
                </Button>
              </div>
            </Spin>
          </div>
        </div>
      )}
      {dataSourceNames.length > 0 && showAttrPanel && (
        <div className="attr-panel">
          {setType === 'openwindow' && (
            <OpenwindowSetting
              ref={opwSettingRef}
              applicationCode={props.applicationCode}
              data={setData}
              columnType={columnType()}
              change={(e) => handleSetting(e)}
            ></OpenwindowSetting>
          )}
          {setType === 'table' && (
            <TableSetting
              data={setData}
              columnType={columnType()}
              change={(e) => handleSettingTable(e)}
            ></TableSetting>
          )}
          {setType === 'column' && (
            <OpenwindowColumnSetting
              data={setData}
              columnType={columnType()}
              change={(e) => handleSettingColumn(e)}
            ></OpenwindowColumnSetting>
          )}
          {setType === 'component' && (
            <OpenwindowComponentSetting
              applicationCode={props.applicationCode}
              data={setData}
              columnType={columnType()}
              isPrivatization={props.isPrivatization}
              change={(e) => handleSettingComponent(e)}
            ></OpenwindowComponentSetting>
          )}
        </div>
      )}
      {/* 控制属性面板显隐 */}
      <div className={`toggle-attr ${!showAttrPanel ? 'hide-attr-panel' : ''}`}>
        {showAttrPanel && (
          <Icon
            type="icondoubleRight-copy"
            className="iconfont"
            onClick={() => {
              setShowAttrPanel(false);
            }}
          />
        )}
        {!showAttrPanel && (
          <Icon
            type="icondoubleLeft-copy"
            className="iconfont"
            onClick={() => {
              setShowAttrPanel(true);
            }}
          />
        )}
      </div>
      {isModalOpen && (
        <Modal
          title={t('选择组件')}
          open={isModalOpen}
          onOk={handleOk}
          onCancel={handleCancel}
          okText={t('dj-确定')}
          cancelText={t('dj-取消')}
        >
          <Select
            showSearch
            optionFilterProp="label"
            defaultValue={componentType}
            style={{ width: '100%' }}
            onChange={onChange}
            options={defaultOptions}
          />
        </Modal>
      )}
    </div>
  );
});

export default OpenwindowDesign;
