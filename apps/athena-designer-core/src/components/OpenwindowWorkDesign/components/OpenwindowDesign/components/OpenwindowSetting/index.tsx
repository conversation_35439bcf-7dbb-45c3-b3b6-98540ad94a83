import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import './index.less';
import { t } from 'i18next';
import {
  Button,
  Checkbox,
  Dropdown,
  Form,
  Input,
  MenuProps,
  Modal,
  Popconfirm,
  Select,
  Space,
  Tooltip,
  TreeSelect,
} from 'antd';
import AppLangInput from '@/components/AppLangInput';
import { useWorkData } from '@/components/OpenwindowWorkDesign/store/workData';
import { cloneDeep, isArray, isEmpty, isEqual } from 'lodash';
import { useDataSources } from '@/components/OpenwindowWorkDesign/store/dataSources';
import { SupportedEditorType } from '@/components/MonacoEditor/enum';
import { MonacoEditor } from '@components/MonacoEditor';
import Icon from '@/components/Icon';
import { DeleteOutlined, DragOutlined } from '@ant-design/icons';
import { isNone, transformTreeDatasToMap } from '@/components/DataSource/tools';
import { formatNodes } from '@/components/OpenwindowWorkDesign/utils/fields.utils';
import EditableSelect from './EditableSelect';
import _ from 'lodash';
import { ReportAndBasicDataInputModal } from '@/components/ReportAndBasicDataInputModal';
import { TreeSelectNotFound } from '@/components/DataSource/components/DataSourceBasic/TreeSelectNotFound';
import { TagNode } from '@/components/DataSource/components/DataSourceBasic/TagNode';
import { DataSourceTreeNode } from '@/components/DataSource/components/DataSourceBasic/DataSourceTreeNode';

const { Option } = Select;

interface OpenwindowSettingProps {
  applicationCode: string;
  data: any;
  columnType: string;
  change: (data: any) => void;
}

const OpenwindowSetting = forwardRef<any>((props: OpenwindowSettingProps, ref) => {
  useImperativeHandle(ref, () => {
    return {
      handleValidate: async () => {
        try {
          await openWindowForm.validateFields();
          return true;
        } catch (error) {
          return false;
        }
      },
    };
  }, []);
  console.log(props);
  // 创建持久化的初始化标记
  const isInitialized = useRef(false);
  const [openWindowForm] = Form.useForm(); // 开窗配置表单
  const [fieldForm] = Form.useForm(); // 回填字段表单
  const [targetFieldTree, setTargetFieldTree] = useState<any[]>([]); // 回填字段目标字段列表
  const [valueScriptList, setValueScriptList] = useState<any[]>([]); // 回填字段值列表
  const [fields, setFields] = useState<any[]>([]); // 回填字段是否回显
  const [fieldModalVisible, setFieldModalVisible] = useState<boolean>(false); // 回填字段开窗显隐
  const [currentField, setCurrentField] = useState<any>(null);
  const [currentFieldIndex, setCurrentFieldIndex] = useState<number>(-1);

  //   const [lang, setLang] = useState({}); // 多语言

  //   const [searchInfosTree, setSearchInfosTree] = useState<any[]>([]); // 高级搜索字段列表
  const [showHandleExtend, setShowHandleExtend] = useState<boolean>(false); // 高级搜索扩展开窗显隐
  // const [currentSearchInfos, setCurrentSearchInfos] = useState<string[]>([]); // 高级搜索扩展数据

  const { fieldTreeData, fieldTreeDataMap } = useWorkData((state) => state);

  const { openWindowDefine, updateOpenWindowDefine, columnType, activeData } = useWorkData(
    (state) => state
  );
  const { dataSources, dataSourceNames, fieldTreeMap, updateDataSources, updateFieldTreeMap } =
    useDataSources((state) => state);

  useEffect(() => {
    // if (!isInitialized.current) {
    //   // 初始化逻辑
    //   console.log('首次渲染初始化', JSON.parse(JSON.stringify(props.data)));
    //   isInitialized.current = true; // 标记已初始化
    handleInit();
    // } else {
    //   // 常规副作用逻辑
    //   console.log('常规副作用', JSON.parse(JSON.stringify(props.data)));
    //   handlePatchValues();
    // }
  }, [props.data]);

  /**
   * 初始化
   */
  const handleInit = () => {
    const {
      description,
      lang: {
        description: descriptionLang = {
          zh_CN: '',
          zh_TW: '',
          en_US: '',
        },
      },
      openWindowDefine,
      fields = [],
      condition,
      navigationConfig,
      applyAlthoughFinished,
    } = props.data ?? {};
    const {
      title,
      lang: {
        title: titleLang = {
          zh_CN: '',
          zh_TW: '',
          en_US: '',
        },
      },
      allAction,
      buttons,
      selectedFirstRow,
      useHasNext,
      enableInputSearch,
    } = openWindowDefine;
    // 回填字段（回显、非回显）
    setFields(fields);
    // 配置表单
    openWindowForm.setFieldsValue({
      title: titleLang,
      description: descriptionLang,
      type: props.columnType,
      condition,
      searchInfos: [
        ...((allAction?.searchInfos || []).map((item) => {
          return item.searchField;
        }) ?? []),
      ],
      backFills: [...(buttons?.[0]?.actions?.[0].backFills ?? [])],
      selectedFirstRow,
      useHasNext,
      enableInputSearch,
      applyAlthoughFinished,
      enable: navigationConfig?.enable ?? false,
      url: navigationConfig?.url ?? 'base-data-entry',
    });
    // 触发校验
    try {
      openWindowForm.validateFields();
    } catch (error) {}
    // 回填字段的目标字段
    if (fieldTreeDataMap?.size > 0) {
      const tree = [];
      for (const [key, value] of fieldTreeDataMap()) {
        const ii =
          formatNodes({
            arr: cloneDeep(value),
          }) || [];
        tree.push(...ii);
      }
      setTargetFieldTree(tree);
    } else {
      const newTargetFieldTree = formatNodes({
        arr: cloneDeep(fieldTreeData()),
      });
      setTargetFieldTree(newTargetFieldTree);
    }
  };

  const { searchInfosTree, searchInfosType } = useDataSources((state) => state);

  const cacheTreeDatasMap = useMemo(() => {
    const cacheMap = new Map<string, string>();
    transformTreeDatasToMap(cacheMap, searchInfosTree);
    return cacheMap;
  }, [searchInfosTree]);

  /**
   * 打开高级查询扩展
   */
  const handleExtendShow = () => {
    const searchInfos = openWindowForm.getFieldValue('searchInfos');
    console.log('searchInfos', searchInfos);
    setShowHandleExtend(true);
  };

  /**
   * 回填字段回显显示内容
   * @param field
   * @returns
   */
  const showFieldBack = (field, liInd): string => {
    const item = fields?.[liInd];
    return item.schema === field.key && item.show ? t('dj-回显字段') : t('dj-非回显字段');
  };

  /**
   * 打开回填字段开窗
   * @param index
   */
  const handleEditField = (index?: number) => {
    // 构建回填字段值列表
    setValueScriptList([]);
    const fieldTree = fieldTreeMap.get(dataSourceNames[0]);
    const newValueScriptList = [];
    (fieldTree?.[0]?.field || []).forEach((field) => {
      if (
        !newValueScriptList.some(
          (valueScript) => valueScript.code === `selectedObject['${field.data_name}']`
        )
      ) {
        newValueScriptList.push({
          code: `selectedObject['${field.data_name}']`,
          name: `selectedObject['${field.data_name}']`,
        });
      }
    });
    setValueScriptList(newValueScriptList);
    // 打开回填字段开窗
    let backFills = openWindowForm.getFieldValue('backFills');
    if (index > -1) {
      // 编辑
      setCurrentField(backFills[index]);
      setCurrentFieldIndex(index);
      fieldForm.setFieldsValue({
        ...backFills[index],
        checked: fields[index]?.show ?? false,
      });
    } else {
      // 新建
      setCurrentFieldIndex(-1);
      setCurrentField(null);
      fieldForm.setFieldsValue({
        key: null,
        valueScript: null,
        checked: false,
      });
    }
    setFieldModalVisible(true);
  };

  /**
   * 多语言输入框回调
   * @param key
   * @param data
   */
  const handlePatchLang = (key: any, data: any) => {
    openWindowForm.setFieldsValue({ [key]: data });
    onChange();
  };

  /**
   * 确定修改高级搜索
   * @param data
   */
  const handleConfirmExtend = (data: any) => {
    const currentSearchInfos = [
      ...((JSON.parse(data).searchInfos ?? []).map((item) => {
        return item.searchField;
      }) ?? []),
    ];
    openWindowForm.setFieldsValue({ searchInfos: currentSearchInfos });
    setShowHandleExtend(false);
    onChange();
  };

  /**
   * 输入框、选择框回调
   * @param key
   * @param data
   */
  const handleChange = (key: any, value: any) => {
    openWindowForm.setFieldsValue({ [key]: value });
    onChange();
  };

  const handleChangeEnable = (value: any) => {
    const newNavigationConfig = {
      enable: value,
      url: 'base-data-entry',
      urlParams: {},
    };
    openWindowForm.setFieldsValue({
      enable: value,
    });
    onChange({
      navigationConfig: newNavigationConfig,
    });
  };

  /**
   * 删除回填字段
   * @param index
   */
  const handleDeleteField = (index) => {
    const backFills = openWindowForm.getFieldValue('backFills');
    backFills.splice(index, 1);
    const newFields = cloneDeep(fields);
    newFields.splice(index, 1);
    setFields(newFields);
    openWindowForm.setFieldsValue({ backFills });
    onChange({ fields: newFields });
  };

  /**
   * 编辑新增回填字段
   */
  const handleChangeField = async () => {
    try {
      const data = await fieldForm.validateFields();
      const backFill = { ..._.omit(data, 'checked') };
      const field = {
        schema: data.key,
        show: !!data.checked,
      };
      const backFills = openWindowForm.getFieldValue('backFills');
      const newFields = cloneDeep(fields);

      if (currentField) {
        // 编辑
        backFills[currentFieldIndex] = backFill;
        newFields[currentFieldIndex] = field;
      } else {
        // 新增
        const match = backFill.key.match(/\.([^.]*)$/);
        if (match) {
          backFill['key'] = match[1];
          field['schema'] = match[1];
        }
        backFills.push(backFill);
        newFields.push(field);
      }
      setFields(newFields);
      openWindowForm.setFieldsValue({ backFills });
      setFieldModalVisible(false);
      onChange({ fields: newFields });
    } catch (error) {}
  };

  /**
   * 快捷添加入口
   * @param e
   */
  const handleChangeUrlParams = (e) => {
    const { code, category } = e;
    const { enable, url = 'base-data-entry' } = openWindowForm.getFieldsValue() ?? {};
    const newNavigationConfig = {
      enable,
      url,
      urlParams: {
        code,
        category,
      },
    };
    onChange({
      navigationConfig: newNavigationConfig,
    });
  };

  /**
   * 回填
   */
  const onChange = async (state?: any) => {
    // 开窗标题、开窗说明、条件、高级查询、回填字段、默认选择第一笔、开启分页
    const {
      title,
      description,
      condition,
      searchInfos,
      backFills,
      selectedFirstRow,
      useHasNext,
      enableInputSearch,
      applyAlthoughFinished,
      enable,
      url,
    } = openWindowForm.getFieldsValue() ?? {};
    console.log('回填', JSON.parse(JSON.stringify(openWindowForm.getFieldsValue() ?? {})));
    // 高级查询
    const allActionSearchInfos = (searchInfos ?? [])
      .filter((searchInfo) => searchInfosType.has(searchInfo))
      .map((data_name) => {
        return {
          searchField: data_name,
          dataType: searchInfosType.get(data_name),
        };
      });

    let data = {
      ...props.data,
      openWindowDefine: {
        ...(props.data?.openWindowDefine ?? {}),
        title: title?.[t('dj-LANG')],
        lang: {
          title,
        },
        selectedFirstRow: selectedFirstRow,
        useHasNext: useHasNext,
        enableInputSearch: enableInputSearch,
        allAction: {
          ...(props.data?.openWindowDefine?.allAction ?? {}),
          searchInfos: allActionSearchInfos,
        },
        buttons: [
          {
            id: 'confirm',
            title: '提交',
            lang: {
              title: {
                zh_TW: '提交',
                en_US: 'Submit',
                zh_CN: '提交',
              },
            },
            language: {
              title: {
                zh_TW: '提交',
                en_US: 'Submit',
                zh_CN: '提交',
              },
            },
            actions: [
              {
                backFills: backFills ?? [],
                type: 'UI',
              },
            ],
          },
        ],
      },
      condition,
      description: description?.[t('dj-LANG')],
      lang: {
        description,
      },
      fields: state?.fields ?? fields,
      navigationConfig: state?.navigationConfig ?? { enable, url, urlParams: {} },
      applyAlthoughFinished: applyAlthoughFinished,
    };
    props.change({
      isControl: true,
      value: data,
    });
  };

  const [draggableList, setDraggableList] = useState([]);
  const [draggedItem, setDraggedItem] = useState(null);

  const handleDragStart = (e, index) => {
    const items = openWindowForm.getFieldValue('backFills') ?? [];
    setDraggableList(items);
    e.dataTransfer.setData('text/plain', JSON.stringify({ key: 'setting', value: items[index] }));
    setDraggedItem(items[index]);
    e.dataTransfer.effectAllowed = 'move';
    e.target.classList.add('dragging');
  };

  const handleDragEnd = (e) => {
    e.target.classList.remove('dragging');
    setDraggedItem(null);
    setDraggableList([]);
    onChange();
  };

  const handleDragOver = (e, index) => {
    e.preventDefault();
    if (draggedItem === null) return;

    const draggedOverItem = draggableList[index];
    if (draggedItem === draggedOverItem) return;

    const newItems = [...draggableList];
    const draggedItemIndex = draggableList.indexOf(draggedItem);
    newItems.splice(draggedItemIndex, 1);
    newItems.splice(index, 0, draggedItem);
    setDraggableList(newItems);
    openWindowForm.setFieldsValue({ backFills: newItems });

    const newFields = cloneDeep(fields);
    const field = newFields[draggedItemIndex];
    newFields.splice(draggedItemIndex, 1);
    newFields.splice(index, 0, field);
    setFields(newFields);
  };

  return (
    <div className="openwindow-setting">
      <div className="breadcrumb-nav">
        <nav aria-label="breadcrumb">
          <ol
            className="breadcrumb"
            style={{ display: 'flex', flexWrap: 'nowrap', maxWidth: '240px' }}
          >
            <li className="breadcrumb-item breadcrumb-text-ellipsis" style={{ minWidth: '12px' }}>
              <a>
                <span className="bi bi-folder" style={{ marginRight: 0 }}></span>
              </a>
            </li>
            <li className="breadcrumb-item breadcrumb-text-ellipsis active">
              <a>{t('dj-开窗')}</a>
            </li>
          </ol>
        </nav>
      </div>
      {/* 字段属性 */}
      <div className="set-form-container">
        <Form
          form={openWindowForm}
          name="openwindow-setting-form"
          layout="vertical"
          labelCol={{
            span: 24,
          }}
          wrapperCol={{
            span: 24,
          }}
        >
          {/* 开窗标题 */}
          <Form.Item
            label={t('dj-开窗标题')}
            name="title"
            rules={[
              { required: true, message: t('dj-开窗标题必填') },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  // 自定义校验规则
                  if (value && value?.[t('dj-LANG')].trim() !== '') {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error(t('dj-开窗标题必填')));
                },
              }),
            ]}
          >
            <AppLangInput
              required
              size="small"
              onChange={(value) => handlePatchLang('title', value)}
            />
          </Form.Item>
          {/* 开窗说明 */}
          <Form.Item label={t('dj-开窗说明')} name="description">
            <AppLangInput
              required
              size="small"
              onChange={(value) => handlePatchLang('description', value)}
            />
          </Form.Item>
          {/* 开窗类型 */}
          <Form.Item label={t('dj-开窗类型')} name="type">
            <Select disabled>
              <Select.Option value="OPERATION_EDITOR">{t('OPERATION_EDITOR')}</Select.Option>
              <Select.Option value="FORM_OPERATION_EDITOR">
                {t('FORM_OPERATION_EDITOR')}
              </Select.Option>
            </Select>
          </Form.Item>
          {/* 启用条件 */}
          <Form.Item label={t('dj-启用条件')} name="condition">
            <Input onChange={(e) => handleChange('condition', e.target.value)} />
          </Form.Item>
          {/* 高级查询 */}
          <Form.Item label={t('dj-高级查询')} name="searchInfos">
            <>
              <TreeSelect
                notFoundContent={<TreeSelectNotFound />}
                popupClassName="tree-select mf-override-treeselect datakeys-treeselect"
                showSearch={true}
                placeholder={t('dj-请选择')}
                multiple={true}
                treeCheckable={true}
                treeData={searchInfosTree}
                treeDefaultExpandAll={true}
                allowClear
                maxTagCount={1}
                virtual={false}
                tagRender={({ value, onClose, isMaxTag, label, closable }) => {
                  const fullDesc = cacheTreeDatasMap.get(value);
                  return (
                    <TagNode
                      value={value}
                      isMaxTag={isMaxTag}
                      label={label}
                      closeable={closable}
                      fullDesc={fullDesc}
                      onClose={onClose}
                    />
                  );
                }}
                treeTitleRender={(data: any) => <DataSourceTreeNode {...data} />}
                value={openWindowForm.getFieldValue('searchInfos')}
                onChange={(e) => handleChange('searchInfos', e)}
              />
              {/* <TreeSelect
                treeData={searchInfosTree}
                value={openWindowForm.getFieldValue('searchInfos')}
                onChange={(e) => handleChange('searchInfos', e)}
                multiple
                style={{ width: '100%' }}
                treeCheckable
                placeholder={t('dj-请选择')}
                showCheckedStrategy={TreeSelect.SHOW_CHILD}
                maxTagCount={1}
              /> */}
              {searchInfosTree && (
                <span className="exten-info" onClick={() => handleExtendShow()}>
                  {t('dj-扩展')} &lt;/&gt;
                </span>
              )}
            </>
          </Form.Item>
          {/* 回填字段 */}
          <Form.Item
            label={
              <span>
                {t('dj-回填字段')}
                <Tooltip title={t('dj-回填暂不支持跨表格')}>
                  <Icon type="iconexplain" style={{ marginLeft: '3px', fontSize: '13px' }} />
                </Tooltip>
              </span>
            }
            name="backFills"
          >
            <ul>
              {(openWindowForm.getFieldValue('backFills') ?? []).map((item, liInd) => {
                return (
                  <li
                    className="condition-item"
                    key={liInd}
                    draggable
                    onDragStart={(e) => handleDragStart(e, liInd)}
                    onDragEnd={(e) => handleDragEnd(e)}
                    onDragOver={(e) => handleDragOver(e, liInd)}
                  >
                    <div className="title" title={item.key + ' = ' + item.valueScript}>
                      {item.key + ' = ' + item.valueScript}
                    </div>
                    <div className="actions">
                      {/* <Tooltip title={showFieldBack(item, liInd)}> */}
                      <span className="back-fields-info">{showFieldBack(item, liInd)}</span>
                      {/* </Tooltip> */}
                      <a>
                        <DragOutlined className="drag-handle" />
                      </a>
                      {/* <Tooltip title={t('dj-编辑')}> */}
                      <Icon type="iconbianjidianji" onClick={() => handleEditField(liInd)} />
                      {/* </Tooltip> */}
                      <Popconfirm
                        className="confirm-delete"
                        placement="top"
                        title={t('dj-确认删除吗？')}
                        description={null}
                        okText={t('dj-删除')}
                        cancelText={t('dj-取消')}
                        onConfirm={() => handleDeleteField(liInd)}
                      >
                        <DeleteOutlined />
                      </Popconfirm>
                    </div>
                  </li>
                );
              })}
              <li className="condition-btn" onClick={() => handleEditField()}>
                <Button className="add-btn" style={{ width: '100%' }}>
                  {t('dj-回填字段')}
                </Button>
              </li>
            </ul>
          </Form.Item>
          {/* 默认选择第一笔 */}
          <Form.Item name="selectedFirstRow" valuePropName="checked" label={null}>
            <Checkbox
              disabled={columnType() === 'FORM_OPERATION_EDITOR'}
              onChange={(e) => handleChange('selectedFirstRow', e.target.checked)}
            >
              {t('dj-默认选择第一笔')}
            </Checkbox>
          </Form.Item>
          {/* 开启分页 */}
          <Form.Item name="useHasNext" valuePropName="checked" label={null}>
            <Checkbox onChange={(e) => handleChange('useHasNext', e.target.checked)}>
              {t('dj-开启分页')}
            </Checkbox>
          </Form.Item>
          {/* 快捷搜索 */}
          <Form.Item name="enableInputSearch" valuePropName="checked" label={null}>
            <Checkbox onChange={(e) => handleChange('enableInputSearch', e.target.checked)}>
              {t('dj-快捷搜索')}
              <Tooltip title={t('dj-开窗外部文本框支持关键词搜索')}>
                <Icon type="iconexplain" style={{ marginLeft: '3px', fontSize: '13px' }} />
              </Tooltip>
            </Checkbox>
          </Form.Item>
          {/* 已完成页签是否显示 */}
          <Form.Item name="applyAlthoughFinished" valuePropName="checked" label={null}>
            <Checkbox onChange={(e) => handleChange('applyAlthoughFinished', e.target.checked)}>
              {t('dj-已完成页签是否显示')}
              <Tooltip title={'applyAlthoughFinished ' + t('dj-仅任务中可用')}>
                <Icon type="iconexplain" style={{ marginLeft: '3px', fontSize: '13px' }} />
              </Tooltip>
            </Checkbox>
          </Form.Item>
          {/* 快捷添加入口 */}
          <Form.Item name="enable" valuePropName="checked" label={null}>
            <Checkbox onChange={(e) => handleChangeEnable(e.target.checked)}>
              {t('dj-快捷添加入口')}
              <Tooltip title={t('dj-启用快捷添加入口，用户可在开窗列表快速跳转至基础资料录入作业')}>
                <Icon type="iconexplain" style={{ marginLeft: '3px', fontSize: '13px' }} />
              </Tooltip>
            </Checkbox>
          </Form.Item>
          {openWindowForm.getFieldValue('enable') && (
            <Form.Item label={null}>
              <ReportAndBasicDataInputModal
                appCode={props.applicationCode}
                pattern="DATA_ENTRY"
                hideLabel={true}
                value={props.data?.navigationConfig?.urlParams}
                onChange={(e) => handleChangeUrlParams(e)}
              ></ReportAndBasicDataInputModal>
            </Form.Item>
          )}
        </Form>
        {showHandleExtend && (
          <MonacoEditor
            title={t('dj-扩展')}
            type={SupportedEditorType.JSON}
            value={JSON.stringify(
              { searchInfos: openWindowDefine().allAction?.searchInfos ?? [] },
              null,
              2
            )}
            visible={showHandleExtend}
            onOk={(e) => handleConfirmExtend(e)}
            onCancel={() => {
              setShowHandleExtend(false);
            }}
            loading
          />
        )}
        {fieldModalVisible && (
          <Modal
            className="field-modal"
            title={currentField ? t('dj-编辑回填字段') : t('dj-新增回填字段')}
            open={fieldModalVisible}
            onOk={() => {
              handleChangeField();
            }}
            onCancel={() => {
              setFieldModalVisible(false);
            }}
            cancelText={t('dj-取消')}
            okText={t('dj-确定')}
          >
            <Form
              form={fieldForm}
              layout="horizontal"
              labelCol={{
                span: 6,
              }}
              wrapperCol={{
                span: 16,
              }}
            >
              {currentField ? (
                <Form.Item
                  label={t('dj-目标字段')}
                  name="key"
                  rules={[{ required: true, message: t('dj-必填！') }]}
                >
                  {/* <TreeSelect
                    treeData={targetFieldTree}
                    style={{ width: '100%' }}
                    placeholder={t('dj-请选择')}
                    showCheckedStrategy={TreeSelect.SHOW_CHILD}
                    allowClear
                  /> */}
                  <Input />
                </Form.Item>
              ) : (
                <Form.Item
                  label={t('dj-目标字段')}
                  name="key"
                  rules={[{ required: true, message: t('dj-必填！') }]}
                >
                  <TreeSelect
                    treeData={targetFieldTree}
                    style={{ width: '100%' }}
                    placeholder={t('dj-请选择')}
                    showCheckedStrategy={TreeSelect.SHOW_CHILD}
                    allowClear
                  />
                </Form.Item>
              )}

              <Form.Item
                label={t('dj-值')}
                name="valueScript"
                rules={[{ required: true, message: t('dj-必填！') }]}
              >
                <EditableSelect valueScriptList={valueScriptList} />
              </Form.Item>
              <Form.Item name="checked" valuePropName="checked" label={t('dj-回显字段')}>
                <Checkbox>{t('dj-是否可显示')}</Checkbox>
              </Form.Item>
            </Form>
          </Modal>
        )}
      </div>
    </div>
  );
});

export default OpenwindowSetting;
