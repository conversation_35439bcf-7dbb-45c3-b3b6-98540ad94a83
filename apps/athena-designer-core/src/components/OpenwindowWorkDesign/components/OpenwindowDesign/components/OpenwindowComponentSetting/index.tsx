import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import './index.less';
import { t } from 'i18next';
import { ATHENA_TYPES } from '@/components/OpenwindowWorkDesign/utils/fields.utils';
import BasicComponentSetting from './components/BasicComponentSetting';
import { MonacoEditor } from '@components/MonacoEditor';
import { SupportedEditorType } from '@/components/MonacoEditor/enum';
import { cloneDeep } from 'lodash';
import UploadComponentSetting from './components/UploadComponentSetting';
import MeasureComponentSetting from './components/MeasureComponentSetting';
import DifferenceComponentSetting from './components/DifferenceComponentSetting';
import LabelComponentSetting from './components/LabelComponentSetting';
import SelectComponentSetting from './components/SelectComponentSetting';
import AddressComponentSetting from './components/AddressComponentSetting';
import ButtonComponentSetting from './components/ButtonComponentSetting';
import PersonComponentSetting from './components/PersonComponentSetting';
import { Form, Select } from 'antd';
import { combinDslComponent, componentList } from '../../config';

interface OpenwindowComponentSettingProps {
  applicationCode: string;
  data: any;
  columnType: string;
  isPrivatization: boolean;
  change: (data: any) => void;
}

const OpenwindowComponentSetting = (props: OpenwindowComponentSettingProps) => {
  const [showHandleExtend, setShowHandleExtend] = useState<boolean>(false); // 扩展
  const [extendData, setExtendData] = useState<boolean>(false); // 扩展数据
  const [componentForm] = Form.useForm();

  const defaultOptions = componentList.map((component) => {
    return {
      label: t(`${component.type}`),
      value: component.type,
    };
  });

  useEffect(() => {
    const type =
      ATHENA_TYPES.INPUT === props.data.type && 'numeric' === props.data.dataType
        ? ATHENA_TYPES.INPUT_NUMBER
        : props.data.type;
    componentForm.setFieldsValue({
      type,
    });
  }, [props.data]);

  /**
   * 组件类型变更
   * @param e
   */
  const handleChangeComponentType = (e) => {
    componentForm.setFieldsValue({ type: e });
    const newData = combinDslComponent(
      {
        data_name: props.data.schema,
        fullPath: `${props.data.path}.${props.data.schema}`,
        description: props.data?.lang?.placeholder ?? {},
      },
      e,
      {
        application: props.applicationCode,
        language: t('dj-LANG'),
      }
    );
    console.log(newData);
    props.change({
      isControl: true,
      value: newData,
    });
  };

  const handleExtendShow = () => {
    setExtendData(cloneDeep(props.data));
    setShowHandleExtend(true);
  };

  const handleConfirmExtend = (e) => {
    setShowHandleExtend(false);
    props.change({
      isControl: true,
      value: JSON.parse(e),
    });
  };

  const handleChanege = (e) => {
    if (e.isControl) {
      props.change(e);
    }
  };

  return (
    <>
      <div className="openwindow-setting">
        <div className="breadcrumb-nav">
          <nav aria-label="breadcrumb">
            <ol
              className="breadcrumb"
              style={{ display: 'flex', flexWrap: 'nowrap', maxWidth: '240px' }}
            >
              <li className="breadcrumb-item breadcrumb-text-ellipsis" style={{ minWidth: '12px' }}>
                <a>
                  <span className="bi bi-folder" style={{ marginRight: 0 }}></span>
                </a>
              </li>
              <li className="breadcrumb-item breadcrumb-text-ellipsis active">
                <a>{props.data?.lang?.label?.[t('dj-LANG')]}</a>
              </li>
            </ol>
          </nav>
        </div>
        <div className="set-form-container">
          <Form
            className="form-info"
            form={componentForm}
            name="openwindow-setting-form"
            layout="vertical"
            labelCol={{
              span: 24,
            }}
            wrapperCol={{
              span: 24,
            }}
          >
            {/* 组件类型 */}
            <Form.Item label={t('dj-组件类型')} name="type">
              <Select onChange={(e) => handleChangeComponentType(e)} options={defaultOptions} />
            </Form.Item>
          </Form>
          {/* 文本输入、日期选择、时间选择、数字输入、百分比、文本域、复选框 */}
          {[
            ATHENA_TYPES.DATEPICKER,
            ATHENA_TYPES.TIMEPICKER,
            ATHENA_TYPES.INPUT,
            ATHENA_TYPES.PERCENT_INPUT,
            ATHENA_TYPES.TEXTAREA,
            ATHENA_TYPES.CHECKBOX,
          ].includes(props.data?.type) && (
            <BasicComponentSetting
              data={props.data}
              change={(e) => handleChanege(e)}
            ></BasicComponentSetting>
          )}
          {/* 表格附件 */}
          {[ATHENA_TYPES.FILE_UPLOAD].includes(props.data?.type) && (
            <UploadComponentSetting
              data={props.data}
              change={(e) => handleChanege(e)}
            ></UploadComponentSetting>
          )}
          {/* 计量组件、金额 */}
          {[ATHENA_TYPES.AMOUNT_INPUT, ATHENA_TYPES.MEASURE].includes(props.data?.type) && (
            <MeasureComponentSetting
              data={props.data}
              change={(e) => handleChanege(e)}
            ></MeasureComponentSetting>
          )}
          {/* 差异值 */}
          {[ATHENA_TYPES.DIFFERENCE_CALCULATION].includes(props.data?.type) && (
            <DifferenceComponentSetting
              data={props.data}
              change={(e) => handleChanege(e)}
            ></DifferenceComponentSetting>
          )}
          {/* 文本 */}
          {[ATHENA_TYPES.LABEL].includes(props.data?.type) && (
            <LabelComponentSetting
              data={props.data}
              change={(e) => handleChanege(e)}
            ></LabelComponentSetting>
          )}
          {/* 多选框、方案选择、选择框、单选框 */}
          {[
            ATHENA_TYPES.SELECT_MULTIPLE,
            ATHENA_TYPES.PLAN_SELECT,
            ATHENA_TYPES.SELECT,
            ATHENA_TYPES.RADIO_GROUP,
          ].includes(props.data?.type) && (
            <SelectComponentSetting
              data={props.data}
              isPrivatization={props.isPrivatization}
              change={(e) => handleChanege(e)}
            ></SelectComponentSetting>
          )}
          {/* 收获地址 */}
          {[ATHENA_TYPES.ADDRESS].includes(props.data?.type) && (
            <AddressComponentSetting
              data={props.data}
              change={(e) => handleChanege(e)}
            ></AddressComponentSetting>
          )}
          {/* 动态按钮 */}
          {[ATHENA_TYPES.BUTTON].includes(props.data?.type) && (
            <ButtonComponentSetting
              data={props.data}
              change={(e) => handleChanege(e)}
            ></ButtonComponentSetting>
          )}
          {/* 人员选择 */}
          {[ATHENA_TYPES.PERSON_SELECT].includes(props.data?.type) && (
            <PersonComponentSetting
              data={props.data}
              change={(e) => handleChanege(e)}
            ></PersonComponentSetting>
          )}
          <span className="exten-info" onClick={() => handleExtendShow()}>
            {t('dj-扩展')} &lt;/&gt;
          </span>
        </div>
      </div>
      {showHandleExtend && (
        <MonacoEditor
          title={t('dj-扩展')}
          type={SupportedEditorType.JSON}
          value={JSON.stringify(props.data, null, 2)}
          visible={showHandleExtend}
          onOk={(e) => handleConfirmExtend(e)}
          onCancel={() => {
            setShowHandleExtend(false);
          }}
          loading
        />
      )}
    </>
  );
};

export default OpenwindowComponentSetting;
