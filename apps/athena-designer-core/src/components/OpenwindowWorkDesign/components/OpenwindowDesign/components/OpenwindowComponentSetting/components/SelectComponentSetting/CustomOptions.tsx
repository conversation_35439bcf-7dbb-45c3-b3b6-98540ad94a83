import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import './CustomOptions.less';
import { cloneDeep, isEmpty } from 'lodash';
import { async } from '@antv/x6/lib/registry/marker/async';
import { getDictByEnumKey } from '@/components/OpenwindowWorkDesign/api/query';
import { DropDownVocabulary } from '@/components/DropDownVocabulary';
import { t } from 'i18next';
import { DeleteOutlined, DragOutlined } from '@ant-design/icons';
import { Button, Popconfirm, Tooltip } from 'antd';
import Icon from '@/components/Icon';
import { OptionModal } from './OptionModal';

interface CustomOptionsProps {
  options: any[];
  dictionaryId: number;
  dictionaryKey: string;
  enumKey: string;
  dataType: string;
  isPrivatization: boolean;
  change: (data: any) => void;
}

const CustomOptions = (props: CustomOptionsProps) => {
  // 选项编辑
  const [optionIndex, setOptionIndex] = useState<number>(-1);
  const [optionData, setOptionData] = useState<any>({});
  const [optionVisible, setOptionVisible] = useState<boolean>(false);

  useEffect(() => {
    // handleGetDictByEnumKey();
  }, []);

  // const handleGetDictByEnumKey = async () => {
  //   if (!isEmpty(props.enumKey)) {
  //     const { data } = await getDictByEnumKey({ enumKey: props.enumKey });
  //     if (data?.code === 0) {
  //       handleResetOptionList(data?.data ?? []);
  //     }
  //   }
  // };

  /**
   * 选择枚举key
   * @param data
   */
  const handleSelectEnumKey = (params): void => {
    const { options, dictionaryId, dictionaryKey, enumKey } = params;
    handleChange(options, dictionaryId, dictionaryKey, enumKey);
  };

  /**
   * 重置选项
   * @param value
   */
  const handleResetOptionList = (value: any[] = []): void => {
    const newOptions = value.map((o) => {
      return {
        value: o.code,
        title: o.value,
        lang: {
          title: o.lang?.value ?? {},
        },
      };
    });
    handleChange(newOptions, props.dictionaryId, props.dictionaryKey, props.enumKey);
  };

  /**
   * 编辑选项
   * @param index
   * @param option
   */
  const handleEdit = (index: number, option: any) => {
    setOptionIndex(index);
    setOptionData(option);
    setOptionVisible(true);
  };

  /**
   * 删除选项
   * @param index
   * @param option
   */
  const handleDelete = (index: number, option: any) => {
    const newOptions = cloneDeep(props.options);
    newOptions.splice(index, 1);
    handleChange(newOptions, null, '', '');
  };

  /**
   * 添加选项
   */
  const handleAdd = (): void => {
    setOptionIndex(-1);
    setOptionData({});
    setOptionVisible(true);
  };

  /**
   * 取消编辑选项
   */
  const handleOptionCancel = (): void => {
    setOptionIndex(-1);
    setOptionData({});
    setOptionVisible(false);
  };

  /**
   * 确认编辑选择
   */
  const handleOptionOk = (newData: any): void => {
    const newOptions = cloneDeep(props.options);
    if (optionIndex > -1) {
      newOptions[optionIndex] = newData;
    } else {
      newOptions.push(newData);
    }
    handleOptionCancel();
    handleChange(newOptions, null, '', '');
  };

  /**
   * 触发数据变更事件
   * @param options
   * @param dictionaryId
   * @param dictionaryKey
   * @param enumKey
   */
  const handleChange = (
    options: any,
    dictionaryId: number,
    dictionaryKey: string,
    enumKey: string
  ): void => {
    props.change({ options, dictionaryId, dictionaryKey, enumKey });
  };

  const [draggableList, setDraggableList] = useState([]);
  const [draggedItem, setDraggedItem] = useState(null);

  const handleDragStart = (e, index) => {
    const items = props.options ?? [];
    setDraggableList(items);
    e.dataTransfer.setData('text/plain', JSON.stringify({ key: 'setting', value: items[index] }));
    setDraggedItem(items[index]);
    e.dataTransfer.effectAllowed = 'move';
    e.target.classList.add('dragging');
  };

  const handleDragEnd = (e) => {
    e.target.classList.remove('dragging');
    console.log('onDragOver', JSON.parse(e.dataTransfer.getData('text/plain') || '{}'));

    handleChange(draggableList, null, '', '');
    setDraggedItem(null);
    setDraggableList([]);
  };

  const handleDragOver = (e, index) => {
    e.preventDefault();
    console.log('onDragEnd', JSON.parse(e.dataTransfer.getData('text/plain') || '{}'));
    if (draggedItem === null) return;

    const draggedOverItem = draggableList[index];
    if (draggedItem === draggedOverItem) return;

    const newItems = [...draggableList];
    const draggedItemIndex = draggableList.indexOf(draggedItem);
    newItems.splice(draggedItemIndex, 1);
    newItems.splice(index, 0, draggedItem);
    setDraggableList(newItems);
  };

  return (
    <>
      <div className="select-content">
        <div className="setter-title">
          <span>{t('dj-配置选项')}</span>
          <span onClick={() => handleAdd()}>+&nbsp;{t('dj-添加')}</span>
        </div>
        <div className="select-input">
          <DropDownVocabulary
            dictionaryId={props.dictionaryId}
            dictionaryKey={props.dictionaryKey}
            enumKey={props.enumKey}
            className="lcdp-dropdown-vocabulary-input"
            options={props.options}
            placeholder={t('dj-请选择')}
            isPrivatization={props.isPrivatization}
            onChange={(e) => handleSelectEnumKey(e)}
          ></DropDownVocabulary>
        </div>
      </div>
      <div className="drop-list">
        {(props.options ?? []).map((option, i) => {
          return (
            <div
              className="drop-box"
              draggable
              onDragStart={(e) => handleDragStart(e, i)}
              onDragEnd={(e) => handleDragEnd(e)}
              onDragOver={(e) => handleDragOver(e, i)}
            >
              <div className="back-fields-info">{option?.lang?.title?.[t('dj-LANG')]}</div>
              <div className="drop-operate">
                <Icon
                  className="iconfont"
                  type="iconbianji1"
                  onClick={() => handleEdit(i, option)}
                />
                <Popconfirm
                  className="confirm-delete"
                  placement="top"
                  title={t('dj-确认删除吗？')}
                  description={null}
                  okText={t('dj-删除')}
                  cancelText={t('dj-取消')}
                  onConfirm={() => handleDelete(i, option)}
                >
                  <DeleteOutlined />
                </Popconfirm>
                <a>
                  <Icon className="iconfont button-move" type="icontuozhuai1" />
                </a>
              </div>
            </div>
          );
        })}
      </div>
      {/* <div>
        <Button type="primary" className="operation-btn" onClick={() => handleAdd()}>
          {t('addItem')}
        </Button>
      </div> */}
      <OptionModal
        visible={optionVisible}
        data={optionData}
        index={optionIndex}
        onChange={handleOptionOk}
        onCancel={handleOptionCancel}
      />
    </>
  );
};

export default CustomOptions;
