import React, { useState, useMemo } from "react";
import { Popover, Input, Button } from "antd";
import VariableTree from "./VariableTree";
import type { VariableTreeNode } from "../DataVariables/types/index";
import "./VariablesPopover.less";
import { SearchOutlined } from '@ant-design/icons'
import { useDynamicWorkDesignStore } from "../DynamicWorkDesign/store"
import { groupVariablesByRule } from '../DataVariables/utils'
import { CheckOutlined } from '@ant-design/icons';
import { t } from 'i18next';


interface Props {
  open: boolean;
  onOk: (node: VariableTreeNode) => void;
  onClose: () => void;
  trigger?: React.ReactNode; // 触发 Popover 的元素
}

const VariablesPopover: React.FC<Props> = ({
  open,
  onOk,
  onClose,
  trigger
}) => {
  const [searchValue, setSearchValue] = useState("");
  const [selectedNode, setSelectedNode] = useState<VariableTreeNode | null>(null);
  const variables = useDynamicWorkDesignStore(
    state => state.dynamicWorkDesignRenderData?.pageUIElementContent?.variables
  );
  const treeData: VariableTreeNode[] = useMemo(
    () => groupVariablesByRule(variables)?.filter(item => item.children?.length) || [],
    [variables]
  );
  // 搜索过滤
  const filteredTreeData = useMemo(() => {
    if (!searchValue) return treeData;
    const filter = (nodes: VariableTreeNode[]): VariableTreeNode[] =>
      nodes
        .map(node => {
          if (node.name.includes(searchValue)) return node;
          if (node.children) {
            const children = filter(node.children);
            if (children.length) return { ...node, children };
          }
          return null;
        })
        .filter(Boolean) as VariableTreeNode[];
    return filter(treeData);

  }, [treeData, searchValue]);

  const renderTitle = (title: string) => {
    if (!searchValue) return title;
    const idx = title.indexOf(searchValue);
    if (idx === -1) return title;
    return (
      <>
        {title.slice(0, idx)}
        <span style={{ color: "#605CE5" }}>{searchValue}</span>
        {title.slice(idx + searchValue.length)}
      </>
    );
  };

  const processTreeData = (data: VariableTreeNode[], level = 0): any[] =>
     data.map(item => ({
      ...item,
      title: (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            fontSize: 14,
            color: selectedNode?.id === item.id ? "#605CE5" : "#1D1C33",
            width: '100%',
          }}
        >
          <span>{renderTitle(item.name)}</span>
          {selectedNode?.id === item.id && (
            <span style={{ marginLeft: 8, color: "#605CE5"}}>
              <CheckOutlined />
            </span>
          )}
        </div>
      ),
      disabled: level === 0,
      key: item.id,
      children: item.children ? processTreeData(item.children, level + 1) : undefined,
    }));

  // 2. 再递归处理（高亮、禁用、title等）
  const processedTreeData = useMemo(
    () => processTreeData(filteredTreeData),
    [filteredTreeData, selectedNode]
  );


  const handleSelect = (node: VariableTreeNode) => {
    console.log('handleSelect--', node)
    setSelectedNode(node);
  };

  const handleOk = () => {
    if (selectedNode) {
      onOk(selectedNode);
      setSelectedNode(null);
      setSearchValue("");
      onClose();
    }
  };

  const content = (
    <div className="data-variables-select-popover">
      <Input className="data-variables-search"
        prefix={<SearchOutlined />}
        placeholder={t('dj-请输入')}
        value={searchValue}
        onChange={e => setSearchValue(e.target.value)}
        style={{ marginBottom: 16 }} />
      <div className="tree-container">
        {processedTreeData.length === 0 ? (
          <div className="empty-tips">
            <div className="img"></div>
            <div>{t('dj-暂无数据')}</div>
          </div>
        ) : (
          <VariableTree
            treeData={processedTreeData}
            onSelect={handleSelect}
            selectedKey={selectedNode?.id}
          />
        )}
      </div>
      <Button className="data-variables-btn"
        type="primary"
        style={{ marginTop: 24 }}
        disabled={!(selectedNode && processedTreeData.length)}
        onClick={handleOk}
      >
        插入
      </Button>
    </div>
  );

  return (
    <Popover
      // title={t('dj-插入变量')}
      open={open}
      arrow={false}
      // placement="right"
      onOpenChange={visible => !visible && onClose()}
      content={content}
      trigger="click"
      overlayClassName="data-variables-select-popover-overlay"
    >
      {trigger}
    </Popover>
  );
};

export { VariablesPopover };