import React, { useState, cloneElement, isValidElement } from "react";
import { VariablesPopover } from "./VariablesPopover";
import { EnterOutlined } from '@ant-design/icons';

interface Props {
  onSelect: (node: any) => void;
  trigger?: React.ReactNode; // 支持自定义触发元素
}

const VariableSelect: React.FC<Props> = ({
  onSelect,
  trigger,
}) => {
  const [open, setOpen] = useState(false);

  // 包装 trigger，自动注入 onClick 打开 Popover
  const triggerNode = isValidElement(trigger)
    ? cloneElement(trigger, {
      onClick: (e: any) => {
        e.stopPropagation();
        setOpen(true);
        trigger.props.onClick && trigger.props.onClick(e);
      }
    })
    : (
      <EnterOutlined  onClick={e => {
        e.stopPropagation()
        setOpen(true);
      }} style={{ fontSize: 12, color: '#1D1C33' }} />
    );

  return (
    <VariablesPopover
      open={open}
      onOk={node => {
        // todo 将变量直接{{}}传递
        const {keyName} = node
        onSelect(`{{${keyName}}}`);
        setOpen(false);
      }}
      onClose={() => setOpen(false)}
      trigger={triggerNode}
    />
  );
};

export default VariableSelect;
