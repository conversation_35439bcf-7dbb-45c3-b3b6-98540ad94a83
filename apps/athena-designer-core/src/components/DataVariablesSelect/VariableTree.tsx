import React, { useState, useEffect, useMemo } from "react";
import { Tree } from "antd";
import type { VariableTreeNode } from "../DataVariables/types/index";
interface Props {
  treeData: VariableTreeNode[];
  selectedKey?: string;
  onSelect: (node) => void;
}

const VariableTree: React.FC<Props> = ({ treeData, onSelect, selectedKey }) => {
  return (
    <Tree
      treeData={treeData}
      selectedKeys={selectedKey ? [selectedKey] : []}
      onSelect={(_, { node }) => {
        onSelect(node);
      }}
      showIcon={false}
      defaultExpandAll
    />
  );
};

export default VariableTree;