.data-variables-select-popover {
  .data-variables-search {
    border-radius: 4px;
  }

  .tree-container {
    max-height: 320px;
    overflow: auto;
    margin: 0 24px;
  }

  .data-variables-btn {
    border-radius: 4px;
    width: 100%;
    font-size: 14px;
  }

  .data-variables-btn:disabled {
    background-color: #C2C0FC;
    color: #fff;
  }

  .ant-tree-treenode {
    width: 100% !important;
  }

  .ant-tree-title {
    width: 100% !important;
  }

  .ant-tree-node-selected {
    background-color: #fff !important;
  }

  .empty-tips {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    font-size: 14px;
    color: #8C8B99;
    .img {
      background-image: url('~@/assets/img/data-empty.png');
      background-size: 100%;
      width: 104px;
      height: 104px;
    }
  }

  // .ant-btn-primary {
  //   margin: 24px 24px 0 24px;
  //   border-radius: 8px;
  //   background: #605CE5;
  //   border: none;
  //   font-weight: 500;
  // }
}