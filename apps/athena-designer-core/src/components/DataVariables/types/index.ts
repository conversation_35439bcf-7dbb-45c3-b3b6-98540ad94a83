// export interface DataVariablesProps {
//     variables: VariableItem[];
//     onChange: () => {}
//     selectedId?: string;
//     onSelect: (id: string) => void;
//     onSearch: (keyword: string) => void;
//   }

type TScopeType = 'system' | 'user'
export interface VariableItem {
    id: string;
    name?: string;
    type?: string;
    defaultValue?: string;
    description?: string;
    disabled?: boolean;
    scope?: string
  }
  export interface VariableTreeNode extends VariableItem {
    id: string;
    name?: string; // 变量名称
    children?: VariableTreeNode[];
    [key: string]: any; // 允许扩展
  }

  // export interface VariableGroupItem {
  //   type: TScopeType;
  //   id: string;
  //   name: string;
  //   title: string;
  //   showGroupEmpty: boolean;
  //   chil: VariableItem[]
  // }
  // 分组参数
export interface GroupRule {
  key: string;      // 分组字段名，如 'scope'
  value: TScopeType;    // 字段值，如 'system'
  name: string;     // 分组显示名，如 '系统变量'
  showGroupEmpty?: boolean // 当分组数据为空时，是否需要展示
}

export enum VariableType {
  System = 'system',
  User = 'user',
}
