import {VariableItem,GroupRule,VariableTreeNode, VariableType } from './types'
import { v4 as uuidv4 } from 'uuid';

const groupRules: GroupRule[] = [
  { key: "scope", value: VariableType.System, name: "dj-系统变量", showGroupEmpty: false },
  { key: "scope", value: VariableType.User , name: "dj-用户变量", showGroupEmpty: true }
];
/**
 * 将 VariableItem[] 按规则分组为 VariableTreeNode[]
 * @param variables 原始变量数组
 * @param groupRules 分组规则
 */
export function groupVariablesByRule(
    variables: VariableItem[]
  ): VariableTreeNode[] {
    return groupRules.map(rule => ({
      id: uuidv4(),
      name: rule.name,
      type: rule.value,
      showGroupEmpty: rule.showGroupEmpty,
      children: variables
        .filter(item => item[rule.key] === rule.value)
        .map(item => ({
          ...item,
          keyName: `variables.${item.scope || VariableType.User}.${item.name}`,
          disabled: item?.scope === VariableType.System,
          id: item.id || uuidv4()
        }))
    }));
  }

  export function flattenGroupVariables(groups: VariableTreeNode[]): VariableItem[] {
    return groups.flatMap(group => group.children);
  }