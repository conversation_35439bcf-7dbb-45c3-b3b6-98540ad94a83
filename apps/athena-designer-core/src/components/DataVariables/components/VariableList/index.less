.variable-list {
    padding: 8px  16px 10px 16px;
    max-height: calc(100% - 50px);
    overflow-y: auto;
    .search {

    }
    .variable-list-box {
        background-color: #FAFAFC;
        border-radius: 4px;
        margin-top: 8px;
        padding-bottom: 8px;
        cursor: pointer;
        border: 1px solid #FAFAFC;
        // &:hover {
        //     border-color: #605CE5;  
        // }
    }
    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 8px 10px 8px;
        border-bottom: 1px solid #F0F0F5;
        .header-left {
            font-size: 14px;
            font-weight: 600;
            color: #3D3D3D;
        }
        .header-right {
            color: #1D1C33;
        }

    }
    .content {
        margin-top: 2px;
        .content-title {
            padding-top: 8px;
            font-size: 14px;
            font-weight: normal;
            color: #8C8B99;
            margin: 0 8px;
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-left: 8px;
        }
    }
    .empty-tips {
        text-align: center;
        padding: 10px 0;
        font-size: 12px;
        color: #666;
    }
    .bottom {
        .collapse-button {
            font-size: 10px;
            color: #8C8B99;
            cursor: pointer;

        }
    }
}