import React, { useState, useMemo, useEffect } from "react";
import VariableList from "./components/VariableList";
import VariableDetailDrawer from "./components/VariableDetailDrawer";
import type { VariableItem, VariableTreeNode } from "./types";
import "./index.less";
import { groupVariablesByRule, flattenGroupVariables } from './utils'

// const mockVariables: VariableItem[] = [
//   { id: "1", name: "sys_uuid", type: "string", enabled: true, description: "用户唯一ID" },
//   { id: "2", name: "variable_1", type: "string", enabled: false, description: "示例变量" },
// ];




interface DataVariablesProps {
  variables: VariableItem[];
  onChange: (vars: VariableItem[]) => void;
}


const DataVariables: React.FC<DataVariablesProps> = ({ variables, onChange }) => {

  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedVariable, setSelectedVariable] = useState<VariableTreeNode>()
  const [searchKeyword, setSearchKeyword] = useState('');
  const groupedVariables = useMemo(
    () => groupVariablesByRule(variables),
    [variables]
  );

  // const [filteredGroups, setFilteredGroups] = useState(groupedVariables);

  // useEffect(() => {
  //   if (!searchKeyword) {
  //     setFilteredGroups(groupedVariables);
  //     return;
  //   }
  //   const newGroups = groupedVariables
  //     .map(group => ({
  //       ...group,
  //       data: group.data.filter(item =>
  //         item.name.includes(searchKeyword) || (item.description && item.description.includes(searchKeyword))
  //       )
  //     }))
  //     .filter(group => group.data.length > 0);
  //   setFilteredGroups(newGroups);
  // }, [searchKeyword, groupedVariables]);

  const handleSearch = (e) => {
    // setSearchKeyword(e.target.value);
  };

  const handleSelect = (groupItem) => {
    setSelectedVariable(groupItem)
    setDrawerOpen(true);
  };


  const handleSave = (items: VariableItem[]) => {
    const newGroupedVariables = groupedVariables.map(item => {
      if (item.type === selectedVariable.type) {
        item.children = items
      }
      return item
    })
    setDrawerOpen(false);
    onChange?.(flattenGroupVariables(newGroupedVariables))
    console.log('handleSave--', items)
  };





  return (
    <div className="data-variables-box">
      <div className="left-panel">
        <VariableList
          variableList={groupedVariables}
          onSelect={handleSelect}
          onSearch={handleSearch}
        />
      </div>
      <VariableDetailDrawer
        visible={drawerOpen}
        variable={selectedVariable}
        onClose={() => setDrawerOpen(false)}
        onSave={handleSave}
      />
    </div>
  );
};

export { DataVariables };