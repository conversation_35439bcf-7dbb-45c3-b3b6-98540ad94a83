import React from 'react';
import DataConnectorsItem from './DataConnectorsItem/index';
import type { DataConnectorsListProps } from '../../types/index';

const DataConnectorsList: React.FC<DataConnectorsListProps> = ({
  dataList,
  onEdit,
  onDelete,
  onCopy,
}) => {
  return (
    <div className="dsl-data-list" >
      {dataList.map((item, index) => (
        <DataConnectorsItem
          key={item.id}
          data={item}
          index={index}
          onEdit={onEdit}
          onDelete={onDelete}
          onCopy={onCopy}
        />
      ))}
    </div>
  );
};

export default DataConnectorsList; 