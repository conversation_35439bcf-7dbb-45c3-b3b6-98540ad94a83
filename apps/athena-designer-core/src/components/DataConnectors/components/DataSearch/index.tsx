import React, { useCallback, useRef } from 'react';
import { Input, Button } from 'antd';
import Icon from '@components/Icon';
import { PlusOutlined } from '@ant-design/icons';
import type { DataSearchProps } from '../../types/index';
import debounce from 'lodash/debounce';
import './index.less'
import { useTranslation } from 'react-i18next';

const DataSearch: React.FC<DataSearchProps> = ({ onInputChange, onAdd, placeholder, onImport }) => {
  const { t } = useTranslation();
  // 用 useRef 保证 debounce 不会每次渲染都变
  const debouncedInputChange = useRef(
    debounce((val: string) => {
      onInputChange(val);
    }, 500)
  ).current;

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      debouncedInputChange(e.target.value);
    },
    [debouncedInputChange]
  );

  return (
    <div className='data-search-header'>
      <Button type="primary"
        onClick={onAdd}
        className='add-data-source-btn'
        icon={<Icon type="icontianjiashuju-xian" className="iconfont" />}>
        {t('dj-添加数据源')}
      </Button>
      <Button
        onClick={onImport}
        className='add-data-source-btn' style={{width:70}}
        icon={<Icon type="icondaoru" style={{fontSize:12}}
          className="iconfont" />}>{t('dj-导入')}
      </Button>
    </div>


    // <div style={{ display: 'flex', alignItems: 'center' }}>
    //   <Input
    //     placeholder={placeholder || '请输入内容'}
    //     onChange={handleChange}
    //     style={{ flex: 1 }}
    //   />


    // </div>
  );
};

export default DataSearch;