import React, { useState, useCallback } from 'react';
import { Table } from 'antd';
import Icon from '@components/Icon';
import { PlusSquareFilled, MinusSquareFilled } from '@ant-design/icons';
import { v4 as uuidv4 } from 'uuid';
import { useTranslation } from 'react-i18next';
import DraggableBodyRow, { MoveRowContext } from '../DraggableBodyRow';
import { getColumns } from './columns';
import {NestedEditableTableProps, NestedEditableRow} from '../../../../types'
import {
  updateRowById,
  findParentArrAndIndex,
  deleteRowById,
  replaceArr,
} from '../../../../utils/tableUtils';
import './index.less';



const NestedEditableTable: React.FC<NestedEditableTableProps> = ({
  dataSource,
  onChange,
  editingRowId,
  setEditingRowId,
  onAdd,
  onBlur,
  onFieldChange,
  showHeader = true,
  errors
}) => {
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const {t} = useTranslation()
  // 拖拽排序
  const moveRow = useCallback(
    (dragId: string, hoverId: string) => {
      const dragInfo = findParentArrAndIndex(dataSource, dragId);
      const hoverInfo = findParentArrAndIndex(dataSource, hoverId);
      if (!dragInfo || !hoverInfo || dragInfo.parentArr !== hoverInfo.parentArr) {
        return;
      }
      const arr = [...dragInfo.parentArr];
      const [dragRow] = arr.splice(dragInfo.index, 1);
      arr.splice(hoverInfo.index, 0, dragRow);
      const newData = replaceArr(dataSource, dragInfo.parentArr, arr);
      onChange(newData);
    },
    [dataSource, onChange]
  );

  const handleBlur = () => {
    onBlur?.();
  }

  // 新增
  const handleAdd = (parentRow?: NestedEditableRow) => {
    const newRow: NestedEditableRow = {
      id: uuidv4(),
      name: '',
      dataType: 'string',
      description: '',
    };
    if (parentRow) {
      const newData = updateRowById(dataSource, parentRow.id, row => ({
        ...row,
        children: [...(row.children || []), newRow],
      }));
      onAdd(newData);
      setEditingRowId(newRow.id);
      setExpandedRowKeys(prev =>
        prev.includes(parentRow.id) ? prev : [...prev, parentRow.id]
      );
    } else {
      onAdd([...dataSource, newRow]);
      setEditingRowId(newRow.id);
    }
  };

  // 删除
  const handleDelete = (row: NestedEditableRow) => {
    const newData = deleteRowById(dataSource, row.id);
    onChange(newData);
    if (editingRowId === row.id) setEditingRowId(null);
  };

  // 编辑
  const handleFieldChange = (row: NestedEditableRow, field: string, value: any) => {
    row[field] = value;
    if (field === 'dataType' && (value === 'object' || value === 'array')) {
      row.children = row.children || [];
    }
    onFieldChange([...dataSource]);
  };
  /**
 * 递归遍历，唯一标记或只取消当前行
 * @param {Array} data - 数据源
 * @param {string} targetId - 目标id
 * @param {boolean} onlyCancel - 是否只取消当前行
 * @returns {Array}
 */
function updateMarked(data, targetId, onlyCancel = false) {
  return data.map(item => {
    const children = item.children ? updateMarked(item.children, targetId, onlyCancel) : undefined;
    if (onlyCancel) {
      // 只取消当前行
      if (item.id === targetId) {
        return { ...item, isDataBody: false, ...(children ? { children } : {}) };
      }
      return { ...item, ...(children ? { children } : {}) };
    } else {
      // 全表唯一标记
      const isDataBody = item.id === targetId;
      return { ...item, isDataBody, ...(children ? { children } : {}) };
    }
  });
}

// 用法
const handleMarked = (row, isDataBody) => {
  const newData = updateMarked(dataSource, row.id, !isDataBody);
  onChange(newData);
};

  const columns = getColumns({
    editingRowId,
    setEditingRowId,
    handleFieldChange,
    handleAdd,
    handleDelete,
    handleMarked,
    handleBlur,
    t,
    errors
  });

  const expandIcon = ({ expanded, onExpand, record }: any) =>
    record.dataType === 'object' || record.dataType === 'array' ? (
      <div className='cust-expand-box'>
        <Icon className='move-icon' type="icontuozhuai1" style={{ marginRight: 8 }} />
        <div
          className='cust-expand-icon'
          onClick={e => {
            e.stopPropagation();
            onExpand(record, e);
          }}
        >
          {expanded ? <Icon type='iconbiaogeshouqi-mian' /> : <Icon type='iconbiaogezhankai-mian' />}
        </div>
      </div>
    ) : (
      <Icon className='move-icon' type="icontuozhuai1" style={{ marginRight: 8 }} />
    );

  return (
    <MoveRowContext.Provider value={moveRow}>
      <Table
        columns={columns}
        className='table-box'
        dataSource={dataSource}
        rowKey="id"
        pagination={false}
        showHeader={showHeader}
        components={{
          body: { row: DraggableBodyRow },
        }}
        locale={{ 
          emptyText: <div style={{ paddingTop: '16px', textAlign: 'center' }}>{t('dj-暂无数据')}</div> 
        }}
        expandable={{
          expandIcon,
          expandIconColumnIndex: 0,
          expandedRowKeys,
          onExpand: (expanded, record) => {
            setExpandedRowKeys(
              expanded
                ? [...expandedRowKeys, record.id]
                : expandedRowKeys.filter(k => k !== record.id)
            );
          },
          rowExpandable: record => record.dataType === 'object' || record.dataType === 'array',
        }}
      />
    </MoveRowContext.Provider>
  );
};

export default NestedEditableTable;