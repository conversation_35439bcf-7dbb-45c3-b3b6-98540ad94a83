// columns.tsx
import React, { useState } from 'react';
import { Space, Tooltip, Switch } from 'antd';
import { PlusCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';
import {EditableCellInput, EditableSelect} from '../EditableCell'
import Icon from "@components/Icon"
const typeOptions = [
    { label: 'string', value: 'string' },
    { label: 'number', value: 'number' },
    { label: 'boolean', value: 'boolean' },
    { label: 'object', value: 'object' },
    { label: 'array', value: 'array' },
];



export function getColumns({
    editingRowId,
    setEditingRowId,
    handleFieldChange,
    handleAdd,
    handleDelete,
    handleMarked,
    handleBlur,
    t,
    errors
}) {
    return [
        {
            title: `${t('dj-参数名')}`,
            dataIndex: 'name',
            render: (text, row) => (
                <EditableCellInput
                    showSuffix={false}
                    value={row.name}
                    onBlur={handleBlur}
                    status={errors[row.id]?'error':undefined}
                    editing={editingRowId === row.id}
                    onClickCell={() => setEditingRowId(row.id)}
                    onChange={v => handleFieldChange(row, 'name', v)}
                />
            ),
        },
        {
            title: `${t('dj-类型')}`,
            dataIndex: 'dataType',
            width: 120,
            render: (text, row) => (
                <EditableSelect
                options={typeOptions}
                    value={row.dataType}
                    onBlur={handleBlur}
                    editing={editingRowId === row.id}
                    onClickCell={() => setEditingRowId(row.id)}
                    onChange={v => handleFieldChange(row, 'dataType', v)}
                />
            ),
        },
        {
            title: `${t('dj-说明')}`,
            dataIndex: 'description',
            render: (text, row) => (
                <EditableCellInput 
                    showSuffix={false}
                    onBlur={handleBlur}
                    onClickCell={() => setEditingRowId(row.id)}
                    value={row.description}
                    editing={editingRowId === row.id}
                    onChange={v => handleFieldChange(row, 'description', v)}
                />
            ),
        },
        {
            title: `${t('dj-操作')}`,
            dataIndex: 'operation',
            width: 120,
            render: (_, row) => (
                <Space >
                    
                    {(row.dataType === 'object' || row.dataType === 'array') && (
                        <Tooltip overlayClassName="mf-override-tooltip" title={t('dj-添加子节点')}>
                            <Icon type='iconzengjiazijiedian-xian' 
                                style={{ color: "#605CE5", fontSize: 18, cursor: 'pointer' }}
                                onClick={() => handleAdd(row)}
                            />
                        </Tooltip>
                    )}
                    <Tooltip overlayClassName="mf-override-tooltip" title={t('dj-删除')}>
                        <Icon type='icondelete3'
                            style={{ color: "#605CE5", fontSize: 14, cursor: 'pointer' }}
                            onClick={() => handleDelete(row)}
                        />
                    </Tooltip>
                    {(row.dataType === 'object' || row.dataType === 'array') && (
                        <Tooltip title={t('dj-设为数据体')} className='set-icon'>
                            <Switch style={{ width: 16, height: 16 }}
                                checked={row.isDataBody}
                                onChange={isDataBody => handleMarked({...row,isDataBody}, isDataBody)} />
                        </Tooltip>

                    )}

                </Space>
            ),
        },

    ];
}
