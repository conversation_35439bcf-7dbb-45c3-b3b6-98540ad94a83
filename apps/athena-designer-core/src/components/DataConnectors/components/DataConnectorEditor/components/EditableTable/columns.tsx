import React from 'react';
import { Input, Select, Tooltip } from 'antd';
import { MinusCircleOutlined } from '@ant-design/icons';
import Icon from '@components/Icon';
import { EditableCellInput, EditableSelect } from '../EditableCell'

// 通用列工厂
export const getColumns = (
  {
    t,
    handleFieldChange,
    handleBlur,
    handleDelete,
    typeOptions,
    fields,
    editingRowId,
    setEditingRowId,
    errors={}
  }


) => {
  // 字段与渲染映射
  const fieldMap = {
    key: {
      title: `${t('dj-参数名')}`,
      dataIndex: 'key',
      render: (text, row) => (
        <div style={{ display: 'flex' }}>
          <Icon className='move-icon' style={{ cursor: 'move', color: '#1D1C33', fontSize: 16, marginRight: 12 }} type='icontuozhuai1' />
          <EditableCellInput showSuffix={false}
            value={text}
            status={errors[row.id] ? 'error' : undefined}
            editing={editingRowId === row.id}
            onBlur={handleBlur}
            onClickCell={() => setEditingRowId(row.id)}
            onChange={v => handleFieldChange(v, row.id, 'key')}
          />
        </div>
      ),
    },
    type: {
      title: `${t('dj-类型')}`,
      dataIndex: 'type',
      render: (text, row) => (
        <EditableSelect
          value={text}
          options={typeOptions}
          editing={editingRowId === row.id}
          onClickCell={() => setEditingRowId(row.id)}
          onBlur={handleBlur}
          onChange={v => handleFieldChange(v, row.id, 'type')}
        />
      ),
    },
    value: {
      title: `${t('dj-参数值')}`,
      dataIndex: 'value',
      render: (text, row) => (
        <EditableCellInput 
          value={text}
          showSuffix={true}
          onBlur={handleBlur}
          editing={editingRowId === row.id}
          onClickCell={() => setEditingRowId(row.id)}
          onChange={v => handleFieldChange(v, row.id, 'value')}
        />
      ),
    },
    description: {
      title: `${t('dj-说明')}`,
      dataIndex: 'description',
      render: (text, row) => (
        <EditableCellInput showSuffix={false}
          value={text}
          onBlur={handleBlur}
          editing={editingRowId === row.id}
          onClickCell={() => setEditingRowId(row.id)}
          onChange={v => handleFieldChange(v, row.id, 'description')}
        />
      ),
    },
  };

  // 操作列
  const baseColumns = [
    ...fields.map(f => fieldMap[f]),
    {
      title: `${t('dj-操作')}`,
      dataIndex: 'operation',
      width: 80,
      render: (_, record) => (
        <Tooltip overlayClassName="mf-override-tooltip" title={t('dj-删除')}>
          <Icon type='icondelete3'
          style={{ color: "#605CE5", fontSize: 14, cursor: 'pointer' }}
          onClick={() => handleDelete(record.id)}
        />
        </Tooltip>
        
      ),
    },
  ];
  return baseColumns;
};

// 新增方法：根据fields和typeOptions动态生成columns
export const getColumnsByFields = (
  fields: Array<'key' | 'type' | 'value' | 'description'>,
  typeOptions: Array<{ label: string; value: string }> = [
    { label: 'string', value: 'string' },
    { label: 'number', value: 'number' },
    { label: 'boolean', value: 'boolean' },
  ],
) => ({
  t,
  handleFieldChange,
  handleBlur,
  handleDelete,
  editingRowId,
  setEditingRowId,
  errors
}) => getColumns({
  t, handleFieldChange, handleBlur, handleDelete, typeOptions, fields, editingRowId,
  setEditingRowId,errors
});