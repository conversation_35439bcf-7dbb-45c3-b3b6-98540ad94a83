import React, { useEffect, useState } from 'react';
import { Collapse } from 'antd';
import type { CollapseProps } from 'antd';
import { CaretRightOutlined } from '@ant-design/icons';
import './index.less'
import { CheckOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { Button, Flex, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import MonacoEditor from '@monaco-editor/react';

interface PreviewDataProps {
  data: any;
  height: number;
  onHeightChange?: (height: number) => void;
  autoExpand?: boolean; // 是否自动展开面板
}



const renderChildrenContent = (data, height = 120, t) => {

  if (data?.resInfo) {
    return (
      <div className='pre-box'>
        <MonacoEditor className={`tips ${data?.isSuccess ? 'success' : 'error'}`}
          language="json"
          value={JSON.stringify(data?.resInfo, null, 2)}
          options={{
            readOnly: true,
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            fontSize: 12,
            lineNumbers: 'on',
            folding: true,
            wordWrap: 'on'
          }}
          height={height - 110}
        />

      </div>
    )
  } else {
    return (
      <div className='empty'>
        <div className='img'></div>
        <div> {data?.showCilckTips ? t('dj-点击立即请求') : t('dj-暂无数据')}</div>
      </div>
    )
  }
}

const PreviewData: React.FC<PreviewDataProps> = ({
  data,
  height,
  onHeightChange,
  autoExpand = false
}) => {
  const [activeKeys, setActiveKeys] = useState<string[]>([]);
  const { t } = useTranslation()
  // 监听 autoExpand 变化，自动展开面板
  useEffect(() => {
    if (autoExpand && data?.resInfo) {
      setActiveKeys(['1']);
      // 同时调整高度
      if (onHeightChange) {
        onHeightChange(300);
      }
    }
  }, [autoExpand, data?.resInfo, onHeightChange]);


  const handleCollapseChange = (keys: string[]) => {
    setActiveKeys(keys);

    if (onHeightChange) {
      onHeightChange(keys.length > 0 ? 300 : 120);
    }
  };
  const renderLabel = (data) => {
    return (
      <div className='preview-data-title'>
        <div className='title'>{t('dj-返回预览')}</div>
        {!data?.showCilckTips ? (
          <div className={`tips ${data?.isSuccess ? 'success' : 'error'}`}>
            {data?.isSuccess ? <CheckOutlined /> : <ExclamationCircleOutlined />}
            <span style={{ paddingLeft: 4 }}>{data?.isSuccess ? t('dj-请求成功') : t('dj-请求失败')}</span>
          </div>
        ) : null}
      </div>
    );
  };
  const items: CollapseProps['items'] = [
    {
      key: '1',
      label: renderLabel(data),
      children:
        <div className='preview-data-content'
          style={{ height: height - 100, overflowY: data?.showCilckTips ? 'auto' : 'hidden' }}>
          {renderChildrenContent(data, height, t)}
        </div>
    },
  ];

  return (
    <div className='preview-data-box'>
      <Collapse
        activeKey={activeKeys}
        onChange={handleCollapseChange}
        expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
        ghost
        items={items} />
    </div>
  );
};

export default PreviewData;