import React, { useState, useEffect, useCallback } from 'react';
import { Modal, message, Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import JsonEditor from '../JsonEditor'
import NestedEditableTable from './NestedEditableTable';
import { v4 as uuidv4 } from 'uuid';
import { jsonToTableRows } from '../../../../utils/tableUtils'
import { useTranslation } from 'react-i18next';
import Icon from '@components/Icon';

import { NestedEditableTableWrapperProps } from '../../../../types'
import './index.less'

function generateRow() {
    return {
        id: uuidv4(),
        name: '',
        dataType: 'string',
        description: '',
    };
}
// 校验同层级 name 是否重复
const validateSameLevelNames = (data, parentErrors: Record<string, string> = {}) => {
    const nameCount: Record<string, number> = {};
    const currentLevelErrors: Record<string, string> = { ...parentErrors };

    data.forEach(item => {
        if (item.name) {
            if (nameCount[item.name]) {
                currentLevelErrors[item.id] = '同层级参数名不能重复';
            } else {
                nameCount[item.name] = 1;
            }
        }
        if (item.children) {
            Object.assign(currentLevelErrors, validateSameLevelNames(item.children, currentLevelErrors));
        }
    });

    return currentLevelErrors;
};


// 外层包裹
const NestedEditableTableWrapper: React.FC<NestedEditableTableWrapperProps> = ({ value, onChange }) => {
    const [data, setData] = useState(Array.isArray(value) ? value : []);
    const [editingRowId, setEditingRowId] = useState<string | null>(null);
    const [jsonModalVisible, setJsonModalVisible] = useState(false);
    const [jsonValue, setJsonValue] = useState('{}'); // 默认空对象
    const { t } = useTranslation()
    const [errors, setErrors] = useState<Record<string, string>>({});
    // 保证受控
    useEffect(() => {
        setData(Array.isArray(value) ? value : []);
    }, [value]);

    const handleChange = (newData) => {
        setData(newData);
        onChange?.(newData);
    }
    const handleBlur = () => {
        const newErrors = validateSameLevelNames(data);
        setErrors(newErrors);
        const isValid = Object.keys(newErrors).length === 0;
         if (isValid) {
            onChange?.(data);
        } else {
            message.warning(t('dj-参数名不能重复，请检查'));
        }
    }
    const handleAddField = () => {
        const newRow = generateRow();
        const newData = [...data, newRow];
        setData(newData);
        setEditingRowId(newRow.id);
    }
    const handleAddJson = () => {
        setJsonValue('{}')
        setJsonModalVisible(true);
    }
    const handleJsonOk = () => {
        try {
          const parsed = JSON.parse(jsonValue);
          const rows = Array.isArray(parsed)
            ? parsed.map((item, idx) => jsonToTableRows(item, String(idx)))
            : Object.entries(parsed).map(([key, value]) => jsonToTableRows(value, key));
          handleChange(rows);
          setJsonModalVisible(false);
        } catch (e) {
          message.error(t('dj-请输入合法的JSON格式'));
        }
      };

    return (
        <>
            <div className='nested-table-box params-box-tab'>
                <div className='tab-header'>

                    <span className="header-left">
                        <div className='title'>
                            {t('dj-数据结构')}
                        </div>
                    </span>
                    <div className='header-right'>
                        <Button type="link" className='btn' onClick={handleAddField} icon={<PlusOutlined />}>
                            {t('dj-添加字段')}
                        </Button>
                        <Button type="link" className='btn' onClick={handleAddJson} icon={<Icon style={{ fontSize: 18 }} type="icondaorujilu-xian" />}>{t('dj-导入JSON')}</Button>
                    </div>
                </div>
                <DndProvider backend={HTML5Backend}>
                    <NestedEditableTable
                        dataSource={data}
                        onChange={handleChange}
                        onAdd={newData => setData(newData)}
                        onBlur={handleBlur}
                        onFieldChange={newData => setData(newData)}
                        editingRowId={editingRowId}
                        errors={errors}
                        setEditingRowId={setEditingRowId}
                    />
                </DndProvider>
            </div>
            <Modal className='json-modal'
                title={t('dj-导入JSON')}
                open={jsonModalVisible}
                onOk={handleJsonOk}
                onCancel={() => setJsonModalVisible(false)}
                width={600}
                destroyOnClose
            >
                <JsonEditor
                    value={jsonValue}
                    onChange={setJsonValue}
                    height={300}
                />
            </Modal>
        </>

    );
};

export default NestedEditableTableWrapper;