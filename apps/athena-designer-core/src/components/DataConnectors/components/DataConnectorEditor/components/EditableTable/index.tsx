import React, { useRef, useState, useEffect, useCallback, useMemo } from 'react';
import { Table, Button, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import type { EditableTableProps } from '../../../../types';
import { getColumnsByFields } from './columns';
import { useTranslation } from 'react-i18next';
import { v4 as uuidv4 } from 'uuid';
import DraggableBodyRow, { MoveRowContext } from '../DraggableBodyRow'



const EditableTable: React.FC<EditableTableProps> = ({ value, fields, typeOptions, leftTitle, onChange }) => {
  const { t } = useTranslation()
  const [localData, setLocalData] = useState(Array.isArray(value) ? value : []);
  const [editingKey, setEditingKey] = useState('');
    // 添加错误状态
  const [errors, setErrors] = useState<Record<string, string>>({});


  useEffect(() => {
    setLocalData(Array.isArray(value) ? value : []);
  }, [value]);

  const handleFieldChange = useCallback((fieldValue, id, dataIndex) => {
    setLocalData(prev =>
      prev.map(item =>
        item.id === id ? { ...item, [dataIndex]: fieldValue } : item
      )
    );
     // 清除对应行的错误
    setErrors(prev => ({ ...prev, [id]: '' }));
  }, []);
  // 校验参数名是否唯一
  const validateKeys = useCallback((data) => {
    const keySet = new Set();
    const newErrors: Record<string, string> = {};
    const uniqueData = [];

    data.forEach(item => {
      if (item.key) {
        if (keySet.has(item.key)) {
          newErrors[item.id] = t('dj-参数名必须唯一');
          // uniqueData.push({...item, key: ''});
        } else {
          keySet.add(item.key);
          uniqueData.push(item);
        }
      } else {
        uniqueData.push(item);
      }
    });

    setErrors({...newErrors});
    return {
      isValid: Object.keys(newErrors).length === 0,
      uniqueData
    };
  }, [t]);

  const handleBlur = useCallback(() => {
    const { isValid, uniqueData } = validateKeys(localData);
    if (isValid) {
      onChange?.(localData);
    } else {
      //  onChange?.(uniqueData);
      message.warning(t('dj-参数名不能重复，请检查'));
    }
  }, [localData, onChange]);

;

  const moveRow = useCallback((dragId: string, hoverId: string) => {
    const dragIndex = localData.findIndex(item => item.id === dragId);
    const hoverIndex = localData.findIndex(item => item.id === hoverId);
    if (dragIndex < 0 || hoverIndex < 0) return;
    const newData = [...localData];
    const dragRow = newData[dragIndex];
    newData.splice(dragIndex, 1);
    newData.splice(hoverIndex, 0, dragRow);
    setLocalData(newData);
    onChange?.(newData);
  }, [localData, onChange]);

  const handleAdd = useCallback(() => {
    const newRow = {
      id: uuidv4(),
      key: '',
      type: 'string',
      description: '',
      value: '',
    };
    const newData = [...localData, newRow];
    setLocalData(newData);
    setEditingKey(newRow.id);
  }, [localData]);

  const handleDelete = useCallback((id: string) => {
    const newData = localData.filter(item => item.id !== id);
    setLocalData(newData);
    if (editingKey === id) setEditingKey('');
    setErrors(prev => {
      const { [id]: _, ...rest } = prev;
      return rest;
    });
    onChange?.(newData);
  }, [localData, editingKey, onChange]);


  const columns = useMemo(
    () => getColumnsByFields(fields, typeOptions)({ t, handleFieldChange, handleBlur, handleDelete, editingRowId: editingKey, setEditingRowId: setEditingKey,errors }),
    [handleFieldChange, handleBlur, handleDelete, errors]
  );


  return (
    <div className='edit-tab-box'>
      <div className='tab-header ' style={{
        justifyContent: leftTitle ? 'space-between' : 'end'
      }}>
        {leftTitle && <div className="header-left"> <span className='title'>{leftTitle}</span> </div>}
        <div className='header-right'>
          <Button className='btn' type="link" icon={<PlusOutlined />} onClick={handleAdd}>
            {t('dj-添加字段')}
          </Button>
          {/* 本期不做 */}
          {/* <Button className='btn' type="link" onClick={handleAdd} icon={<PlusOutlined />} style={{ marginLeft: 16 }}>
            批量操作
          </Button> */}
        </div>
      </div>
      <MoveRowContext.Provider value={moveRow}>
        <Table
          className='table-box'
          columns={columns}
          dataSource={localData}
          rowKey="id"
          pagination={false}
          components={{
            body: { row: DraggableBodyRow }
          }}
          locale={{
            emptyText: <div style={{ paddingTop: '16px', textAlign: 'center' }}>{t('dj-暂无数据')}</div>
          }}
        />
      </MoveRowContext.Provider>
    </div>
  );
};

export default EditableTable;