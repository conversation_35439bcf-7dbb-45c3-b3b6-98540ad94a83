import React, { useEffect, useRef, useState } from 'react';
import { Button, Radio, Space } from 'antd';
import { CopyOutlined, DeleteOutlined, FormatPainterOutlined, RightOutlined, LeftOutlined } from '@ant-design/icons';
import MonacoEditor from '@monaco-editor/react';
import { IScriptEditorProps } from '../../../../types'
import './index.less';
import Icon from '@components/Icon'
import { useTranslation } from 'react-i18next';
import VariableSelect from '@/components/DataVariablesSelect'

const SNIPPETS = [
  { label: '获取环境变量', code: 'getEnvVar();' },
  { label: '设置环境变量', code: 'setEnvVar();' },
  { label: '获取环境临时变量', code: 'getTempEnvVar();' },
  { label: '请求接口', code: 'requestApi();' },
];


const ScriptEditor: React.FC<IScriptEditorProps> = ({
  value,
  onChange,
}) => {
  const { t } = useTranslation()
  const [tab, setTab] = useState<'preProcess' | 'postProcess'>('preProcess');
  const [snippetsCollapsed, setSnippetsCollapsed] = useState(false);
  const [code, setCode] = useState(value?.[tab] || '');
  const editorRef = useRef<any>(null);
  const handleTabChange = (newTab) => {
    const newValue = { ...value, [tab]: code }
    onChange?.(newValue);
    setTab(newTab)
    setCode(value?.[newTab] || '')
  }
  const handleDelete = () => {
    setCode('');
    const newValue =
    {
      ...value,
      [tab]: ''
    }

    onChange?.(newValue);
  };
  const handleFormat = () => {
    if (editorRef.current) {
      editorRef.current.getAction('editor.action.formatDocument').run();
    }
  };
  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor;
  };
  const handleChange = (val: string | undefined) => {
    setCode(val || '');
    const newValue =
    {
      ...value,
      [tab]: val || ''
    }

    onChange?.(newValue);
  };

  // 代码片段插入
  const handleInsertSnippet = (snippetCode: string) => {
    if (editorRef.current) {
      const selection = editorRef.current.getSelection();
      editorRef.current.executeEdits('', [
        {
          range: selection,
          text: snippetCode,
          forceMoveMarkers: true,
        },
      ]);
      editorRef.current.focus();
    }
  };
  // 插入变量
  const handleAddVariable = (varName) => {
    const newCode = `${code}${varName}`
    setCode(newCode)
    const newValue =
    {
      ...value,
      [tab]: newCode || ''
    }
    onChange?.(newValue);
  }

  return (
    <div className="script-editor-root">
      <div className="tab-header">
        <Radio.Group size='small'
          value={tab}
          onChange={e => handleTabChange(e.target.value)}
          buttonStyle="solid"
        >
          <Radio.Button value="preProcess">{t('dj-入参脚本')}</Radio.Button>
          <Radio.Button value="postProcess">{t('dj-出参脚本')}</Radio.Button>
        </Radio.Group>
        <div className='header-right'>
          {/* 变量todo */}
          <VariableSelect
            trigger={<Button className='btn' type='link' icon={<Icon type='iconbiangengdongtai-xian' />}>{t('dj-动态值')}</Button>}
            onSelect={handleAddVariable} />
          <Button className='btn' type='link' icon={<Icon type='iconsaozhou-xian1' className="iconfont" />} onClick={handleFormat}>{t('dj-格式化')}</Button>
          <Button className='btn' type='link' icon={<Icon type='icondelete3' style={{ fontSize: 14 }} />} onClick={handleDelete}>{t('dj-删除')}</Button>
        </div>
      </div>
      <div className="script-editor-body">

        <div className="script-editor-main">
          <MonacoEditor
            height={200}
            language={'javascript'}
            value={code}
            onChange={handleChange}
            onMount={handleEditorDidMount}
            options={{ minimap: { enabled: false }, formatOnPaste: true, formatOnType: true }}
          />
        </div>
        {/* <div className={`script-editor-snippets${snippetsCollapsed ? ' collapsed' : ''}`}>
          <div className="snippets-header" onClick={() => setSnippetsCollapsed(v => !v)}>
            <span>代码片段</span>
            {snippetsCollapsed ? <LeftOutlined />  : <RightOutlined />}
          </div>
          {!snippetsCollapsed && (
            <ul>
              {SNIPPETS.map(snippet => (
                <li key={snippet.label} onClick={() => handleInsertSnippet(snippet.code)}>
                  {snippet.label}
                </li>
              ))}
            </ul>
          )}
        </div> */}
      </div>
    </div>
  );
};

export default ScriptEditor;