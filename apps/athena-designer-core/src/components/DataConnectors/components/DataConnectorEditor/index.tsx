import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>er, Button, Space, message } from 'antd';
import ConfigFormLayout from './components/ConfigFormLayout';
import PreviewData from './components/PreviewData';
import { DataConnectorFormData, DataConnectorEditorProps, DataConnectorsItemType } from '../../types'
import { validateNestedFields } from '../../utils/index'
import { getInitialFormData, buildDataConnectorsItem } from '../../utils/formDataUtils'
import './index.less'
import { parseFormDataToRequestOptions, requestFactory } from '../../utils/request-factory';
import { useSplitterDrag } from '../../hooks/useSplitterDrag';
import { useTranslation } from 'react-i18next';

type ResData = {
  isSuccess?: boolean;
  showCilckTips: boolean;
  resInfo?: any;
  errorMsg?: string;
}; 
const requiredFields = [
  { key: 'name', tips: 'dj-接口名称必填' },
  { key: 'path', tips: 'dj-接口地址必填' },
  {key: 'method', tips: 'dj-接口类型必填'}
];

const DataConnectorEditor: React.FC<DataConnectorEditorProps> = ({ visible, currentItem, onClose, onSave, dataConnectorNameMap }) => {
  const [formData, setFormData] = useState<DataConnectorFormData>(getInitialFormData(currentItem));
  const [resData, setResData] = useState<ResData>({ showCilckTips: true })
  const [heightSize, setHeightSize] = useState(120);
  const [autoExpandPreview, setAutoExpandPreview] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const [resetKey, setResetKey] = useState(0);
  const {t} = useTranslation()

  const { startDragging } = useSplitterDrag({
    containerRef,
    minSize: 120,
    maxPercent: 0.8,
    onSizeChange: setHeightSize,
  });

  // 2. 弹窗打开时重置
  useEffect(() => {
    if (visible) {
      setFormData(getInitialFormData(currentItem));
      setResData({ showCilckTips: true });
      setHeightSize(120);
    }
  }, [visible, currentItem]);

  // 3. 关闭时重置
  const handleClose = () => {
    setResetKey(prev => prev + 1);
    setFormData(getInitialFormData());
    setResData({ showCilckTips: true });
    setHeightSize(120);
    setAutoExpandPreview(false)
    onClose?.();
  };


  const handleRequestApi = async () => {
    if (!validateNestedFields({data: formData?.baseInfo, requiredFields, message, t})) {
      return;
    }
    const requestOptions = parseFormDataToRequestOptions(formData);
    try {
      const res = await requestFactory(requestOptions)
      setAutoExpandPreview(true);
      setResData({
        isSuccess: true,
        showCilckTips: false,
        resInfo: res
      })

    } catch (error) {
      setResData({
        isSuccess: false,
        showCilckTips: false,
        resInfo: error
      })
    }

  };
   // 校验接口名称格式
  const validateNameFormat = (name: string) => {
    const isValid = /^[^\u4e00-\u9fa5\s]*$/.test(name);
    if (!isValid) {
      message.warning(t('dj-接口名称不能包含中文字符和空格'));
    }
    return isValid;
  };

  // 校验接口名称是否重复
  const validateNameDuplication = (name: string, isEdit: boolean, currentId?: string) => {
    const existingId = dataConnectorNameMap.get(name);
    if (existingId && (isEdit ? existingId !== currentId : true)) {
      message.warning(t('dj-接口名称已存在，请使用其他名称'));
      return false;
    }
    return true;
  };
  

  const handleSaveFormData = () => {
    console.log('dataConnectors--handleSaveFormData', formData)
    const {baseInfo} = formData
    if (!validateNestedFields({data: baseInfo, requiredFields, message, t})) {
      return; // 校验不通过，阻止保存
    }
    const {name} = baseInfo
     // 校验名称格式
    if (!validateNameFormat(name)) {
      return;
    }
    // 校验名称是否重复
    if (!validateNameDuplication(name, !!currentItem?.id, currentItem?.id)) {
      return;
    }
    const dataConnectorsItem = buildDataConnectorsItem(formData, currentItem);
    console.log('dataConnectors---buildDataConnectorsItem', dataConnectorsItem)
    setResetKey(prev => prev + 1);
    onSave?.(dataConnectorsItem)
  }

  // 在 DataConnectorEditor 中添加平滑过渡
  const handlePreviewHeightChange = (newHeight: number) => {
    // 添加平滑过渡动画
    setHeightSize(newHeight);
    // 用户手动调整高度后，重置自动展开状态
    setAutoExpandPreview(false);
  };
  return (
    <Drawer
      title={currentItem ? t('dj-编辑数据源') : t('dj-新建数据源')}
      width={'70%'}
      closeIcon={null}
      open={visible}
      onClose={handleClose}
      extra={
        <Space>
          <Button onClick={handleClose}>{t('dj-取消')}</Button>
          <Button type="primary" onClick={handleSaveFormData}>{t('dj-保存')}</Button>
        </Space>
      }
    >
      <div className='splitter-box' ref={containerRef}>
        <div className='splitter-mian' style={{
          height: `calc(100% - ${heightSize}px)`
        }}>
          <ConfigFormLayout key={resetKey}
            data={formData}
            onRequestApi={handleRequestApi}
            onBaseInfoFormChange={baseInfo => setFormData(prev => ({ ...prev, baseInfo }))}
            onParamsChange={paramsInfo => setFormData(prev => ({ ...prev, paramsInfo }))}
            onResponseForm={responseInfo => setFormData(prev => ({ ...prev, responseInfo }))}
          />
        </div>
        <div className='splitter-bottom' style={{ height: heightSize }}>
          <div className='splitter-line'
            onMouseDown={startDragging}
            onTouchStart={startDragging}
          >
            <div className='splitter-line-btn'></div>
          </div>
          <PreviewData
            key={resetKey}
            autoExpand={autoExpandPreview}
            onHeightChange={handlePreviewHeightChange}
            height={heightSize} data={resData} />
        </div>
      </div>

    </Drawer>
  );
};

export default DataConnectorEditor;