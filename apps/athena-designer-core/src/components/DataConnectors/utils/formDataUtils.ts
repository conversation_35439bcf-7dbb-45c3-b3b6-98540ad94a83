// src/components/DataConnectors/tools/formDataUtils.ts
import { v4 as uuidv4 } from 'uuid';
import { DataConnectorFormData, DataConnectorsItemType } from '../types';

export function getInitialFormData(currentItem?: any) {
  const getIdItems = (list: any[] = []) =>
    list.map(item => ({ ...item, id: item?.id || uuidv4() }));

  return {
    baseInfo: {
      name: currentItem?.name || '',
      method: currentItem?.option?.request?.method || '',
      path: currentItem?.option?.request?.path || ''
    },
    paramsInfo: {
      params: getIdItems(currentItem?.option?.request?.params),
      body: {
        type: 'json',
        content: currentItem?.option?.request?.body?.content || ''
      },
      headers: getIdItems(currentItem?.option?.request?.headers),
      advanced: {
        runOnPageLoad: currentItem?.runOnPageLoad || false
      },
      preEvent: {
        postProcess: currentItem?.option?.postProcess?.script || '',
        preProcess: currentItem?.option?.preProcess?.script || ''
      }
    },
    responseInfo: currentItem?.option?.response?.children || []
  };
}

function filterEmptyName(data, keyName='name') {
  return (data || [])
    .filter(item => !!item[keyName] && item[keyName].trim() !== '')
    .map(item => {
      const children = item.children ? filterEmptyName(item.children) : undefined;
      if (children && children.length > 0) {
        return { ...item, children };
      } else {
        const { children: _c, ...rest } = item;
        return rest;
      }
    });
}
// function filterEmptyBoName(data) {
//   return (data || [])
//     .filter(item => !!item?.key && item.key.trim() !== '')
// }


export function buildDataConnectorsItem(
  formData: DataConnectorFormData,
  currentItem?: DataConnectorsItemType
): DataConnectorsItemType {
  const { baseInfo, paramsInfo, responseInfo } = formData;
  const dataConnectorsItem: DataConnectorsItemType = {
    ...currentItem,
    name: baseInfo?.name,
    connectType: "api",
    runOnPageLoad: paramsInfo?.advanced?.runOnPageLoad,
    option: {
      request: {
        method: baseInfo?.method,
        path: baseInfo?.path,
        params: filterEmptyName(paramsInfo?.params || [], 'key') || [],
        body: paramsInfo?.body,
        headers: filterEmptyName(paramsInfo?.headers || [], 'key') || []
      },
      preProcess: {
        type: 'javascript',
        script: paramsInfo?.preEvent?.preProcess
      },
      postProcess: {
        type: 'javascript',
        script: paramsInfo?.preEvent?.postProcess
      }
    }
  };
  if (responseInfo?.length) {
    const filteredData = filterEmptyName(responseInfo);
    if (filteredData) {
      const response = currentItem?.option?.response || {};
      dataConnectorsItem.option.response = {
        ...response,
        name: response?.name || baseInfo?.name,
        dataType: response?.dataType || 'object',
        children: filteredData
      };
    }

  }
  return dataConnectorsItem;
}