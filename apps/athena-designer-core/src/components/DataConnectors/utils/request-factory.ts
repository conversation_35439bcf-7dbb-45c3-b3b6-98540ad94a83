import axios, { AxiosRequestConfig } from 'axios';
import { FormDataRequest, RequestOptions } from '../types/request';

// 工具函数：数组转对象
function arrToObj(arr: Array<{ key: string; value: any }>) {
  return Array.isArray(arr)
    ? arr.reduce((acc, cur) => {
        if (cur.key) acc[cur.key] = cur.value;
        return acc;
      }, {})
    : {};
}


export function parseFormDataToRequestOptions(formData: any): RequestOptions {
  // 1. 提取 baseInfo、paramsInfo 等
  const baseInfo = formData.baseInfo || {};
  const paramsInfo = formData.paramsInfo || {};

  // 2. params: [{key, value}] => { key: value }
  const params = arrToObj(paramsInfo.params || []);

  // 3. headers: [{key, value}] => { key: value }
  const headers = arrToObj(paramsInfo.headers || [])

  // 4. body: json字符串 => 对象
  let data;
  try {
    data = paramsInfo.body?.content ? JSON.parse(paramsInfo.body.content) : undefined;
  } catch {
    data = paramsInfo.body?.content || undefined;
  }

  // 5. pre/post 脚本
  const preScript = paramsInfo.preEvent?.preProcess || '';
  const postScript = paramsInfo.preEvent?.postProcess || '';

  // 6. 组装
  return {
    url: baseInfo.path,
    method: baseInfo.method || 'get',
    params,
    data,
    headers: {
      'Content-Type': 'application/json',
      ...headers,

    },
    preScript,
    postScript,
    timeout: 10000,
    retry: 3,
    retryDelay: 1000,
    // ...其它需要透传的字段
  };
}

// 执行 JS 脚本（让 context 可读写）
function runScript(script: string, context: Record<string, any> = {}) {
  if (!script) return;
  // 允许 script 直接操作 context（如 context.options）
  return new Function('context', `with(context){${script}}`)(context);
}

// 主工厂函数
export async function requestFactory(
  options: RequestOptions,
  extraOptions: {
    transformRequest?: (options: RequestOptions) => RequestOptions;
    transformResponse?: (response: any) => any;
    preScriptContext?: Record<string, any>;
    postScriptContext?: Record<string, any>;
  } = {}
) {
  // 构造 context，让 preScript 能操作 options
  const preContext = { options, ...(extraOptions.preScriptContext || {}) };

  // 前置 JS
  if (options.preScript) {
    runScript(options.preScript, preContext);
  }

  // preScript 可能已修改 options
  let finalOptions = preContext.options;

  // transformRequest
  if (extraOptions.transformRequest) {
    finalOptions = extraOptions.transformRequest(finalOptions);
  }


  let response;
  try {
    response = await axios(finalOptions as AxiosRequestConfig);

    // transformResponse
    let data = response.data;
    if (extraOptions.transformResponse) {
      data = extraOptions.transformResponse(data);
    }

    // 后置 JS
    if (options.postScript) {
      runScript(options.postScript, { ...extraOptions.postScriptContext, response: data });
    }

    return data;
  } catch (error) {
    throw error;
  }
}