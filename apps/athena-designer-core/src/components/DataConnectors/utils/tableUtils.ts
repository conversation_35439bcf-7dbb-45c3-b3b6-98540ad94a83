// 递归操作相关工具函数及类型定义

import {NestedEditableRow} from '../types'
import { v4 as uuidv4 } from 'uuid';

// 递归更新指定 id 的 children
export function updateRowById(list: NestedEditableRow[], id: string, updater: (row: NestedEditableRow) => NestedEditableRow): NestedEditableRow[] {
  return list.map(row => {
    if (row.id === id) {
      return updater(row);
    }
    if (row.children) {
      return { ...row, children: updateRowById(row.children, id, updater) };
    }
    return row;
  });
}

// 查找 id 所在的父数组及下标
export function findParentArrAndIndex(list: NestedEditableRow[], id: string, parentArr: NestedEditableRow[] | null = null): { parentArr: NestedEditableRow[]; index: number } | null {
  for (let i = 0; i < list.length; i++) {
    const row = list[i];
    if (row.id === id) {
      return { parentArr: list, index: i };
    }
    if (row.children) {
      const res = findParentArrAndIndex(row.children, id, list);
      if (res) return res;
    }
  }
  return null;
}

// 递归删除指定 id 的行
export function deleteRowById(list: NestedEditableRow[], id: string): NestedEditableRow[] {
  return list
    .filter(row => row.id !== id)
    .map(row =>
      row.children
        ? { ...row, children: deleteRowById(row.children, id) }
        : row
    );
}

// 递归替换指定数组
export function replaceArr(list: NestedEditableRow[], targetArr: NestedEditableRow[], newArr: NestedEditableRow[]): NestedEditableRow[] {
  if (list === targetArr) return newArr;
  return list.map(row =>
    row.children
      ? { ...row, children: replaceArr(row.children, targetArr, newArr) }
      : row
  );
} 




export function jsonToTableRows(value: any, name: string): any {
  const type = Array.isArray(value)
    ? 'array'
    : value === null
      ? 'null'
      : typeof value;

  const row: any = {
    id: uuidv4(),
    name,
    dataType: type,
  };

  if (type === 'object') {
    row.children = Object.entries(value).map(([k, v]) => jsonToTableRows(v, k));
  } else if (type === 'array') {
    if (value.length > 0) {
      const first = value[0];
      if (typeof first === 'object' && first !== null && !Array.isArray(first)) {
        // 如果是对象，children 是对象的每个字段
        row.children = Object.entries(first).map(([k, v]) => jsonToTableRows(v, k));
      } else {
        // 基础类型或数组，children 是一个 name 为 'item' 的节点
        row.children = [jsonToTableRows(first, 'item')];
      }
    } else {
      row.children = [];
    }
  } else {
    row.description = String(value);
  }

  return row;
}
// export function jsonToTableRows(value: any, name: string): any {
//   const type = Array.isArray(value)
//     ? 'array'
//     : value === null
//       ? 'null'
//       : typeof value;

//   const row: any = {
//     id: uuidv4(),
//     name,
//     dataType: type,
//   };

//   if (type === 'object') {
//     row.children = Object.entries(value).map(([k, v]) => jsonToTableRows(v, k));
//   } else if (type === 'array') {
//     // 只取第一个元素推断结构
//     if (value.length > 0) {
//       row.children = [jsonToTableRows(value[0], 'item')];
//     } else {
//       row.children = [];
//     }
//   } else {
//     row.description = String(value);
//   }

//   return row;
// }