import { RemoteModuleConfig } from './type';

export const athenaDesignerCoreConfigModules = [
  {
    module: 'react',
    alias: 'react18',
  },
  {
    module: 'reactDomClient',
    alias: 'react18DomClient',
  },
  {
    module: 'SelectFieldModal',
    alias: 'SelectFieldModal',
  },
  {
    module: 'MonacoEditor',
    alias: 'MonacoEditor',
  },
  {
    module: 'OpenwindowWorkDesign',
    alias: 'OpenwindowWorkDesign',
  },
  {
    module: 'OpenwindowWorkDesignLib',
    alias: 'OpenwindowWorkDesignLib',
  },
  {
    module: 'AppLangInput',
    alias: 'AppLangInput',
  },
  {
    module: 'ActionModal',
    alias: 'ActionModal',
  },
  {
    module: 'TagEditModal',
    alias: 'TagEditModal',
  },
  {
    module: 'PlatformIconModal',
    alias: 'PlatformIconModal',
  },
  {
    module: 'ReportAndBasicDataInputModal',
    alias: 'ReportAndBasicDataInputModal',
  },
  {
    module: 'ImgUpload',
    alias: 'ImgUpload',
  },
  {
    module: 'DropDownVocabulary',
    alias: 'DropDownVocabulary',
  },
  {
    module: 'ActionParams',
    alias: 'ActionParams',
  },
  {
    module: 'SubpageSelector',
    alias: 'SubpageSelector',
  },
  {
    module: 'PrintTemplateModal',
    alias: 'PrintTemplateModal',
  },
  {
    module: 'WorkflowSelector',
    alias: 'WorkflowSelector',
  },
  {
    module: 'OperatePermission',
    alias: 'OperatePermission'
  },
  {
    module: 'PermissionTab',
    alias: 'PermissionTab',
  }
] as const;

// athena-designer-core 分享的mf
export const athenaDesignerCoreConfig: RemoteModuleConfig = {
  remoteName: 'athena_designer_core',
  modules: athenaDesignerCoreConfigModules,
};
