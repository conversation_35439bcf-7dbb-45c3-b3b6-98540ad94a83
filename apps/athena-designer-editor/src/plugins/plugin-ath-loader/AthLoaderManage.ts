import EventEmitter from 'eventemitter3';
import {
  AthLoaderManageContext,
  AthLoaderManageEventHandler,
  AthLowCodeConfigKey,
  AthLowCodeEventName,
  DataSourceInfo,
  GobalData,
  LocalContext,
  MessageToMainContent,
  MessageToMainDataSourceInfo,
  MessageToMainGobal,
  MessageToMainOpenWindow,
  MessageToMainRule,
  MessageToMainType,
  MicroAppContext,
  PageUIElementContent,
  PanelType,
  SaveType,
  UpdatePageExtraData,
} from './type';
import {
  IPublicEnumTransformStage,
  IPublicModelDocumentModel,
  IPublicModelPluginContext,
  IPublicModelSkeletonItem,
} from '@alilc/lowcode-types';
import {
  getProjectPageUIElement,
  lcdpConverterManager,
  libConverters,
  loadPageUIElementToProject,
} from '@/tools/business/lcdp-converter';
import { getProjectSchema, getDefaultSchema } from '@/tools/business/mock';
import { debounce } from 'lodash';
import { handleComponentI18n, handlePageI18n } from '../plugin-ath-material-loader/tools';
import { componentTypeNotHideList } from './config';
import { AthComponentType } from '@/tools/business/lcdp-converter/type';
import { generateOutLet, handleComponentSnippets } from './tools';

enum ContainerComponents {
  BUTTON_GROUP = 'BUTTON_GROUP',
  TABS = 'TABS',
  COLLAPSE = 'COLLAPSE',
  LAYOUT = 'LAYOUT',
  FLEXIBLE_BOX = 'FLEXIBLE_BOX',
  ATHENA_TABLE = 'ATHENA_TABLE',
  FORM_LIST = 'FORM_LIST',
  GRIDSTER = 'GRIDSTER',
}

const isMicroAppContext = (context: AthLoaderManageContext): context is MicroAppContext => {
  return !!context.isMicroApp;
};

/**
 * 主要用来管理，转换，加载 ath 相关数据
 * 有 微前端模式（也可以理解为外部提供数据资源） 和 本地模式
 * 在lowcode 只会存在一份 实例，可以更改模式，会挂载到 lowcode上，可主动调用 public方法，但一般 不会这样操作，或者 识别从context中获取信息
 * AthLoaderManage 因为 挂载了 lowcode的 context，可以直接与 lowcode进行 交互
 * 一般来说：
 * 对内：AthLoaderManage 与 lowcode 的交互 会通过 lowcode的 自定义事件 系统，相关的事件名 可在 枚举 AthLowCodeEventName 中查看
 * 对外：AthLoaderManage 本身 继承自 EventEmitter，会对外暴露相关事件，接入方可在处理相关事件 对外部进行交互，当前对外 只有 microApp环境
 */
export class AthLoaderManage<
  Context extends AthLoaderManageContext,
> extends EventEmitter<AthLoaderManageEventHandler> {
  private _athLoaderManageContext: AthLoaderManageContext; // loader上下文
  private _lowCodeContext: IPublicModelPluginContext; // lowCode环境上下文

  constructor(athLoaderManageContext: Context, lowCodeContext: IPublicModelPluginContext) {
    super();
    this._athLoaderManageContext = athLoaderManageContext;
    this._lowCodeContext = lowCodeContext;
    this.initLowCodeEvent();
    this.init();
  }

  get lowcodeVersion() {
    if (!isMicroAppContext(this._athLoaderManageContext)) return '1.0';
    return this._athLoaderManageContext.dslWorkDesignData?.version ?? '1.0';
  }

  private parseDsl(layout: any[], operations: any[], isCustom: boolean) {
    if (!layout || layout.length === 0) return [];
    layout.forEach((item) => {
      const type = item.type;
      switch (true) {
        case type === ContainerComponents.BUTTON_GROUP: {
          this.parseDsl(item.group, operations, isCustom);
          break;
        }
        case /^BUTTON/.test(item.type): {
          operations.push({
            componentId: item.id,
            componentName: item.title,
            lang: item.lang?.title
              ? { componentName: item.lang?.title }
              : { componentName: { zh_CN: item.title, en_US: item.title, zh_TW: item.title } },
            authKey: item.authKey,
            isCustomize: isCustom,
            couldSetPrint: item.type === 'BUTTON_PRINT',
            templates: item.extendedFields?.templates ?? [],
          });
          break;
        }
        case this.isCustomComponent(type): {
          operations.push({
            componentId: item.id,
            componentName: item.headerName,
            lang: item.lang?.headerName
              ? { componentName: item.lang?.headerName }
              : {
                  componentName: {
                    zh_CN: item.headerName,
                    en_US: item.headerName,
                    zh_TW: item.headerName,
                  },
                },
            authKey: item.authKey,
            isCustomize: isCustom,
            couldSetPrint: false,
          });
          break;
        }
        case type === ContainerComponents.TABS: {
          this.parseDsl(item.tabs, operations, isCustom);
          break;
        }
        case type === ContainerComponents.COLLAPSE: {
          this.parseDsl(item.panels, operations, isCustom);
          break;
        }
        case type === ContainerComponents.LAYOUT: {
          this.parseDsl(item.header?.group, operations, isCustom);
          this.parseDsl(item.sider?.group, operations, isCustom);
          this.parseDsl(item.content?.group, operations, isCustom);
          this.parseDsl(item.footer?.group, operations, isCustom);
          break;
        }
        case type === 'TOOLBAR': {
          item.items?.forEach((info: any) => {
            operations.push({
              componentId: info.id,
              componentName: info.title,
              lang: info.lang?.title
                ? { componentName: info.lang?.title }
                : { componentName: { zh_CN: info.title, en_US: info.title, zh_TW: info.title } },
              authKey: info.authKey,
              isCustomize: isCustom,
              couldSetPrint: false,
            });
          });
          break;
        }
        case type === ContainerComponents.GRIDSTER:
        case type === ContainerComponents.FORM_LIST:
        case type === ContainerComponents.FLEXIBLE_BOX: {
          this.parseDsl(item.group, operations, isCustom);
          break;
        }
        case type === ContainerComponents.ATHENA_TABLE: {
          item.columnDefs?.map((def: any) => {
            this.parseDsl(def.columns, operations, isCustom);
          });
          item.slots?.map((slot: any) => {
            this.parseDsl(slot.group, operations, isCustom);
          });
          break;
        }
        default:
          break;
      }
    });
  }

  private isCustomComponent(type: string) {
    const { material, config } = this._lowCodeContext;
    const isvComponentList = config.get(AthLowCodeConfigKey.AthIsvComponentList) ?? [];
    return (
      !material.componentsMap[type] &&
      !isvComponentList.some((component: any) => component.type === type)
    );
  }

  private getOperationList(layout: any[] = [], operations: any[] = [], isCustom: boolean) {
    const operationList: any[] = [];
    this.parseDsl(layout, operationList, isCustom);
    operations
      ?.filter((operation) => !['openwindow', 'open-task-window'].includes(operation.operate))
      .forEach((operation) => {
        operationList.push({
          componentId: operation.id,
          componentName: operation.title,
          lang: operation.lang?.title
            ? { componentName: operation.lang?.title }
            : {
                componentName: {
                  zh_CN: operation.title,
                  en_US: operation.title,
                  zh_TW: operation.title,
                },
              },
          couldSetPrint: operation.type === 'print',
          templates: operation.extendedFields?.templates ?? [],
          authKey: operation.authKey,
          isCustomize: isCustom,
        });
      });
    return operationList;
  }

  private rebuildIamCondition(info: {
    layout: any[];
    operations: any[];
    pageCode: string;
    platform: string;
    iamCondition: any[];
  }) {
    const { layout = [], operations = [], pageCode, platform, iamCondition = [] } = info ?? {};
    const operateLists = this.getOperationList(layout, operations, false) ?? [];
    if (!operateLists || operateLists.length === 0) return iamCondition;
    const correctionIamCondition = iamCondition.map((condition) => {
      const authKey = condition.key;
      const refComponents = condition.refComponents ?? [];
      // console.log(
      //   '%c 🚀 [Neovim AutoGR Log]: path = src/plugins/plugin-ath-loader/AthLoaderManage.ts, scope = AthLoaderManage.rebuildIamCondition.correctionIamCondition.refComponents, refComponents = ',
      //   'color: orangered; font-weight: bold;',
      //   refComponents,
      // );
      const restComponents = refComponents.filter(
        (comp: any) =>
          !(comp.pageCode === pageCode && comp.platform === platform) || comp.isCustomize,
      );
      // console.log(
      //   '%c 🚀 [Neovim AutoGR Log]: path = src/plugins/plugin-ath-loader/AthLoaderManage.ts, scope = AthLoaderManage.rebuildIamCondition.correctionIamCondition.restComponents, restComponents = ',
      //   'color: orangered; font-weight: bold;',
      //   restComponents,
      // );
      const relateOperation = operateLists
        .filter((operate) => operate.authKey === authKey)
        .map((operation) => {
          return {
            componentId: operation.componentId,
            componentName: operation.componentName,
            lang: operation.lang?.componentName
              ? { componentName: operation.lang?.componentName }
              : {
                  componentName: {
                    zh_CN: operation.componentName,
                    en_US: operation.componentName,
                    zh_TW: operation.componentName,
                  },
                },
            pageCode,
            platform,
            templates: operation.templates ?? [],
            authKey: operation.authKey,
            isCustomize: false,
          };
        });
      return {
        ...condition,
        refComponents: [...restComponents, ...relateOperation],
      };
    });
    console.log(
      '%c 🚀 [Neovim AutoGR Log]: path = src/plugins/plugin-ath-loader/AthLoaderManage.ts, scope = AthLoaderManage.rebuildIamCondition, correctionIamCondition = ',
      'color: orangered; font-weight: bold;',
      correctionIamCondition,
    );
    return correctionIamCondition;
  }

  private getGroupSchemaList(): string[] {
    const { project } = this._lowCodeContext;
    const nodeList = [...(project?.currentDocument?.nodesMap?.values() ?? [])];

    return nodeList
      .filter(
        (node) => node.componentName === 'DATA_QUERY' && node.getPropValue('dslInfo.showAccount'),
      )
      .map((node) => {
        return node.children?.map((child) => child.getPropValue('dslInfo.schema'));
      })
      .flat()
      .filter(Boolean);
  }

  /**
   *  project schema 变化时的处理
   */
  public handleProjectSchemaChange = debounce(() => {
    const { config, event, project } = this._lowCodeContext;
    this.updateSimulatorHostDom();

    // TODO
    const pageUIElementContent =
      this.lowcodeVersion === '1.0'
        ? getProjectPageUIElement(
            generateOutLet(project.exportSchema(IPublicEnumTransformStage.Save)),
          )
        : getProjectPageUIElement();

    const layout = pageUIElementContent?.layout ?? [];
    const operations = pageUIElementContent?.operations ?? [];
    const extraData = config.get(AthLowCodeConfigKey.AthExtraData);
    const dynamicInfo = config.get(AthLowCodeConfigKey.AthDynamicWorkDesignInfo);
    const pageCode = dynamicInfo?.pageCode;
    const platform = 'PC';
    /**
     * TODO: 更新权限数据 return iamCondition
     */
    const iamCondition = extraData?.iamCondition ?? [];
    const newIamConditions = this.rebuildIamCondition({
      layout,
      operations,
      pageCode,
      platform,
      iamCondition,
    });
    event.emit(AthLowCodeEventName.LowCodeExtraDataUpdate, {
      ...(extraData ?? {}),
      iamCondition: newIamConditions,
    });

    this.handleMessageToOuter({
      type: MessageToMainType.PageUIElementContent,
      data: {
        type: 'update',
        pageUIElementContent,
        iamCondition: newIamConditions,
        groupSchemaList: this.getGroupSchemaList(),
      },
    });
  }, 300);

  public handleUpdatePanelState = debounce((panelName: string, isShow: boolean) => {
    this.handleMessageToOuter({
      type: MessageToMainType.Gobal,
      data: {
        type: 'updatePanelState',
        data: { panelName, isShow },
      } as GobalData,
    });
  }, 100);

  public handleUpdatePluginStatus = (pluginName: string, status: string) => {
    console.log('handleUpdatePluginStatus, pluginName = ', pluginName, '; status = ', status);
    this.emit('onHandleUpdatePluginStatus', {
      type: MessageToMainType.Gobal,
      data: {
        type: 'updatePluginStatus',
        data: { pluginName, status },
      } as GobalData,
    });
  };

  /**
   * 更新画布渲染的dom
   * TODO 对于 simulatorHostDom 样式的处理 可以寻找 更好的api，或者更合理的逻辑
   */
  private updateSimulatorHostDom = () => {
    const { project } = this._lowCodeContext;
    const contentDocument = project?.simulatorHost?.contentDocument;
    const simulatorHostAppLcePageDom = contentDocument?.querySelector(
      '#app .lce-page',
    ) as HTMLElement;
    if (simulatorHostAppLcePageDom) {
      simulatorHostAppLcePageDom.style.overflow = 'auto';
      simulatorHostAppLcePageDom.style.position = 'relative';
      simulatorHostAppLcePageDom.style.minHeight = '100vh';
    }
  };

  /**
   * 根据isCustomize（是否自定义），更新界面设计器状态
   */
  private updateDesignerByIsCustomize = () => {
    const { config, project, material } = this._lowCodeContext;
    const { dynamicWorkDesignConfig } = config.get(AthLowCodeConfigKey.AthDynamicWorkDesignInfo);
    const { isCustomize = false, customTip } = dynamicWorkDesignConfig.commonConfig;
    if (isCustomize) {
      // 隐藏画布中渲染的非操作和flex相关的组件
      project?.currentDocument &&
        project?.currentDocument?.nodesMap.forEach((node) => {
          if (!componentTypeNotHideList.includes(node.componentName)) {
            node.visible = false;
          }
        });
    }

    // 画布增加 自定义样式背景
    // 可以看看有没有更合适的 lowcode api
    // const iframe = document.querySelector('iframe');
    // const iframeDocument = iframe?.contentDocument || iframe?.contentWindow?.document;
    // const app = iframeDocument?.querySelector('#app');
    const contentDocument = project?.simulatorHost?.contentDocument;
    const simulatorHostAppDom = contentDocument?.querySelector('#app') as HTMLElement;
    simulatorHostAppDom?.classList[isCustomize ? 'add' : 'remove']('isCustomize');
    simulatorHostAppDom?.querySelector('#customTip')?.remove();
    const customTipDom = contentDocument?.createElement('span');
    if (isCustomize && simulatorHostAppDom && customTipDom) {
      customTipDom.id = 'customTip';
      customTipDom.textContent = customTip;
      simulatorHostAppDom.appendChild(customTipDom);
    }

    this.updateComponentPanelByIsCustomize();
  };

  // 根据isCustomize（是否自定义），更新组件库的状态
  private updateComponentPanelByIsCustomize() {
    const { config } = this._lowCodeContext;
    const { dynamicWorkDesignConfig } = config.get(AthLowCodeConfigKey.AthDynamicWorkDesignInfo);
    const { isCustomize = false } = dynamicWorkDesignConfig.commonConfig;

    // TODO 以下为临时逻辑，之后将会 改造组件库 使用 config 去配置
    const elements = document.querySelectorAll('[class*="snippet Component"]');
    elements.forEach((element: any) => {
      if (!isCustomize) {
        element.style.display = 'block';
        return;
      }

      if (
        ![
          '按钮组',
          '功能按钮',
          '提交按钮',
          '按鈕組',
          '功能按鈕',
          '提交按鈕',
          'Button group',
          'Function button',
          'Submit button',
          '弹性布局组件',
          '彈性布局元件',
          'Flex layout component',
        ].includes(element.getAttribute('title') ?? '')
      ) {
        element.style.display = 'none';
      }
    });
  }

  /**
   *  初始化lowCode相关的事件逻辑
   */
  private initLowCodeEvent() {
    const { project, skeleton, event } = this._lowCodeContext;
    project.onChangeDocument((document: IPublicModelDocumentModel) => {
      this.handleProjectSchemaChange();
      document.onAddNode((node) => {
        this.handleProjectSchemaChange();
      });
      document.onRemoveNode((node) => {
        this.handleProjectSchemaChange();
      });
      document.onChangeNodeProp((node) => {
        this.handleProjectSchemaChange();
      });
      document.onChangeNodeChildren((node) => {
        this.handleProjectSchemaChange();
      });
    });

    skeleton.onShowPanel((panelName?: string, panel?: IPublicModelSkeletonItem) => {
      if (panelName) this.handleUpdatePanelState(panelName, true);
    });

    skeleton.onHidePanel((panelName?: string, panel?: IPublicModelSkeletonItem) => {
      if (panelName) this.handleUpdatePanelState(panelName, false);
    });

    // lowcode 对规则的 操作
    event.on(
      `common:${AthLowCodeEventName.AthRulesHandle}`,
      (messageToMainRule: MessageToMainRule) => {
        this.handleMessageToOuter(messageToMainRule);
      },
    );

    // lowcode DataSourceInfo 操作
    event.on(
      `common:${AthLowCodeEventName.AthDataSourceInfoHandle}`,
      (messageToMainDataSourceInfo: MessageToMainDataSourceInfo) => {
        this.handleMessageToOuter(messageToMainDataSourceInfo);
      },
    );

    // lowcode isSideBarRivet 操作
    event.on(`common:${AthLowCodeEventName.AthIsSideBarRivetHandle}`, (isSideBarRivet: boolean) => {
      this.handleMessageToOuter({
        type: MessageToMainType.Gobal,
        data: {
          type: 'updateIsSideBarRivet',
          data: {
            isSideBarRivet,
          },
        } as GobalData,
      } as MessageToMainGobal);
    });

    // 开窗的操作
    event.on(
      `common:${AthLowCodeEventName.AthOpenWindowHandle}`,
      (messageToMainOpenWindow: MessageToMainOpenWindow) => {
        this.handleMessageToOuter(messageToMainOpenWindow);
      },
    );
  }

  /**
   *  微前端环境初始化
   */
  private async initMicroApp(context: MicroAppContext) {
    console.log('微前端环境，加载外部数据');
    const { config, skeleton, material } = this._lowCodeContext;

    // TODO 可能有更好的方式
    document.getElementById('lce-container')?.classList.add('is-micro-app');

    skeleton.hideArea('leftArea');
    skeleton.hideArea('topArea');

    // 初始化版本
    config.set(AthLowCodeConfigKey.AthVersion, context.dslWorkDesignData?.version);


    // TODO 处理转换器逻辑
    // if (context.dslWorkDesignData?.version === '2.0') {
    // 加载组件库特殊的转换器，如果key相同则会覆盖
    libConverters.forEach((converter) => {
      lcdpConverterManager.setConverter(converter.key, converter);
    });

    // }

    // 更新数据源仅在1.0版本中
    if (context.dslWorkDesignData?.version === '1.0') {
      this.updateMicroAppContextDslWorkDesignDataFieldTreeMap({
        fieldTreeMap: context.dslWorkDesignData?.fieldTreeMap,
      });
      this.updateMicroAppContextDslWorkDesignDataSourceInfo(
        context.dslWorkDesignData?.dataSourceInfo ?? {
          dataSourceName: '',
          dataSourceNames: [],
          dataSources: {},
        },
      );
    }
    // 2.0中处理单独fieldTreeMap
    if (context.dslWorkDesignData?.version === '2.0') {
      const fieldTreeMap = context.dslWorkDesignData?.fieldTreeMap;
      if (fieldTreeMap && fieldTreeMap.size > 0) {
        config.set(AthLowCodeConfigKey.AthFieldTreeMap, fieldTreeMap);
      }
    }

    this.updateMicroAppContextDslWorkDesignDataRules(context.dslWorkDesignData?.rules ?? []);
    // 如果有在初始化之后更新的 需求，就会抽成一个update方法，这时就无需在这里set，仅需调用update方法
    // TODO 当然，如果结构稳定之后，这些update方法可以抽象一层
    config.set(AthLowCodeConfigKey.AthStatusInfo, context.dslWorkDesignData?.statusInfo ?? {});
    config.set(AthLowCodeConfigKey.AthSystemConfig, context.dslWorkDesignData?.systemConfig ?? {});

    this.updateMicroAppContextDslWorkDesignDataExtraData(
      context.dslWorkDesignData?.extraData ?? {},
    );

    this.updateMicroAppContextDslWorkDesignDataHideComponent(
      context.dslWorkDesignData?.hideComponent ?? [],
    );

    config.set(
      AthLowCodeConfigKey.AthDynamicWorkDesignInfo,
      context.dslWorkDesignData?.dynamicWorkDesignInfo ?? {},
    );

    const isvComponentList = context.dslWorkDesignData?.isvComponentList ?? [];
    isvComponentList.forEach(handleComponentI18n);
    config.set(AthLowCodeConfigKey.AthIsvComponentList, isvComponentList);

    material.loadIncrementalAssets({
      version: '0.1.6',
      components: isvComponentList.filter((item) => !material.componentsMap[item.componentName]),
    });

    // 处理部分组件默认值
    handleComponentSnippets(material, config, context.dslWorkDesignData?.version);

    const defaultSchema = getDefaultSchema();
    handlePageI18n(defaultSchema.componentsTree?.[0]);
    loadPageUIElementToProject(
      context.dslWorkDesignData.pageUIElementContent,
      defaultSchema,
      true,
      [AthComponentType.OUTLET],
    );
  }

  /**
   *  本地环境初始化
   *  TODO 在本地化环境中，规则，数据源，字段树 相关逻辑将被隐藏
   */
  private async initLocal(context: LocalContext) {
    console.log('非微前端环境，加载本地数据');
    const { material, project, config, skeleton, plugins } = this._lowCodeContext;
    config.set(AthLowCodeConfigKey.AthFieldTree, []);
    config.set(AthLowCodeConfigKey.AthRules, []);
    const scenarioName = config.get('scenarioName');
    const schema = await getProjectSchema(scenarioName);
    schema.meta = {
      projectName: 'athena-designer-editor',
    };
    project.importSchema(schema);
  }

  // ========================= 对外的方法（通用） =========================
  /**
   *  初始化 AthLoaderManage, 也可以作为根据当前上下文reset low code 的方法
   */
  public init() {
    if (isMicroAppContext(this._athLoaderManageContext)) {
      this.initMicroApp(this._athLoaderManageContext);
    } else {
      this.initLocal(this._athLoaderManageContext);
    }
  }

  /**
   * 向外部发送消息（microApp微前端环境 模式 和 本地模式有区别）
   * @param data MessageToMainContent
   */
  public handleMessageToOuter(data: MessageToMainContent) {
    // microApp 环境
    if (this._athLoaderManageContext.isMicroApp) {
      window.microApp.clearData();
      window.microApp.dispatch(data);
      return;
    }
    // 本地环境
    this.emit('onHandleMessageToOuter', data);
  }

  /**
   *  获取当前project的pageUIElement
   */
  public getProjectPageUIElement(): PageUIElementContent {
    return getProjectPageUIElement();
  }

  /**
   * 更新context
   * @param context
   * @param isNeedInit 是否需要重新init
   */
  public updateAthLoaderManageContext(context: Context, isNeedInit = false) {
    this._athLoaderManageContext = context;
    if (isNeedInit) {
      this.init();
    }
  }

  /**
   * 操作左侧面板
   * @param isShow 是否打开
   * @param panel 面板名称
   */
  public togglePanel(isShow: boolean, panel: string = '', isSideBarRivet: boolean = true) {
    const { skeleton, config, event } = this._lowCodeContext;
    Object.values(PanelType).forEach((panelName: string) => {
      if (isShow && panel === panelName) {
        skeleton.showPanel(panelName);
        event.emit(AthLowCodeEventName.LowCodeIsSideBarRivetUpdate, isSideBarRivet);
      } else {
        skeleton?.hidePanel(panelName);
      }
    });
  }

  // ========================= 对外的方法（仅微前端环境） =========================
  /**
   * （仅MicroAppContext）微前端环境 更新 PageUIElementContent
   * @param pageUIElementContent
   * @param extraData 额外信息
   */
  public updateMicroAppContextDslWorkDesignDataPageUIElement(
    pageUIElementContent: Partial<PageUIElementContent>,
    extraData: UpdatePageExtraData,
  ): void {
    const { isLoadImmediately = true, saveKey, dataKey, saveType } = extraData;
    const { project, material } = this._lowCodeContext;
    if (!isMicroAppContext(this._athLoaderManageContext)) return;
    const resultData = {
      ...this._athLoaderManageContext.dslWorkDesignData.pageUIElementContent,
      ...pageUIElementContent,
    };
    this._athLoaderManageContext.dslWorkDesignData.pageUIElementContent = resultData;
    if (isLoadImmediately) {
      loadPageUIElementToProject(resultData);
    } else if (saveType === SaveType.Root) {
      project?.currentDocument?.root?.setPropValue(saveKey, pageUIElementContent[dataKey]);
      const selection = project?.currentDocument?.selection;
      const selectIds = selection.selected;
      if (selectIds?.length > 0) {
        project?.currentDocument?.root?.select();
        // selection.remove(selectIds[0]);
        selection.select(selectIds[0]);
      }
    }
  }

  /**
   * （仅MicroAppContext）微前端环境 更新 rules
   * @param rules any[]
   * @param isLoadImmediately 是否需要立即刷新
   */
  public updateMicroAppContextDslWorkDesignDataRules(rules: any[], isLoadImmediately = true) {
    const { event } = this._lowCodeContext;
    if (!isMicroAppContext(this._athLoaderManageContext)) return;
    const { config, project } = this._lowCodeContext;
    this._athLoaderManageContext.dslWorkDesignData.rules = rules;
    config.set(AthLowCodeConfigKey.AthRules, rules);
    if (isLoadImmediately) {
      event.emit(AthLowCodeEventName.LowCodeRulesUpdate, rules);
    }
  }

  public updateMicroAppContextDslWorkDesignDataFieldTree(
    dataSourceName: string,
    fieldTreeMap: Map<string, any>,
    isLoadImmediately = true,
  ) {
    if (!isMicroAppContext(this._athLoaderManageContext)) return;
    const { config, event } = this._lowCodeContext;
    const version = config.get(AthLowCodeConfigKey.AthVersion);
    if (version === '2.0') return;
    /**
     * 这边是这样的，dataSourceName和fieldTreeMap的更新并不同步，所以这边的条件如下：
     * 1. 如果dataSourceName和fieldTreeMap存在空有两种场景：
     *    1. 就是没有数据源 - 没有数据源两个都为空
     *    2. 删了最后一个数据源 - 那么其中一个为先为空
     *  -- 所以这两种场景都应该直接清空fieldTree
     * 2. dataSourceName在fieldTreeMap中找不到字段树, 这边也有两种场景
     *    1. 修改了数据源名称 - 那么dataSourceName先变，然后fieldTreeMap监听到dataSources变更会更新fieldTreeMap, 更新完成之后才能配上
     *    2. 删除了当前数据源 - 那么dataSources变更，会触发dataSourceName和fieldTreeMap都变更，但是更新并不同步，两个字段都更新完成才能匹配上
     *  -- 所以这个场景的现象就是下面代码中用dataSourceName从fieldTreeMap中拿tree的时候拿不到，这种场景可以不实时去更新fieldTree, 等后面两个字段都更新好再更新,避免画布拿到空字段树出现报错
     */
    if (dataSourceName && fieldTreeMap && fieldTreeMap.size > 0) {
      if (fieldTreeMap && fieldTreeMap.size > 0) {
        const fieldTree = fieldTreeMap.get(dataSourceName);
        if (!fieldTree) return;
        config.set(AthLowCodeConfigKey.AthFieldTree, fieldTree);
        this._athLoaderManageContext.dslWorkDesignData.fieldTree = fieldTree;
        if (isLoadImmediately) {
          event.emit(AthLowCodeEventName.LowCodeFieldTreeUpdate, fieldTree);
        }
      }
    } else {
      config.set(AthLowCodeConfigKey.AthFieldTree, []);
      this._athLoaderManageContext.dslWorkDesignData.fieldTree = [];
      if (isLoadImmediately) {
        event.emit(AthLowCodeEventName.LowCodeFieldTreeUpdate, []);
      }
    }
  }

  /**
   * （仅MicroAppContext）微前端环境 更新 fieldTree
   * @param fieldTreeMap Map<string, any>
   * @param isLoadImmediately 是否需要立即刷新
   */
  public updateMicroAppContextDslWorkDesignDataFieldTreeMap({
    fieldTreeMap,
    dataSourceName,
    isLoadImmediately = true,
  }: {
    fieldTreeMap?: Map<string, any>;
    dataSourceName?: string;
    isLoadImmediately?: boolean;
  }) {
    if (!isMicroAppContext(this._athLoaderManageContext)) return;
    const { config, event } = this._lowCodeContext;
    /**
     * fieldTreeMap变更
     */
    if (fieldTreeMap && fieldTreeMap.size > 0) {
      config.set(AthLowCodeConfigKey.AthFieldTreeMap, fieldTreeMap);
      this._athLoaderManageContext.dslWorkDesignData.fieldTreeMap = fieldTreeMap;
      if (isLoadImmediately) {
        event.emit(AthLowCodeEventName.LowCodeFieldTreeMapUpdate, fieldTreeMap);
      }
      const { dataSourceName } = config.get(AthLowCodeConfigKey.AthDataSourceInfo) ?? {};
      this.updateMicroAppContextDslWorkDesignDataFieldTree(
        dataSourceName,
        fieldTreeMap,
        isLoadImmediately,
      );
    }
    /**
     * dataSourceName变更
     */
    if (dataSourceName) {
      const fieldTreeMap = config.get(AthLowCodeConfigKey.AthFieldTreeMap);
      this.updateMicroAppContextDslWorkDesignDataFieldTree(
        dataSourceName,
        fieldTreeMap,
        isLoadImmediately,
      );
    }
  }

  /**
   * （仅MicroAppContext）微前端环境 更新 dataSourceNames
   * @param dataSourceInfo DataSourceInfo
   * @param isLoadImmediately 是否需要立即刷新
   */
  public updateMicroAppContextDslWorkDesignDataSourceInfo(
    dataSourceInfo: DataSourceInfo,
    isLoadImmediately = true,
  ) {
    if (!isMicroAppContext(this._athLoaderManageContext)) return;
    const { config, event } = this._lowCodeContext;
    const oldDataSourceInfo = config.get(AthLowCodeConfigKey.AthDataSourceInfo);
    const oldDataSourceName = oldDataSourceInfo?.dataSourceName;
    this._athLoaderManageContext.dslWorkDesignData.dataSourceInfo = dataSourceInfo;
    config.set(AthLowCodeConfigKey.AthDataSourceInfo, dataSourceInfo);
    if (isLoadImmediately) {
      event.emit(AthLowCodeEventName.LowCodeDataSourceInfoUpdate, dataSourceInfo);
    }
    if (oldDataSourceName !== dataSourceInfo?.dataSourceName) {
      this.updateMicroAppContextDslWorkDesignDataFieldTreeMap({
        dataSourceName: dataSourceInfo?.dataSourceName,
      });
    }
  }

  /**
   * （仅MicroAppContext）微前端环境 更新 extraData
   * @param extraData any
   * @param isLoadImmediately 是否需要立即刷新
   */
  public updateMicroAppContextDslWorkDesignDataExtraData(extraData: any, isLoadImmediately = true) {
    if (!isMicroAppContext(this._athLoaderManageContext)) return;
    const { config, event } = this._lowCodeContext;
    this._athLoaderManageContext.dslWorkDesignData.extraData = extraData;
    config.set(AthLowCodeConfigKey.AthExtraData, extraData);
    if (isLoadImmediately) {
      event.emit(AthLowCodeEventName.LowCodeExtraDataUpdate, extraData);
    }
  }

  /**
   * （仅MicroAppContext）微前端环境 更新 hideComponent
   * @param hideComponent 隐藏的组件列表
   * @param isLoadImmediately 是否需要立即刷新
   * @returns
   */
  public updateMicroAppContextDslWorkDesignDataHideComponent(
    hideComponent: any,
    isLoadImmediately = true,
  ) {
    if (!isMicroAppContext(this._athLoaderManageContext)) return;
    const { config, event } = this._lowCodeContext;
    this._athLoaderManageContext.dslWorkDesignData.hideComponent = hideComponent;
    config.set(AthLowCodeConfigKey.AthHideComponent, hideComponent);
    if (isLoadImmediately) {
      event.emit(AthLowCodeEventName.LowCodeHideComponentUpdate, hideComponent);
    }
  }
}
