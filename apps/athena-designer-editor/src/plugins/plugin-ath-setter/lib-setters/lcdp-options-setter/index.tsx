import React, { useState } from 'react';
import { Popconfirm, Select } from 'antd';
import { DraggableList } from '../../components/DraggableList';
import { OptionModal } from './OptionModal';
import { useTranslation } from 'react-i18next';

import './index.scss';
import i18n, {t} from 'i18next';
import type { ILcdpOptionsSetterProps, ILcdpOptionsItemData } from './types';
import type { IDraggableConfig } from '../../components/DraggableList/types';
import Icon from '@/components/Icon';

function LcdpLibOptionsSetter(props: ILcdpOptionsSetterProps) {
  const { field, value: optionsValue, onChange, options } = props;
  const { componentProps = {} } = options;
  const { dataConnectorId, options: value } = optionsValue;
  const { t } = useTranslation();
  const [opData, setOpData] = useState<{
    visible: boolean;
    data: ILcdpOptionsItemData | null;
    index: number | null;
  }>({ visible: false, data: null, index: null });

  if (componentProps.placeholder) {
    componentProps.placeholder = t(componentProps.placeholder);
  }

  const doMove = (datas: ILcdpOptionsItemData[]) => {
    onChange({
      dataConnectorId: '',
      options: datas,
    });
  };

  const doEdit = (data: ILcdpOptionsItemData, index: number) => {
    setOpData({
      visible: true,
      data,
      index,
    });
  };

  const doDelete = (dIndex: number) => {
    onChange({
      dataConnectorId: '',
      options: value?.filter((_, index) => dIndex !== index),
    });
  };

  const configFragment: IDraggableConfig<ILcdpOptionsItemData> = {
    label: (data) => data.lang?.title?.[i18n?.language || 'zh_CN'] || data?.title,
    operations: [
      {
        custom: (data, index) => {
          return (
            // <FormOutlined
            //   onClick={() => {
            //     doEdit(data, index);
            //   }}
            // />
            <Icon
              className="iconfont button-move"
              type="iconbianji1"
              onClick={() => {
                doEdit(data, index);
              }}
            />
          );
        },
      },
      {
        custom: (data, index) => {
          return (
            <Popconfirm
              className="confirm-delete"
              title={t('dj-确认删除吗？')}
              onConfirm={() => {
                doDelete(index);
              }}
              onCancel={() => {}}
              okText={t('dj-删除')}
              cancelText={t('dj-取消')}
            >
              {/* <DeleteOutlined /> */}
              <Icon className="iconfont button-move" type="iconshanchu3" />
            </Popconfirm>
          );
        },
      },
    ],
  };

  const doAdd = () => {
    setOpData({
      visible: true,
      data: null,
      index: null,
    });
  };

  const doCancel = () => {
    setOpData({
      visible: false,
      data: null,
      index: null,
    });
  };

  const doChange = (newData: ILcdpOptionsItemData) => {
    if (Number.isInteger(opData.index)) {
      onChange({
        dataConnectorId: '',
        options: value?.map((data, index) => {
          if (index === opData.index) return newData;
          return data;
        }),
      });
    } else {
      onChange({
        dataConnectorId: '',
        options: [...value, newData],
      });
    }
    doCancel();
  };

  const handleChange = (dataConnectorId: string) => {
    onChange({
      dataConnectorId,
      options: [],
    });
  };

  const { useCoreData: { dataConnectors = [] } } = field?.node?.document?.root?.propsData as any;
  return (
    <div className="options-draggable-list-setter">
      <div className="setter-title">
        <span>{t('dj-配置选项')}</span>
        <span onClick={doAdd}>+&nbsp;{t('dj-添加')}</span>
      </div>

      <Select
        {...componentProps}
        className="ath-common-select"
        value={dataConnectorId}
        options={(dataConnectors || []).map((d) => ({ label: d.name, value: d.name }))}
        onChange={handleChange}
      />

      <div className="setter-content">
        <DraggableList<ILcdpOptionsItemData>
          datas={value}
          onMove={doMove}
          config={configFragment}
        />
      </div>
      <OptionModal
        visible={opData.visible}
        data={opData.data}
        onChange={doChange}
        onCancel={doCancel}
      />
    </div>
  );
}

LcdpLibOptionsSetter.displayName = 'LcdpLibOptionsSetter';
export default LcdpLibOptionsSetter;
