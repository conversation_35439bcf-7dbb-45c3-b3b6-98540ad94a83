import React, { useMemo } from 'react';
import { IPublicModelSettingField } from '@alilc/lowcode-types';
import './index.scss';
import { FormOutlined, DeleteOutlined } from '@ant-design/icons';
import {
  MessageToMainType,
  AthLowCodeEventName,
} from '../../../plugin-ath-loader/type';
import { event } from '@alilc/lowcode-engine';
import i18n, { t } from 'i18next';
import { DslData } from '@/tools/business/lcdp-converter/type';
import { getDataConnectorId } from '@/tools/utils/setter';

export interface Rule {
  key: string; // 规则的唯一值
  content: RuleContent; // 规则内容
  [propName: string]: any;
}

export interface RuleContent {
  key: string; // 规则类型
  [propName: string]: any;
}

export interface LcdpLibRulesSetterProps {
  field: IPublicModelSettingField;
  value: DslData;
}

const LcdpLibRulesSetter: React.FC<LcdpLibRulesSetterProps> = (props: LcdpLibRulesSetterProps) => {
  const { field, value } = props;
  const { useCoreData = {} } = field?.node?.document?.root?.propsData as any;
  const { rules: configRules = [] } = useCoreData;
  const rules = useMemo(() => {
    const { path: nodePath, schema: nodeSchema } = value;
    if (!nodePath && !nodeSchema) return [];
    return configRules?.filter((rule: Rule) => {
      return rule?.content?.path === nodePath && rule?.content?.schema === nodeSchema;
    });
  }, [value, configRules]);

  const handleAdd = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    const targetElement = e.target as HTMLElement;
    const rect = targetElement.getBoundingClientRect();
    const ruleLeft = rect.left - 320 + rect.width + 10;
    const ruleTop = Math.min(rect.top, window.innerHeight - 470);
    const { path = '', schema = '' } = field.node?.getPropValue('dslInfo');
    event.emit(`${AthLowCodeEventName.AthRulesHandle}`, {
      type: MessageToMainType.Rules,
      data: {
        type: 'add',
        contextDataSourceName: getDataConnectorId(field),
        data: {
          addSelectPosition: {
            left: ruleLeft,
            top: ruleTop,
          },
          addRuleBaseInfo: {
            content: {
              path,
              schema,
            },
          },
        },
      },
    });
  };

  const handleEdit = (rule: Rule) => {
    event.emit(`${AthLowCodeEventName.AthRulesHandle}`, {
      type: MessageToMainType.Rules,
      data: { type: 'edit', contextDataSourceName: getDataConnectorId(field), data: rule },
    });
  };

  const handleDelete = (rule: Rule) => {
    event.emit(`${AthLowCodeEventName.AthRulesHandle}`, {
      type: MessageToMainType.Rules,
      data: { type: 'delete', data: rule },
    });
  };

  const getShowTitle = (rule: Rule) => {
    return (
      rule?.content?.lang?.name?.[i18n.language] ??
      rule.name ??
      rule?.content?.name ??
      rule.key ??
      ''
    );
  };

  return (
    <div className="lcdp-lib-rules-setter">
      <div className="rule-title">
        <span>{t('dj-规则列表')}</span>
        <span onClick={handleAdd}>+&nbsp;{t('dj-添加')}</span>
      </div>
      <div className="rules-list">
        {rules.map((rule, index) => {
          return (
            <div className="rules-item">
              <span className="rules-item-title">{getShowTitle(rule)}</span>
              <span className="rules-item-button">
                <FormOutlined
                  onClick={() => {
                    handleEdit(rule);
                  }}
                />
                <DeleteOutlined
                  onClick={() => {
                    handleDelete(rule);
                  }}
                />
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default LcdpLibRulesSetter;
