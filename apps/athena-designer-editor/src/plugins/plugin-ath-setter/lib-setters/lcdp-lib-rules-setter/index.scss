.lcdp-lib-rules-setter {
  .rule-title {
    line-height: 28px;
    height: 28px;
    font-weight: 700;
    width: 100%;

    span {
      float: left;
      color: #333333;
      font-size: 13px;
      font-weight: Medium;
    }

    span:nth-of-type(2) {
      color: #6a4cff;
      margin-left: 13px;
      cursor: pointer;
    }
  }

  .rules-list {
    .rules-item {
      padding: 5px 10px;
      margin-bottom: 8px;
      color: #333;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      font-size: 12px;
      border-radius: 4px;
      background: #f1f2fb;
      width: 100%;
      .rules-item-title {
        float: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 82%;
      }
      .rules-item-button {
        float: right;
        span {
          font-size: 13px;
          color: #6a4cff;
          cursor: pointer;
          margin-left: 4px;
        }
      }
    }
  }
}
