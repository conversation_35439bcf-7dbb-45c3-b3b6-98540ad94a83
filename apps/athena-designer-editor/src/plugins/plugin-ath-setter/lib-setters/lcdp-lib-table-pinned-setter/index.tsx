import React, { useEffect, useMemo, useState } from 'react';
import { IPublicModelSettingField } from '@alilc/lowcode-types';
import { AthComponentType, DslData } from '@/tools/business/lcdp-converter/type';
import i18n, { t } from 'i18next';
import theme from '@/config/theme.json';
import { ConfigProvider, Segmented, Tooltip } from 'antd';
import zh_CN from 'antd/es/locale/zh_CN';
import en_US from 'antd/es/locale/en_US';
import zh_TW from 'antd/es/locale/zh_TW';
import { VerticalRightOutlined, VerticalLeftOutlined, DashOutlined } from '@ant-design/icons';
import './index.scss';

const configProviderLocale = { zh_CN, en_US, zh_TW }?.[i18n?.language] ?? zh_CN;
interface LcdpTablePinnedSetterProps {
  value: any;
  field: IPublicModelSettingField;
  onChange: (value: any) => void;
}

const LcdpLibTablePinnedSetter: React.FC<LcdpTablePinnedSetterProps> = (
  props: LcdpTablePinnedSetterProps,
) => {
  const { value, onChange, field } = props;

  const handlePinnedChange = (val: string | null) => {
    onChange(val);
  };

  return (
    <ConfigProvider theme={theme} locale={configProviderLocale}>
      <div className="lcdp-table-pinned">
        <div className="title">{t('dj-冻结列')}</div>
        <Segmented
          className="pinned-select"
          options={[
            {
              value: 'left',
              icon: (
                <Tooltip title={t('dj-左冻结')}>
                  <VerticalRightOutlined />
                </Tooltip>
              ),
            },
            { value: null, icon: <DashOutlined /> },
            {
              value: 'right',
              icon: (
                <Tooltip title={t('dj-右冻结')}>
                  <VerticalLeftOutlined />
                </Tooltip>
              ),
            },
          ]}
          value={value ? value : null}
          onChange={handlePinnedChange}
        />
      </div>
    </ConfigProvider>
  );
};

export default LcdpLibTablePinnedSetter;
