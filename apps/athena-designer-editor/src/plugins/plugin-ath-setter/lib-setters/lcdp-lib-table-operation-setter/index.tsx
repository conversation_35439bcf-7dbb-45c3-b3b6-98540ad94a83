import React, { useEffect, useMemo, useState } from 'react';
import { IPublicModelSettingField } from '@alilc/lowcode-types';
import './index.scss';
import TableOperationListItem from './table-operation-list-item';
import { AthComponentType, DslData } from '@/tools/business/lcdp-converter/type';
import { project } from '@alilc/lowcode-engine';
import i18n, { t } from 'i18next';
import theme from '@/config/theme.json';
import { ConfigProvider, Dropdown } from 'antd';
import zh_CN from 'antd/es/locale/zh_CN';
import en_US from 'antd/es/locale/en_US';
import zh_TW from 'antd/es/locale/zh_TW';
import {
  ButtonAttachMode,
  ButtonSizeType,
  ButtonStyleMode,
  ButtonType,
} from '../../components/Button/enum';
import { getDecoupleBtnDefaultValueByType } from '../../components/Button/tools';
import { getOperationByRow } from './utils';

const configProviderLocale = { zh_CN, en_US, zh_TW }?.[i18n?.language] ?? zh_CN;
interface LcdpTableOperationSetterProps {
  value: DslData;
  field: IPublicModelSettingField;
  onChange: () => void;
}

const LcdpLibTableOperationSetter: React.FC<LcdpTableOperationSetterProps> = (
  props: LcdpTableOperationSetterProps,
) => {
  const { field, value, onChange } = props;

  const optionsRow = [
    {
      label: 'dj-维护',
      value: 'BUTTON_EDIT_ITEM_DECOUPLE',
    },
    {
      label: 'dj-详情',
      value: 'BUTTON_DETAIL_ITEM_DECOUPLE',
    },
    {
      label: 'dj-删除',
      value: 'BUTTON_DELETE_ITEM_DECOUPLE',
    },
    {
      label: 'dj-通用按钮',
      value: 'BUTTON_DECOUPLE',
    },
  ];

  const { columnDefs } = useMemo(() => {
    const findOperationButtons = (components: any[]): any[] => {
      let buttons: any[] = [];
      components.forEach((component) => {
        if (component.componentName === 'BUTTON') {
          buttons.push(component);
        }
        if (component.children) {
          buttons = buttons.concat(findOperationButtons(component.children));
        }
      });
      return buttons;
    };

    const { columnDefs } = (field?.node?.children ?? []).reduce(
      (acc, cur) => {
        if (cur.componentName !== AthComponentType.DYNAMIC_OPERATION) {
          acc.columnDefs.push(cur);
        }
        return acc;
      },
      { columnDefs: [] },
    );

    return {
      columnDefs: findOperationButtons(columnDefs),
    };
  }, [field?.node?.children]);

  /**
   *添加
   * @param type 按钮类型
   * @param operationType  按钮附加模式
   */
  const handleAdd = (type: ButtonType) => {
    const defaultDslInfo = getDecoupleBtnDefaultValueByType(type);
    const defaultValue: any = {
      componentName: AthComponentType.BUTTON,
      title: t('dj-功能按钮'),
      props: {
        dslInfo: {
          ...defaultDslInfo,
          styleMode: ButtonStyleMode.TEXT,
          size: ButtonSizeType.SMALL,
          targetSchema: value.schema,
          targetPath: value.path,
          attachMode: ButtonAttachMode.ROW,
        },
      },
    };

    const newNode = project.currentDocument?.createNode(defaultValue ?? {});
    getOperationByRow({ field, newNode });
    onChange();
  };

  return (
    <ConfigProvider theme={theme} locale={configProviderLocale}>
      <div className="lcdp-table-operation-item-list-setter">
        <div className="table-operation-item-list-title">
          <span>{t('dj-操作列')}</span>
          <Dropdown
            menu={{
              items: optionsRow?.map((item: any) => {
                return {
                  key: item.value,
                  label: t(item.label),
                  onClick: () => {
                    handleAdd(item.value);
                  },
                };
              }),
            }}
            overlayClassName="table-operation-item-list-dropdown"
            trigger={['click']}
            placement="bottomLeft"
          >
            <span>+{t('dj-添加')}</span>
          </Dropdown>
        </div>
        <div className="table-operation-item-list">
          {columnDefs.map((panel, index) => {
            const panelData = panel.getPropValue('dslInfo');
            return (
              <TableOperationListItem
                key={index}
                index={index}
                title={panelData?.lang?.title?.[i18n?.language] ?? panelData?.title}
                onEdit={(index = 0) => {
                  columnDefs[index].select();
                }}
                onDelete={(index = 0) => {
                  columnDefs[index].remove();
                  onChange();
                }}
              />
            );
          })}
        </div>
      </div>
    </ConfigProvider>
  );
};

export default LcdpLibTableOperationSetter;
