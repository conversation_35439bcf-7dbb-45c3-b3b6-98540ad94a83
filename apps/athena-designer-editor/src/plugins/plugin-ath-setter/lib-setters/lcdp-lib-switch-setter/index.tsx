import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Switch } from 'antd';
import { CommonSetterLayoutProps } from '../../components/common-setter-layout';
import { t } from 'i18next';
import './index.scss';
export interface SwitchProps {
  value?: any;
  onChange?: (values: any) => void;
  options: {
    titleProps: CommonSetterLayoutProps;
    componentProps?: any;
    componentType?: boolean;
  };
}

const SwitchItem: React.FC<SwitchProps> = (props: SwitchProps) => {
  const { value, onChange, options } = props;
  const { componentProps = {}, componentType } = options ?? {};
  const setterTitle = options?.titleProps?.setterTitle as string;
  const onValueChange = (val: boolean) => {
    onChange?.(val);
  };
  return (
    <div className="action-switch">
      <div className="title">{t(setterTitle)}</div>
      <Switch {...componentProps} defaultChecked={componentType} onChange={onValueChange} />
    </div>
  );
};
export default SwitchItem;
