import React, { useEffect, useMemo, useState } from 'react';
import i18n, { t } from 'i18next';
import './index.scss';
import { DeleteOutlined, FormOutlined, HolderOutlined } from '@ant-design/icons';
import { useDrag, useDrop } from 'react-dnd';
import { Mode } from '../type';
import Icon from '@/components/Icon';
import { Popconfirm } from 'antd';

export interface OperationItemInfo {
  index: number;
}

export interface OperationItemProps {
  operation: any;
  index: number;
  onMove: (fromIndex: number, toIndex: number) => void;
  onEdit: (index: number) => void;
  onEditJson: (index: number) => void;
  onDelete: (index: number) => void;
}
const OperationItem: React.FC<OperationItemProps> = (props: OperationItemProps) => {
  const { operation, index, onEdit, onDelete, onMove, onEditJson } = props;

  const title = useMemo(() => {
    return (
      operation?.openWindowDefine?.lang?.title?.[i18n.language] ??
      operation?.openWindowDefine?.title ??
      operation?.name
    );
  }, [operation]);

  const [, ref] = useDrag({
    type: 'operation-item',
    item: { index },
  });

  const [, drop] = useDrop<OperationItemInfo>({
    accept: 'operation-item',
    hover: (draggedItem, monitor) => {
      if (draggedItem.index !== index) {
        onMove(draggedItem.index, index);
        draggedItem.index = index; // 更新拖动项的索引
      }
    },
  });

  return (
    <div className="operation-item" ref={(node) => ref(drop(node))}>
      <span className="operation-item-title">{title}</span>
      <span className="operation-item-button">
        <FormOutlined
          onClick={() => {
            onEdit(index);
          }}
        />
        <Icon
          className="iconfont"
          type="icongaodaima"
          onClick={() => {
            onEditJson(index);
          }}
        />
        <Popconfirm
          className="confirm-delete"
          title={t('dj-确认删除吗？')}
          // placement="left"
          onConfirm={() => {
            onDelete(index);
          }}
          onCancel={() => {}}
          okText={t('dj-删除')}
          cancelText={t('dj-取消')}
        >
          <DeleteOutlined />
        </Popconfirm>
        {/* TODO */}
        <Icon className="iconfont button-move" type="icontuozhuai1" />
      </span>
    </div>
  );
};

export default OperationItem;
