// 列操作基础信息
export const rowOperationBaseInfoList = [
  {
    title: '维护',
    type: 'edit',
    mode: 'row',
    operate: 'openpage',
    operateTarget: 'row',
    lang: {
      zh_CN: '维护',
      zh_TW: '維護',
      en_US: 'maintain',
    },
    sequence: undefined,
    priority: undefined,
  },
  {
    title: '删除',
    type: 'delete-row',
    mode: 'row',
    operate: 'delete-row',
    operateTarget: 'row',
    lang: {
      zh_CN: '刪除',
      zh_TW: '刪除',
      en_US: 'delete',
    },
    sequence: undefined,
    priority: undefined,
  },
  {
    title: '复制',
    type: 'copy',
    mode: 'row',
    lang: {
      zh_CN: '复制',
      zh_TW: '複製',
      en_US: 'copy',
    },
    sequence: undefined,
    priority: undefined,
  },
];

// 表格操作基础信息
export const allOperationBaseInfoList = [
  {
    title: '下载模板',
    type: 'download_template',
    mode: 'all',
    lang: {
      zh_CN: '下载模板',
      zh_TW: '下載模板',
      en_US: 'Download Template',
    },
    sequence: undefined,
    priority: undefined,
  },
  {
    title: '数据导入',
    type: 'upload_file',
    mode: 'all',
    lang: {
      zh_CN: '数据导入',
      zh_TW: '數據匯入',
      en_US: 'export data',
    },
    sequence: undefined,
    priority: undefined,
  },
  {
    title: '数据导出',
    type: 'data_export',
    mode: 'all',
    lang: {
      zh_CN: '数据导出',
      zh_TW: '數據導出',
      en_US: 'Data export',
    },
    sequence: undefined,
    priority: undefined,
  },
  {
    title: 'dj-新建模板',
    type: 'openpage',
    mode: 'all',
    lang: {
      zh_CN: '新建模板',
      zh_TW: '新建模板',
      en_US: 'New Template',
    },
    sequence: undefined,
    priority: undefined,
  },
  {
    title: '客制按钮',
    type: 'isCustomize',
    mode: 'all',
    lang: {
      zh_CN: '客制按钮',
      zh_TW: '客制按鈕',
      en_US: 'Custom button',
    },
    sequence: undefined,
    priority: undefined,
  },
  {
    title: '表格拆行',
    type: 'split-row',
    mode: 'all',
    lang: {
      zh_CN: '表格拆行',
      zh_TW: '表格拆行',
      en_US: 'Table splitting',
    },
    sequence: undefined,
    priority: undefined,
  },
];
