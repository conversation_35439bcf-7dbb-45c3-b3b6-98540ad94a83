import React, { useEffect, useMemo, useState } from 'react';
import { IPublicModelSettingField } from '@alilc/lowcode-types';
import './index.scss';
import { t } from 'i18next';
import { DslData } from '@/tools/business/lcdp-converter/type';
import OperationItem from './operation-item';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import AthenaDesignerCoreMFComponent from '@/components/AthenaDesignerCoreMFComponent';
import { SupportedEditorType } from '@/components/AthenaDesignerCoreMFComponent/type';
import theme from '@/config/theme.json';
import { ConfigProvider } from 'antd';
import {
  AthLowCodeConfigKey,
  AthLowCodeEventName,
  LibOpenWindowData,
} from '../../../plugin-ath-loader/type';
import { config } from '@alilc/lowcode-engine';
import { v4 as uuidv4 } from 'uuid';
import { getDataConnectorId } from '@/tools/utils/setter';
import { cloneDeep } from 'lodash';
import { event } from '@alilc/lowcode-engine';
import { queryFieldTree } from '@/tools/utils/common';

/**
 * 组件 props 定义
 */
export interface LibOpenWindowSetterProps {
  value: DslData;
  field: IPublicModelSettingField;
  onChange: (data: { operations: any[]; fields: any[] }) => void;
}

/**
 * 开窗设置组件
 * 支持新增、编辑、删除和排序开窗操作
 */
const LibOpenWindowSetter: React.FC<LibOpenWindowSetterProps> = (
  props: LibOpenWindowSetterProps,
) => {
  // 开窗数据
  const { value, field, onChange } = props;
  const { operations = [], fields = [] } = value;
  const {
    useCoreData: { dataConnectors = [] },
  } = field?.node?.document?.root?.propsData as any;
  const [treeMapOrigin, setTreeMapOrigin] = useState<Map<string, any>>();

  // 扩展配置信息
  const [isOpenMonacoEditorModal, setIsOpenMonacoEditorModal] = useState(false); // 是否打开脚本编辑器弹窗
  const [editInfo, setEditInfo] = useState<any>(null); // 编辑内容
  const editInfoJson = useMemo(() => JSON.stringify(editInfo, null, 2), [editInfo]); // JSON 字符串化
  const [editOpenWindowEventData, setEditOpenWindowEventData] = useState<any>(null); // 开窗数据状态

  // 开窗设计抽屉
  const [isOpenOpenwindowWorkDesignDrawer, setIsOpenOpenwindowWorkDesignDrawer] =
    useState<boolean>(false); // 是否打开开窗设计器抽屉
  const [editIndex, setEditIndex] = useState(-1); // 当前编辑项索引
  const [operationsIndex, setOperationsIndex] = useState<number>(-1); // 当前编辑的开窗下标

  useEffect(() => {
    const eventKey = `common:${AthLowCodeEventName.LowCodeFieldTreeMapUpdate}`;
    const updateTreeDataOrigin = (athTreeDataNode: Map<string, any>) => {
      setTreeMapOrigin(athTreeDataNode);
    };
    updateTreeDataOrigin(cloneDeep(config.get(AthLowCodeConfigKey.AthFieldTreeMap) ?? new Map()));
    event.on(eventKey, updateTreeDataOrigin);
    return () => {
      event.off(eventKey, updateTreeDataOrigin);
    };
  }, []);

  /**
   * 处理编辑后的 JSON 数据并更新对应操作项
   * @param data 更新后的操作项数据
   */
  const handleResultData = (data: any) => {
    const newOperations = cloneDeep(operations);
    if (editIndex >= 0) {
      newOperations[editIndex] = data;
      handleSaveOperations(newOperations, fields);
    }
  };

  /**
   * 保存更新后的 operations 和 fields 数据
   * @param operations 更新后的操作列表
   * @param fields 更新后的字段列表
   */
  const handleSaveOperations = (operations: any[], fields: any[]) => {
    onChange({ operations, fields });
  };

  /**
   * 构建默认操作对象
   * @returns 默认操作对象
   */
  const getDeafultOperation = () => {
    const { path, schema, type } = value;
    return {
      attach: { mode: 'row', applyToField: schema, target: path },
      sequence: 0,
      operate: 'openwindow',
      name: '开窗',
      id: uuidv4(),
      openWindowDefine: {
        title: '开窗',
        lang: {
          title: {
            en_US: 'open window',
            zh_CN: '开窗',
            zh_TW: '開窗',
          },
        },
        allAction: {
          searchInfos: [],
        },
        multipleSelect: type === 'FORM_OPERATION_EDITOR',
        applyToArray: type === 'FORM_OPERATION_EDITOR',
        supportBatch: false,
        selectedFirstRow: false,
        useHasNext: false,
        enableInputSearch: true,
        buttons: [
          {
            id: 'confirm',
            title: '提交',
            lang: {
              title: {
                zh_TW: '提交',
                en_US: 'Submit',
                zh_CN: '提交',
              },
            },
            language: {
              title: {
                zh_TW: '提交',
                en_US: 'Submit',
                zh_CN: '提交',
              },
            },
            actions: [
              {
                backFills: [],
                type: 'UI',
              },
            ],
          },
        ],
        type: 'dataConnector',
        dataConnectorId: null,
      },
      condition: null,
      description: '',
      lang: {
        description: {
          zh_CN: '',
          zh_TW: '',
          en_US: '',
        },
      },
      navigationConfig: {
        enable: false,
        url: 'base-data-entry',
        urlParams: {},
      },
    };
  };

  /**
   * 打开编辑（新增）弹窗
   * @param index 当前编辑项索引，-1 表示新增
   */
  const handleOpenEditModal = (index = -1) => {
    const { type } = value;
    const editInfo = { ...(index >= 0 ? cloneDeep(operations[index]) : getDeafultOperation()) };
    const eventData = {
      type: 'edit',
      data: {
        component: {
          type,
        },
        data: editInfo,
        dataConnectorId: getDataConnectorId(field),
        dataConnectors,
        fieldTreeMap: treeMapOrigin ?? config.get(AthLowCodeConfigKey.AthFieldTreeMap),
        treeData: cloneDeep(queryFieldTree(field, treeMapOrigin)) || [],
        fields,
      },
    } as LibOpenWindowData;

    setEditOpenWindowEventData(eventData);
    setOperationsIndex(index);
    setIsOpenOpenwindowWorkDesignDrawer(true);
  };

  /**
   * 打开脚本编辑器弹窗
   * @param index 当前编辑项索引
   */
  const handleOpenMonacoEditorModal = (index = 0) => {
    setEditInfo(cloneDeep(operations[index]));
    setEditIndex(index);
    setIsOpenMonacoEditorModal(true);
  };

  /**
   * 脚本编辑器弹窗确认
   * @param data 编辑后的内容
   */
  const handleMonacoEditorModalSubmit = (data: any) => {
    setIsOpenMonacoEditorModal(false);
    handleResultData(JSON.parse(data));
  };

  /**
   * 脚本编辑器弹窗取消
   */
  const handleMonacoEditorCancel = () => {
    setIsOpenMonacoEditorModal(false);
  };

  /**
   * 关闭开窗设计器
   */
  const handleCloseOpenwindowWorkDesign = () => {
    setEditOpenWindowEventData(null);
    setOperationsIndex(-1);
    setIsOpenOpenwindowWorkDesignDrawer(false);
  };

  /**
   * 确认编辑开窗并保存
   * @param operation 更新后的操作项
   * @param updateFields 更新后的字段信息
   */
  const handleConfirmOpenwindowWorkDesign = (operation: any, fields: any[]) => {
    console.log('开窗保存', operation, fields);
    let libOperations: any[] = cloneDeep(operations);
    if (operationsIndex > -1) {
      libOperations[operationsIndex] = operation;
    } else {
      libOperations.push(operation);
    }
    handleSaveOperations(libOperations, fields);
    handleCloseOpenwindowWorkDesign();
  };

  /**
   * 删除操作项
   * @param index 要删除的索引
   */
  const handleDelete = (index = 0) => {
    let libOperations: any[] = cloneDeep(operations);

    const deletedBackFills =
      operations[index]?.openWindowDefine?.buttons?.[0]?.actions?.[0].backFills ?? [];
    let updateFields: any[] = cloneDeep(fields ?? []);

    updateFields = updateFields.filter(
      (item) => !deletedBackFills.find((backFill: any) => backFill.key === item.schema),
    );

    libOperations.splice(index, 1);
    handleSaveOperations(libOperations, updateFields);
  };

  /**
   * 移动操作项顺序
   * @param fromIndex 原位置
   * @param toIndex 目标位置
   */
  const handleMove = (fromIndex: number, toIndex: number) => {
    let libOperations: any[] = cloneDeep(operations);
    const [removed] = libOperations.splice(fromIndex, 1);
    libOperations.splice(toIndex, 0, removed);
    handleSaveOperations(libOperations, fields);
  };

  return (
    <ConfigProvider theme={theme}>
      <div className="ath-operations-open-window-setter">
        {/* 标题栏 */}
        <div className="operations-title">
          <span>{t('dj-开窗列表')}</span>
          <span onClick={() => handleOpenEditModal()}>+&nbsp;{t('dj-添加')}</span>
        </div>

        {/* 操作项列表 */}
        <div className="operations-list">
          <DndProvider backend={HTML5Backend} context={window}>
            {operations.map((operation: any, index: number) => (
              <OperationItem
                key={index}
                index={index}
                operation={operation}
                onMove={handleMove}
                onEdit={handleOpenEditModal}
                onDelete={handleDelete}
                onEditJson={handleOpenMonacoEditorModal}
              />
            ))}
          </DndProvider>
        </div>

        {/* JSON 编辑器弹窗 */}
        <AthenaDesignerCoreMFComponent
          componentName="MonacoEditor"
          componentProps={{
            value: editInfoJson,
            visible: isOpenMonacoEditorModal,
            type: SupportedEditorType.JSON,
            onOk: handleMonacoEditorModalSubmit,
            onCancel: handleMonacoEditorCancel,
          }}
        />

        {/* 开窗设计器抽屉 */}
        {isOpenOpenwindowWorkDesignDrawer && (
          <AthenaDesignerCoreMFComponent
            componentName="OpenwindowWorkDesignLib"
            componentProps={{
              workVisible: isOpenOpenwindowWorkDesignDrawer,
              workData: cloneDeep(editOpenWindowEventData?.data ?? {}),
              isPrivatization: true,
              close: handleCloseOpenwindowWorkDesign,
              onOk: handleConfirmOpenwindowWorkDesign,
            }}
          />
        )}
      </div>
    </ConfigProvider>
  );
};

export default LibOpenWindowSetter;
