import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Form, Space, Select, Input } from 'antd';
import './index.scss';
import CommonSetterLayout, { CommonSetterLayoutProps } from '../../components/common-setter-layout';
import { t } from 'i18next';
import { cloneDeep } from 'lodash';
import AthenaDesignerCoreMFComponent from '@/components/AthenaDesignerCoreMFComponent';

export interface LcdpImageSetterProps {
  value: any;
  onChange: (value: any) => void;
  options: {
    titleProps: CommonSetterLayoutProps;
    formItemRules?: any; // form rules 透传，具体配置 查看 antd
  };
}

const LcdpImageSetter: React.FC<LcdpImageSetterProps> = (props: LcdpImageSetterProps) => {
  const { value: imageConfig, onChange, options } = props;
  const { src: value, useDMC } = imageConfig;
  const { formItemRules = [] } = options;
  const [srcType, setSrcType] = useState('upload');
  const [addIconModalVisible, setAddIconModalVisible] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    if (!value) {
      return;
    }
    // setSrcType(value?.srcType || 'upload');
    form.setFieldsValue({ value });
  }, [value]);

  const onValueChange = async () => {
    try {
      await form.validateFields();
      const data = form.getFieldsValue();
      // onChange({
      //   src: data?.value,
      //   srcType: type || srcType,
      // });
      onChange(data?.value);
    } catch (error) {
      console.log(error);
    }
  };

  // const onChangeType = (data: any) => {
  //   setSrcType(data);
  //   onValueChange(data);
  // };

  const handleChangeSrc = (data: any) => {
    form.setFieldValue('value', data);
    onValueChange();
  };

  const addonAfter = useMemo(() => {
    return (srcType === 'upload' && !!useDMC) ? (
      <div
        style={{ width: 20 }}
        className="image-input-add"
        onClick={() => setAddIconModalVisible(true)}
      >
        {t('dj-选择')}
      </div>
    ) : null;
  }, [srcType]);

  return (
    <CommonSetterLayout {...options.titleProps}>
      <Form className={`ath-image-form`} layout={'horizontal'} form={form} size="middle">
        <Form.Item name="value" rules={[...formItemRules]}>
          <Input
            placeholder={t('dj-请输入')}
            // addonBefore={
            //   <Select style={{ width: 96, height: 28 }} value={srcType} onChange={onChangeType}>
            //     <Select.Option value="upload">{t('dj-手动上传')}</Select.Option>
            //     <Select.Option value="link">{t('dj-图片链接')}</Select.Option>
            //   </Select>
            // }
            onChange={(e) => {
              onValueChange();
            }}
            addonAfter={addonAfter}
          />
        </Form.Item>
      </Form>
      {addIconModalVisible && (
        <AthenaDesignerCoreMFComponent
          componentName="ImgUpload"
          componentProps={{
            visible: addIconModalVisible,
            value,
            onClose: () => setAddIconModalVisible(false),
            onOk: (data: any) => handleChangeSrc(data),
          }}
        />
      )}
    </CommonSetterLayout>
  );
};

export default LcdpImageSetter;
