import React, { useEffect, useRef, useState } from 'react';
import { config } from '@alilc/lowcode-engine';
import { ButtonTypeSelector } from '../../components/Button/ButtonTypeSelector';
import { ButtonType, Pattern } from '../../components/Button/enum';
import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type/ath-loader-manage';
import CommonSetterLayout from '../../components/common-setter-layout';
import {
  findTargetNodeByTypes,
  getAttachMode,
  getDefaultValueByType,
} from '../../components/Button/tools';

import type { ILcdpButtonTypeSetterProps, IAppInfo } from './types';
import { AthComponentType } from '@/tools/business/lcdp-converter/type';
import { queryFieldTree } from '@/tools/utils/common';

function LcdpButtonTypeSetter(props: ILcdpButtonTypeSetterProps) {
  const { value, onChange, options, field } = props;
  const { type, id } = value ?? {};
  const [appInfo, setAppInfo] = useState<IAppInfo>({});
  /**
   * 字段树顶层data_name
   */
  const schema = useRef<string>('');

  useEffect(() => {
    const dynamicInfo = config.get(AthLowCodeConfigKey.AthDynamicWorkDesignInfo);
    const fieldData = queryFieldTree(field);
    if (fieldData?.[0]?.data_name) {
      schema.current = fieldData[0].data_name;
    }
    const attachMode = getAttachMode(field?.getNode());
    const targetNode = findTargetNodeByTypes(
      [AthComponentType.ATHENA_TABLE, AthComponentType.FORM_LIST],
      field?.getNode(),
    );
    const targetNodeDslInfo = targetNode?.getPropValue('dslInfo') ?? {};
    setAppInfo({
      /**
       * TODO: 目前pattern是没有存的，所以默认是基础资料（因为本期只接基础资料）
       */
      pattern: dynamicInfo?.pattern ?? Pattern.DATA_ENTRY,
      category: dynamicInfo?.category,
      pageCode: dynamicInfo?.pageCode,
      attachMode,
      schema: targetNodeDslInfo?.schema,
      path: targetNodeDslInfo?.path,
    });
  }, [field]);

  useEffect(() => {
    doTypeChange(type);
  }, [type]);

  const doTypeChange = (type: ButtonType) => {
    const defaultValue = getDefaultValueByType({
      type,
      pattern: appInfo.pattern,
      pageCode: appInfo.pageCode,
      category: appInfo.category,
      submitInfo: { schema: schema.current },
      operationInfo: { schema: appInfo.schema, path: appInfo.path, attachMode: appInfo.attachMode },
    });
    const needAddActivityIdButtonTypeSet: Set<ButtonType> = new Set([
      ButtonType.BUTTON_OPENPAGE_ADD,
      ButtonType.BUTTON_ADD_ITEM,
      ButtonType.BUTTON_OPENPAGE_EDIT,
    ]);
    /**
     * operations 的新增和维护按钮，需要补父页面的pageCode, 如果是子页面的话
     */
    if (needAddActivityIdButtonTypeSet.has(type)) {
      const dynamicWorkDesignInfo = config.get(AthLowCodeConfigKey.AthDynamicWorkDesignInfo);
      defaultValue.activityId = dynamicWorkDesignInfo?.parentPageCode;
    }
    onChange({ ...defaultValue, id });
  };

  const doErrorType = (type: ButtonType) => {
    doTypeChange(type);
  };

  return (
    <CommonSetterLayout {...(options?.titleProps ?? {})}>
      <div className="lcdp-button-type-setter-wrapper">
        <ButtonTypeSelector
          type={type}
          {...appInfo}
          onChange={doTypeChange}
          onErrorType={doErrorType}
        />
      </div>
    </CommonSetterLayout>
  );
}

LcdpButtonTypeSetter.displayName = 'LcdpButtonTypeSetter';
export default LcdpButtonTypeSetter;
