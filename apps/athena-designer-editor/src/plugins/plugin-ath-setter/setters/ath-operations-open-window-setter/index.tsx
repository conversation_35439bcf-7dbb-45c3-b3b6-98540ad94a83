import React, { Component, useEffect, useMemo, useState } from 'react';
import {
  IPublicModelNode,
  IPublicModelSettingField,
  IPublicTypePropChangeOptions,
} from '@alilc/lowcode-types';
import './index.scss';
import i18n, { t } from 'i18next';
import { DslData } from '@/tools/business/lcdp-converter/type';
import OperationItem from './operation-item';

import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Mode } from './type';
import AthenaDesignerCoreMFComponent from '@/components/AthenaDesignerCoreMFComponent';
import { SupportedEditorType } from '@/components/AthenaDesignerCoreMFComponent/type';
import theme from '@/config/theme.json';
import { ConfigProvider } from 'antd';
import {
  MessageToMainType,
  OpenWindowData,
  AthLowCodeEventName,
  AthLowCodeConfigKey,
} from '../../../plugin-ath-loader/type';
import { config, event } from '@alilc/lowcode-engine';
import { v4 as uuidv4 } from 'uuid';
import { getDataConnectorId, getDataSourceName } from '@/tools/utils/setter';
import { getDataSourceNameFactory, queryFieldTree } from '@/tools/utils/common';

interface Operation {
  mode: Mode;
  target: string;
  id: string;
  title?: string;
  name?: string;
  description?: string;
  applyToField?: string;
  openWindowDefine?: any;
  [key: string]: any;
}

export interface AthOperationsOpenWindowSetterProps {
  value: DslData;
  field: IPublicModelSettingField;
  onChange: () => void;
}

const AthOperationsOpenWindowSetter: React.FC<AthOperationsOpenWindowSetterProps> = (
  props: AthOperationsOpenWindowSetterProps,
) => {
  const { value, field, onChange } = props;
  const { operations = [], dataConnectors = [] } = field?.node?.document?.root?.propsData as any;

  // 是否打开脚本编辑器弹窗
  const [isOpenMonacoEditorModal, setIsOpenMonacoEditorModal] = useState(false);
  // 是否打开DSL开窗设计
  const [isOpenOpenwindowWorkDesignDrawer, setIsOpenOpenwindowWorkDesignDrawer] =
    useState<boolean>(false);
  // const [operationsOrigin, setOptionsOrigin] = useState<any[]>([]);
  const [editIndex, setEditIndex] = useState(-1);
  const [editInfo, setEditInfo] = useState<any>(null);
  const editInfoJson = useMemo(() => {
    return JSON.stringify(editInfo, null, 2);
  }, [editInfo]);

  // 开窗数据
  const [editOpenWindowEventData, setEditOpenWindowEventData] = useState<any>(null);
  // 开窗operations的下标
  const [operationsIndex, setOperationsIndex] = useState<any>(null);

  const dynamicInfo = config.get(AthLowCodeConfigKey.AthDynamicWorkDesignInfo) ?? {};

  const fullPath = useMemo(() => {
    const { path: nodePath, schema: nodeSchema } = value;
    const fullPath = [nodePath, nodeSchema].filter(Boolean).join('.');
    return fullPath;
  }, [value]);

  // 匹配的开窗操作
  // 平台对于 操作数据的 定义 比较奇怪，所以记录一下
  // 首先，当前 只有开窗栏位才会支持自定义开窗操作，所以这个 setter 只给开窗栏位使用
  // 此外，栏位组件 暂时 只能 新增 开窗操作，所以这里 只需要匹配  row 和 fullPath 即可
  // 匹配到的 都可以被认为是 开窗操作
  // 奇怪的 地方在于 operation的 作用路径
  // 对于 栏位的 row操作数据 其 作用路径 是 target + applyToField
  // 而 对于 表格的 row操作数据 其applyToField 固定是 BUTTON_GROUP，对于 表格的 all操作数据 没有 applyToField
  // 所以 表格 的操作数据 仅需 匹配 target，而 开窗栏位 需匹配 target + applyToField
  // 因此作用在 表格上的 row 操作，由于 applyToField 都是 固定值，所以 在栏位这里 自然不会 匹配到
  const operationsOpenWindow: Operation[] = useMemo(() => {
    return operations.filter((operation: any) => {
      const operationFullPath = [operation.target, operation.applyToField]
        .filter(Boolean)
        .join('.');
      return operation.mode === 'row' && operationFullPath === fullPath;
    });
  }, [operations, fullPath]);

  // useEffect(() => {
  //   updateOperationsOrigin();
  //   const dispose = field?.node?.document?.onChangeNodeProp(
  //     (info: IPublicTypePropChangeOptions) => {
  //       const { node, key } = info;
  //       if (node === field?.node?.document?.root && key === 'operations') {
  //         updateOperationsOrigin();
  //       }
  //     },
  //   );

  //   return () => {
  //     dispose?.();
  //   };
  // }, []);

  // const updateOperationsOrigin = () => {
  //   const { operations = [] } = field?.node?.document?.root?.propsData as any;
  //   setOptionsOrigin(operations);
  // };

  // 处理编辑后的数据
  const handleResultData = (formValue: any) => {
    const currentOperation = {
      ...formValue,
    };

    let operations: Operation[] = [...operationsOpenWindow];
    if (editIndex >= 0) {
      operations.splice(editIndex, 1, currentOperation);
    } else {
      operations = [...operations, currentOperation];
    }

    handelSaveOperations(operations);
  };

  // 编辑了操作数据，更新到根节点
  const handelSaveOperations = (operationsOpenWindow: any[]) => {
    const operationsOtherPrevious: any[] = operations.filter((item: any) => {
      const operationFullPath = [item.target, item.applyToField].filter(Boolean).join('.');
      return operationFullPath !== fullPath;
    });
    field?.node?.document?.root?.setPropValue('operations', [
      ...operationsOtherPrevious,
      ...operationsOpenWindow,
    ]);
    onChange();
  };

  const getDeafultOperation = () => {
    const { path, schema, type } = value;
    return {
      mode: 'row',
      sequence: 0,
      operate: 'openwindow',
      name: '开窗',
      applyToField: schema,
      target: path,
      id: uuidv4(),
      openWindowDefine: {
        multipleSelect: type === 'FORM_OPERATION_EDITOR',
        applyToArray: type === 'FORM_OPERATION_EDITOR',
        buttons: [
          {
            id: 'confirm',
            title: '提交',
            lang: {
              title: {
                zh_TW: '提交',
                en_US: 'Submit',
                zh_CN: '提交',
              },
            },
            language: {
              title: {
                zh_TW: '提交',
                en_US: 'Submit',
                zh_CN: '提交',
              },
            },
            actions: [
              {
                backFills: [],
                type: 'UI',
              },
            ],
          },
        ],
      },
    };
  };

  // 打开编辑（新增）弹窗
  const handleOpenEditModal = (index = -1) => {
    const { type } = value;
    const editInfo = { ...(index >= 0 ? operationsOpenWindow[index] : getDeafultOperation()) };
    const athDynamicWorkDesignInfo = config.get(AthLowCodeConfigKey.AthDynamicWorkDesignInfo) ?? {};
    const getDataSourceNameFN = getDataSourceNameFactory();
    const dataSourceName = getDataSourceNameFN(field);
    const treeData = queryFieldTree(field);
    const eventData = {
      type: 'edit',
      data: {
        component: {
          type,
        },
        data: editInfo,
        fieldTreeData: treeData,
        fieldTreeDataMap: config.get(AthLowCodeConfigKey.AthFieldTreeMap),
        dataSourceName,
        dataConnectorId: getDataConnectorId(field),
        // searchViewDisplay:
        //   athDynamicWorkDesignInfo?.config?.sidebarConfig?.dataSourceOptions?.isShowQueryPlan,
        serachViewDisplay:
          !!athDynamicWorkDesignInfo?.dynamicWorkDesignConfig?.businessConfig
            ?.isOpenWindowShowQueryPlan,
        // TODO 历史逻辑的单词 都拼错了，这里先暂时沿用，否则没法 正常使用之前的逻辑，但 后续 该 历史逻辑肯定要改
      },
    } as OpenWindowData;

    if (editInfo?.openWindowDefine?.roleAttention) {
      eventData.data['roleAttention'] = editInfo?.openWindowDefine?.roleAttention;
    }

    // event.emit(`${AthLowCodeEventName.AthRulesHandle}`, {
    //   type: MessageToMainType.OpenWindow,
    //   data: eventData,
    // });

    console.log('athDynamicWorkDesignInfo', athDynamicWorkDesignInfo);

    console.log(index, JSON.parse(JSON.stringify(eventData.data)));

    // 开窗数据
    setEditOpenWindowEventData(eventData);
    // 开窗的operations的下标
    setOperationsIndex(index);
    // 打开开窗
    setIsOpenOpenwindowWorkDesignDrawer(true);
  };

  // 打开脚本编辑器弹窗
  const handleOpenMonacoEditorModal = (index = 0) => {
    setEditInfo({ ...operationsOpenWindow[index] });
    setEditIndex(index);
    setIsOpenMonacoEditorModal(true);
  };

  // 脚本编辑器弹窗确认
  const handleMonacoEditorModalSubmit = (data: any) => {
    setIsOpenMonacoEditorModal(false);
    handleResultData(JSON.parse(data));
  };

  // 脚本编辑器弹窗取消
  const handleMonacoEditorCancel = () => {
    setIsOpenMonacoEditorModal(false);
  };

  // 关闭开窗
  const handleCloseOpenwindowWorkDesign = () => {
    setEditOpenWindowEventData(null);
    setOperationsIndex(-1);
    setIsOpenOpenwindowWorkDesignDrawer(false);
  };

  // 确认编辑开窗
  const handleConfirmOpenwindowWorkDesign = (data: any) => {
    let operations = [...operationsOpenWindow];
    if (operationsIndex > -1) {
      operations[operationsIndex] = data;
    } else {
      operations.push(data);
    }
    handelSaveOperations(operations);
    handleCloseOpenwindowWorkDesign();
  };

  // 删除操作
  const handleDelete = (index = 0) => {
    let operations = [...operationsOpenWindow];
    operations.splice(index, 1);
    handelSaveOperations(operations);
  };

  // 移动了操作顺序
  const handleMove = (fromIndex: number, toIndex: number) => {
    let operations = [...operationsOpenWindow];
    const [removed] = operations.splice(fromIndex, 1);
    operations.splice(toIndex, 0, removed);
    handelSaveOperations(operations);
  };

  console.log(
    'AthOperationsOpenWindowSetter - isPrivatization -',
    dynamicInfo?.dynamicWorkDesignConfig?.businessConfig?.isPrivatization,
  );

  return (
    <ConfigProvider theme={theme}>
      <div className="ath-operations-open-window-setter">
        <div className="operations-title">
          <span>{t('dj-开窗列表')}</span>
          <span
            onClick={() => {
              handleOpenEditModal();
            }}
          >
            +&nbsp;{t('dj-添加')}
          </span>
        </div>
        <div className="operations-list">
          <DndProvider backend={HTML5Backend} context={window}>
            {operationsOpenWindow.map((operation, index) => {
              return (
                <OperationItem
                  key={index}
                  index={index}
                  operation={operation}
                  onMove={handleMove}
                  onEdit={handleOpenEditModal}
                  onDelete={handleDelete}
                  onEditJson={handleOpenMonacoEditorModal}
                />
              );
            })}
          </DndProvider>
        </div>

        <AthenaDesignerCoreMFComponent
          componentName="MonacoEditor"
          componentProps={{
            value: editInfoJson,
            visible: isOpenMonacoEditorModal,
            type: SupportedEditorType.JSON,
            onOk: handleMonacoEditorModalSubmit,
            onCancel: handleMonacoEditorCancel,
          }}
        />

        {isOpenOpenwindowWorkDesignDrawer && (
          <AthenaDesignerCoreMFComponent
            componentName="OpenwindowWorkDesign"
            componentProps={{
              workVisible: isOpenOpenwindowWorkDesignDrawer,
              workData: JSON.parse(JSON.stringify(editOpenWindowEventData?.data ?? {})),
              appType: dynamicInfo?.appType,
              applicationCode: dynamicInfo?.applicationCode,
              isPrivatization:
                dynamicInfo?.dynamicWorkDesignConfig?.businessConfig?.isPrivatization,
              close: handleCloseOpenwindowWorkDesign,
              onOk: handleConfirmOpenwindowWorkDesign,
            }}
          />
        )}
      </div>
    </ConfigProvider>
  );
};

export default AthOperationsOpenWindowSetter;
