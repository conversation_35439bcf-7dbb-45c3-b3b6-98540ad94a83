import React from 'react';
import { useTranslation } from 'react-i18next';
import { LayoutBox } from '../../components/LayoutBox';
import './index.scss';
import type { ModalPaddingetterProps } from './types';
import type { ILayoutBoxInfo } from '../../components/LayoutBox/types';
import CommonSetterLayout from '../../components/common-setter-layout';

function ModalPaddingSetter(props: ModalPaddingetterProps) {
  const { value, onChange, options } = props;

  const { top = '', left = '', right = '', bottom = '' } = value ?? {};

  const { t } = useTranslation();

  const doLayoutChange = (data: ILayoutBoxInfo) => {
    onChange({
      ...data,
    });
  };

  return (
    <CommonSetterLayout {...options?.titleProps}>
      <div className="lcdp-padding-setter-wrapper">
        <LayoutBox
          placeholder={t('24px')}
          data={{ top, left, right, bottom }}
          onChange={doLayoutChange}
        />
      </div>
    </CommonSetterLayout>
  );
}

ModalPaddingSetter.displayName = 'ModalPaddingSetter';
export default ModalPaddingSetter;
