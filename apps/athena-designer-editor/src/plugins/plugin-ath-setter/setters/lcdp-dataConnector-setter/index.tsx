import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Button, Form, Modal, Select } from 'antd';
import './index.scss';
import CommonSetterLayout, { CommonSetterLayoutProps } from '../../components/common-setter-layout';
import i18n, { t } from 'i18next';
import {
  IPublicModelSettingField,
} from '@alilc/lowcode-types';

export interface AthCommonSetterProps {
  field: IPublicModelSettingField;
  value: any;
  onChange: (value: any) => void;
  options: {
    titleProps: CommonSetterLayoutProps;
    formItemRules?: any; // form rules 透传，具体配置 查看 antd
    componentProps?: any; // 组件属性 透传，具体配置 查看 antd
  };
}

const LcdpDataConnectorSetter: React.FC<AthCommonSetterProps> = (props: AthCommonSetterProps) => {
  const { field, value: connectConfig, onChange, options } = props;
  const { dataConnectorId: value, type } = connectConfig;
  const { formItemRules = [], componentProps = {} } = options;
  const setterTitle = options?.titleProps?.setterTitle;

  const { useCoreData: { dataConnectors = [] } } = field?.node?.document?.root?.propsData as any;
  const formRules = useMemo(() => {
    return (
      (formItemRules || []).map((item: any) => {
        item.message = t(item.message);
        return item;
      }) || []
    );
  }, [formItemRules]);

  // @ts-ignore
  const requiredObj = options?.formItemRules?.find((item) => item?.hasOwnProperty('required')) ?? {
    required: false,
  };
  if (requiredObj?.required && typeof setterTitle === 'string') {
    options.titleProps.setterTitle = (
      <div>
        <span style={{ color: '#eb0000', fontSize: '10px' }}>* </span>
        {t(setterTitle)}
      </div>
    );
  }
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue({ value });
  }, [value]);

  const confirmChange = (value: string, clear: boolean) => {
    onChange({
      dataConnectorId: value,
      clear,
    });
    Modal.destroyAll();
  };

  const onValueSelect = useCallback(
    (newValue: string) => {
      if (type !== 'dataConnect') {
        confirmChange(newValue, false);
      } else {
        Modal.confirm({
          title: '',
          width: 426,
          content: t(
            'dj-更换或清空数据源可能会导致当前组件及子组件的配置无效（path+schema等），是否需要清空相关配置？',
          ),
          footer: (_, { OkBtn, CancelBtn }) => (
            <>
              <Button
                type="primary"
                onClick={() => {
                  confirmChange(newValue, true);
                }}
              >
                {t('dj-清空')}
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  confirmChange(newValue, false);
                }}
              >
                {t('dj-保留')}
              </Button>
              <CancelBtn />
            </>
          ),
          onCancel: () => {
            form.setFieldsValue({ value });
          },
        });
      }
    },
    [form, value],
  );

  return (
    <CommonSetterLayout {...options.titleProps}>
      <Form
        className={`ath-common-form`}
        layout={'horizontal'}
        form={form}
        size="middle"
        onValuesChange={(data) => {
          onValueSelect(data?.value);
        }}
      >
        <Form.Item name="value" rules={[...formRules]}>
          <Select
            className="ath-common-select"
            {...componentProps}
            options={(dataConnectors || []).map((d) => ({ label: d.name, value: d.name }))}
            // onChange={onValueChange}
          />
        </Form.Item>
      </Form>
    </CommonSetterLayout>
  );
};

export default LcdpDataConnectorSetter;
