import React from 'react';

const SIZE_OPTIONS = [
  { label: '小', value: 'small' },
  { label: '中', value: 'medium' },
  { label: '大', value: 'large' },
  { label: '超大', value: 'xlarge' },
];

function AthModalSizeSetter({ value = 'medium', onChange }) {
  return (
    <div style={{ display: 'flex', gap: 8 }}>
      {SIZE_OPTIONS.map(opt => (
        <button
          key={opt.value}
          type="button"
          style={{
            padding: '4px 16px',
            border: value === opt.value ? '2px solid #4B7BFA' : '1px solid #ccc',
            background: value === opt.value ? '#E6F0FF' : '#fff',
            borderRadius: 4,
            cursor: 'pointer',
            color: value === opt.value ? '#4B7BFA' : '#333',
          }}
          onClick={() => onChange(opt.value)}
        >
          {opt.label}
        </button>
      ))}
    </div>
  );
}

export default AthModalSizeSetter;
