import {
  ButtonActionType,
  ButtonApiType,
  ActionParamType,
  ActionParamConverterType,
  ButtonType,
  PositionType,
  PlacementType,
  SubpageOpenType,
  ButtonIconPositionType,
  ButtonAlignType,
  ButtonStyleMode,
  ButtonSizeType,
  ButtonAttachMode,
  ButtonShapeType,
  ButtonConditionTriggerType,
  PageCode,
  OperationType,
} from './enum';

export interface ILangDetailInfo {
  zh_CN: string;
  zh_TW: string;
  en_US: string;
}

export interface ILangInfo {
  [key: string]: ILangDetailInfo;
}

/**
 * BUTTON_GROUP以及所有相关BUTTON的数据结构统一定义
 */

/**
 * 流程+送审时会自动生成这个玩意，有啥用不知道，问了能不能删，说不能，processId是uuid生成的
 */
export interface IButtonWorkflowInvokeParas {
  processId?: string;
  [key: string]: any;
}

/**
 * 按钮提交相关配置数据定义
 */
export interface IButtonSubmitType {
  /**
   * 提交数据
   */
  schema?: string;
  /**
   * 是否分批
   */
  isBatch?: boolean;
  /**
   * 是否提交所有数据
   */
  submitAll?: boolean;
}

/**
 * 特殊添加的extendParas数据
 */
export interface IButtonExtendParasInfo {
  /**
   * 不知道啥意思，有啥用，默认添时=true
   */
  ignoreDataChange?: boolean;
  /**
   * 不知道啥意思，默认添加时=true
   */
  applyToRecycle?: boolean;
}

/**
 * action属性核心定义
 */
export interface IButtonActionCoreInfo {
  /**
   * 操作API的类型
   */
  type?: ButtonApiType;
  /**
   * 业务类型
   */
  actionType?: ButtonActionType;
  /**
   * 服务的actionId
   */
  actionId?: string;
  /**
   * 服务名
   */
  serviceName?: string;
  /**
   * API路径
   */
  url?: string;
  /**
   * 字段在格式化attachAction/combineAction时自己生成的id
   * 2024-12-12 文档：https://hx3wbv0mcjh.feishu.cn/wiki/WbqQwm9Waib19CkLlaPcEv4ansc中并没有这个字段，所以删了
   */
  // parentId?: string;
  paras?: IButtonWorkflowInvokeParas;
}

/**
 * 按钮参数结构定义
 */
export interface IButtonActionParam {
  /**
   * 提交目标字段
   */
  name?: string;
  /**
   * 变量类型
   */
  type?: ActionParamType;
  /**
   * 值
   */
  value?: string;
  /**
   * 变量转换器
   */
  typeConverter?: ActionParamConverterType;
}

/**
 * attachAction/combineAction的action对象定义
 */
export interface IButtonSubActionInfo extends IButtonActionCoreInfo {
  /**
   * 标题
   */
  title?: string;
  /**
   * 成功之后的签章文本
   */
  returnText?: string;
  /**
   * 语言信息
   */
  lang?: ILangInfo;
  /**
   * 整个combineActions和attachActions，把转换交给转换器，dsl操作中统一为下面的属性，避免切换type造成的一系列麻烦的问题
   */
  combineAttachActions?: IButtonSubActionInfo[];
  /**
   * COMBINE类型的子Action
   */
  // combineActions?: IButtonSubActionInfo[];
  /**
   * 其余类型的子Action
   */
  // attachActions?: IButtonSubActionInfo[];
  /**
   * 参数
   */
  actionParams?: IButtonActionParam[];
  dataSourceType?: string;
  dataConnectorId?: string;
}

/**
 * 打印模版信息
 */
export interface IPrintTemplateInfo {
  /**
   * 打印模版code，prinTemplateCode
   */
  templateId?: string;
  /**
   * 打印模版对应的actionId
   */
  actionId?: string;
  /**
   * 配置的参数
   */
  actionParams?: IButtonActionParam[];
}

/**
 * 子页面信息
 */
export interface ISubPageDefineInfo {
  /**
   * 子页面页面code
   */
  pageCode?: string;
  /**
   * 子页面查询code
   */
  code?: string;
  subPageCode?: string;
}

/**
 * 子页面详细信息
 */
export interface ISubPageQueryParamInfo {
  /**
   * 子页面详细信息
   */
  pageDefine?: ISubPageDefineInfo;
}

export interface ISubPageOptionalInfo {
  /**
   * 标题
   */
  title?: string;
  /**
   * 标题多语言
   */
  lang?: ILangInfo;
  /**
   * 宽度, 400 | "400px" | "40vw"
   */
  width?: number | string;
  /**
   * 高度, 400 | "400px" | "40vw"
   */
  height?: number | string;
  /**
   * 抽屉位置，抽屉独有
   */
  placement?: PlacementType;
}

/**
 * 子页面配置
 */
export interface ISubPageConfigDetailInfo {
  /**
   * 以什么形式打开子页面
   */
  target?: SubpageOpenType;
  /**
   * 子页面信息
   */
  queryParams?: ISubPageQueryParamInfo;
  /**
   * 子页面补充信息
   */
  optional?: ISubPageOptionalInfo;
}

/**
 * 子页面配置
 */
export interface ISubPageConfigInfo {
  subpageConfig?: ISubPageConfigDetailInfo;
}

export interface IOperationAttachInfo {
  /**
   * target = 当前关联表格的fullPath
   */
  target?: string;
  /**
   * mode = all | row
   * WARN: 其实还有个line，但是line不在画布里面体现, 只做透传
   */
  mode?: ButtonAttachMode;
  /**
   * applyToField:
   * 1. mode = all时，为空
   * 2. mode = row时
   *    1. 浏览界面 = BUTTON_GROUP
   *    2. 编辑界面 = UIBOT_BUTTON_GROUP
   */
  applyToField?: string;
  /**
   * 自动拆行比较字段
   */
  comparisonField?: string;
}

export interface IOperationOpenWindowDefineInfo {
  /**
   * 是否定制
   */
  isCustomize?: boolean;
}

/**
 * 原operation类型按钮的扩展字段定义
 */
export interface IButtonOperationExtendedFieldsInfo {
  /**
   * 关联表格的关联数据源的actionId
   */
  actionId?: string;
  /**
   * WARN: 只是在文档中看见，之前没有这玩意，所以只是定义
   */
  targetField?: string;
  /**
   * WARN: 部分同上，上面好歹知道类型是string
   */
  actionParas?: unknown;
}

/**
 * operation相关核心配置
 */
export interface IOperationConfigDetailInfo {
  /**
   * operation 标题
   */
  title?: string;
  /**
   * operation类型
   */
  type?: OperationType;
  /**
   * 国际化内容
   */
  lang?: ILangInfo;
  /**
   * 排序字段，是否需要未知
   * NOTE: 不需要，没吊用
   */
  // sequence?: number;
  /**
   * 描述
   */
  description?: string;
  /**
   * 是否客制
   * WARN: 是否operation内的客制按钮其实是根据类型来的, 这个字段只需和类型匹配即可, 但是要有; 另客制按钮类型=isCustomize
   */
  isCustomize?: boolean;
  /**
   * 挂载字段
   */
  applyToField?: string;
  /**
   * 操作方式
   */
  operate?: string;
  /**
   * 执行脚本
   */
  operateScript?: string;
  /**
   * 操作目标
   */
  operateTarget?: string;
  /**
   * 拆行挂在字段
   */
  targetDetailField?: string;
  /**
   * 如何挂载相关字段，包含原来operation用于判断如何挂载
   */
  attach?: IOperationAttachInfo;
  /**
   * 开窗配置信息
   */
  openWindowDefine?: IOperationOpenWindowDefineInfo;
  /**
   * 使用场景
   */
  pageCode?: PageCode;
  /**
   * condition配置
   */
  condition?: string;
  /**
   * hidden配置
   */
  hidden?: string;
  /**
   * TODO: 未知字段，原operation并没有这个字段
   */
  switchData?: boolean;
  /**
   * 原operation特殊的扩展字段
   */
  extendedFields?: IButtonOperationExtendedFieldsInfo | null;

  /**
   * 数据导入配置的action属性
   */
  actions?: IActionsInfo[];

  /**
   * 同步emit配置
   */
  emitConfig?: any;
  dataSourceType?: string;
  dataConnectorId?: string;
}

/**
 * 数据导入配置的action属性
 */
export interface IActionsInfo {
  actionId?: string;
  targetField?: string;
  type?: 'insert' | 'update' | 'insertOrUpdate';
}

/**
 * operation按钮相关属性定义, 这边特殊的点：打印按钮，operation按钮沿用之前动态打印按钮的结构，所以已经调整了原来动态打印按钮的数据结构：IButtonActionInfo
 * WARN: 所以BUTTON_PRINT按钮的类型定义应该继承的是IButtonActionInfo而不是IButtonOperationInfo,虽然operation中也有打印按钮
 */
export interface IOperationConfigInfo {
  /**
   * 描述
   */
  description?: string;
  /**
   * operation相关配置都在这个字段下
   */
  operation?: IOperationConfigDetailInfo;
}

/**
 * 存储的选择的打印模版的信息
 */
export interface IButtonPrintTemplateBusinessConfigInfo {
  /**
   * 模板id
   */
  businessId?: string;
  /**
   * 模板code
   */
  activityId?: string;
}

/**
 * 打印模版1.0打印模版相关数据存储字段
 */
export interface IButtonPrintTemplateActionInfo {
  /**
   * 写死 ATMC_PRINT
   */
  type?: string;
  /**
   * 提交配置，这边只关注schema，M层表名 data-name
   */
  submitType?: IButtonSubmitType;
  /**
   * 服务的actionId
   */
  actionId?: string;
  /**
   * 参数配置
   */
  actionParams?: IButtonActionParam[];
  /**
   * 选择的打印模版的信息
   */
  businessConfig?: IButtonPrintTemplateBusinessConfigInfo;
}

/**
 * 扩展字段详细配置定义
 */
export interface IButtonExtendedFieldsDetailInfo {
  /**
   * 打印模版1.0，数据存在action下
   */
  action?: IButtonPrintTemplateActionInfo;
  /**
   * 打印模版2.0，数据存在templates下，关联的打印模版数据
   * 目前在做兼容历史数据的逻辑时，由于对接的后端开发说不想改代码，所以需要保留action字段。也就是转换之后action和templates会同时存在
   */
  templates?: IPrintTemplateInfo[];
}

/**
 * 扩展字段信息
 */
export interface IButtonExtendedFieldsInfo {
  /**
   * 扩展字段详细配置信息
   */
  extendedFields?: IButtonExtendedFieldsDetailInfo;
}

/**
 * action属性定义, 主要用在SubmitAction
 */
export interface IButtonActionInfo
  extends Omit<IButtonActionCoreInfo, 'type'>,
    Omit<IButtonPrintTemplateActionInfo, 'type'> {
  type?: ButtonApiType | string;
  /**
   * 语言信息
   */
  lang?: ILangInfo;
  /**
   * 是否设为默认按钮, 对应改按钮基类的styleMode属性
   */
  // defaultAction?: boolean;
  /**
   * 成功之后的签章文本
   */
  returnText?: string;
  /**
   * 数据处理完毕，是否结束任务
   */
  dispatch?: boolean;
  /**
   * 是否终止流程
   */
  terminateProcess?: boolean;
  /**
   * 是否在数据提交之后执行
   */
  executeAfterCheckCompleted?: boolean;
  /**
   * 根据场景默认添加的额外数据
   */
  extendParas?: IButtonExtendParasInfo;
  /**
   * 彻底删除默认添加=false
   */
  dispatchBPM?: boolean;
  /**
   * 彻底删除默认添加=false
   */
  needProxyToken?: boolean;
  /**
   * 整个combineActions和attachActions，把转换交给转换器，dsl操作中统一为下面的属性，避免切换type造成的一系列麻烦的问题
   */
  combineAttachActions?: IButtonSubActionInfo[];
  /**
   * COMBINE类型的子Action
   */
  // combineActions?: IButtonSubActionInfo[];
  /**
   * 其余类型的子Action
   */
  // attachActions?: IButtonSubActionInfo[];
  /**
   * 参数
   */
  actionParams?: IButtonActionParam[];
  /**
   * 提交配置
   */
  submitType?: IButtonSubmitType;
  /**
   * TODO: 目前不知道什么意思
   */
  trackCode?: string;
  /**
   * 打印模版2.0，数据存在templates下，关联的打印模版数据
   * 目前在做兼容历史数据的逻辑时，由于对接的后端开发说不想改代码，所以需要保留action字段。也就是转换之后action和templates会同时存在
   */
  templates?: IPrintTemplateInfo[];
  dataSourceType?: string;
  dataConnectorId?: string;
}

/**
 * condition配置信息
 */
export interface IButtonConditionInfo {
  /**
   * 脚本
   */
  script?: string;
  /**
   * 触发时机
   */
  trigger?: ButtonConditionTriggerType[];
  /**
   * 关联校验状态，代替原来的ignoreRule（原来并没有这个属性）;默认值false，不关联校验状态；true则关联校验，校验通过可点击，不通过则disabled
   */
  relateValidators?: boolean;
  /**
   * 业务数据录入页面，当没有编辑过，则底部按钮不高亮。代替ignoreDataChange
   * 是否忽略数据变化，设定为true时，当表单未发生数据变更时，按钮也可以点击
   * false，则数据change和按钮状态无关；true，没有编辑过的，禁止点击按钮
   */
  unchangedForbidClick?: boolean;
}

/**
 * 提示信息配置
 */
export interface IButtonConfirmInfo {
  /**
   * 是否启用
   */
  enable?: boolean;
  /**
   * 提示信息标题，默认提示
   */
  title?: string;
  /**
   * 提示文案多语言
   */
  lang?: ILangInfo;
  /**
   * 提示信息
   */
  content?: string;
}

/**
 * 隐藏配置, 原来是hidden,改为hiddenConfig
 */
export interface IButtonHiddenInfo {
  /**
   * 执行脚本
   */
  script?: string;
  /**
   * 触发时机
   */
  trigger?: ButtonConditionTriggerType[];
}

/**
 * 现有提交按钮中一些听说在特定场景才有用，但是目前的实现是一直存在的字段，单独抽出来为了方便后续知道具体在哪个场景使用之后，单独拎出来
 */
export interface ISubmitButtonExtraInfo {
  /**
   * condition配置
   */
  condition?: IButtonConditionInfo;
  /**
   * 点击二次确认框配置
   */
  confirm?: IButtonConfirmInfo;
  /**
   * 隐藏配置
   */
  hiddenConfig?: IButtonHiddenInfo;
}

/**
 * 报表类型的相关参数
 */
export interface IReportNavigationUrlParams {
  /**
   * 报表code
   */
  code?: string;
  /**
   * 报表category
   */
  category?: string;
  /**
   * 当前应用code
   */
  appCode?: string;
}

/**
 * 任务详情的相关参数
 */
export interface ITaskDetailNavigationUrlParams {
  /**
   * 写死的：activity__backLog__data.ptmBacklogId
   */
  id?: string;
}

/**
 * 基础资料录入的相关参数
 */
export interface IBaseDataEntryNavigationUrlParams {
  /**
   * 作业code
   */
  code?: string;
  /**
   * 作业类型
   */
  category?: string;
}

/**
 * 按钮跳转配置
 */
export type IButtonNavigationConfig = {
  /**
   * 是否启用
   */
  enable: boolean;
} & (
  | ({
      /**
       * 跳转类型
       */
      url?: 'report';
    } & {
      /**
       * 跳转类型的相关参数定义
       */
      urlParams?: IReportNavigationUrlParams;
    })
  | ({ url?: 'task-detail' } & { urlParams?: ITaskDetailNavigationUrlParams })
  | ({ url?: 'base-data-entry' } & { urlParams?: IBaseDataEntryNavigationUrlParams })
);

/**
 * 按钮上图标的配置信息
 * 通过position兼容之前的beforeIcon、afterIcon，旧配置beforeIcon、afterIcon两个按钮都有时，只显示beforeIcon
 * 也就是说当兼容历史数据时，针对icon的处理逻辑如下：
 *  if (afterIcon && !beforeIcon) {
 *    icon.name = afterIcon
 *    icon.position = after
 *  } else {
 *    icon.name = beforeIcon
 *    icon.position = before
 *  }
 *
 */
export interface IButtonIconConfig {
  /**
   * 图标库中icon的名称
   */
  name?: string;
  /**
   * 图标位置在前还是在后, 默认before
   */
  position?: ButtonIconPositionType;
}

export interface PageConfig {
  operationType?: string;
  dslCode?: string;
}

/**
 * 通用BUTTON数据结构定义
 */
export interface IBaseButtonInfo {
  id?: string;
  /**
   * 按钮类型
   */
  type?: ButtonType;
  /**
   * 按钮对齐方向, 默认center
   */
  align?: ButtonAlignType;
  /**
   * 按钮类型, 默认default
   */
  styleMode?: ButtonStyleMode;
  /**
   * 按钮大小, 默认large
   */
  size?: ButtonSizeType;
  /**
   * 是否禁用，默认false
   */
  disabled?: boolean;
  /**
   * 幽灵属性，使按钮背景透明，无视其他样式属性, 默认false
   */
  ghost?: boolean;
  /**
   * 设置危险按钮，红色主题，视情况红字、红背景、红边框, 默认false
   */
  danger?: boolean;
  /**
   * 块状按钮，按钮宽度是父元素宽度, 默认false
   */
  block?: boolean;
  /**
   * 按钮显示文字
   */
  title?: string;
  /**
   * 国际化信息
   */
  lang?: ILangInfo;
  /**
   * 是否开启点击防抖, 默认false
   */
  debounce?: boolean;
  /**
   * 开启防抖后的防抖时长, 默认300
   */
  debounceTime?: number;
  /**
   * 按钮title所在schema
   */
  schema?: string;
  /**
   * 按钮title所在path
   */
  path?: string;
  /**
   * 加载态, 目前只有通用按钮需要配置这两个参数
   */
  async?: boolean;
  /**
   * 加载态, 目前只有通用按钮需要配置这两个参数
   */
  showLoading?: boolean;
  /**
   * 作用是整个数组，还是某一行, 目前只有row
   * WARN: 目前得到的消息是，这个字段是给后面的operation预留的，目前的功能按钮和提交按钮都用不到这个字段
   */
  attachMode?: ButtonAttachMode;
  /**
   * 跳转配置
   */
  navigationConfig?: IButtonNavigationConfig;
  /**
   * 按钮针对数据所在schema
   */
  targetSchema?: string;
  /**
   * 按钮针对数据所在path
   */
  targetPath?: string;
  /**
   * 设置按钮形状，圆角、圆形
   * WARN: 基础组件历史原因不支持暂不开放
   */
  shape?: ButtonShapeType;
  /**
   * 按钮的图标配置
   */
  iconConfig?: IButtonIconConfig;
  /**
   * 子页面保存并关闭按钮配置，目前写死close-page
   */
  trailingAction?: string;
  /**
   * 当按钮在子页面时，会存在父页面的pageCode，就存在这
   */
  activityId?: string;
  emitConfig?: PageConfig;
}

/**
 * 提交按钮数据结构定义
 * - submitAction转换的BUTTON的dsl结构定义
 */
export interface ISubmitButtonInfo extends IBaseButtonInfo, ISubmitButtonExtraInfo {
  action?: IButtonActionInfo;
  dataSourceType?: string;
  dataConnectorId?: string;
}

/**
 * 功能按钮数据结构定义
 */
export interface IBusinessButtonInfo
  extends IBaseButtonInfo,
    IButtonExtendedFieldsInfo,
    ISubPageConfigInfo,
    IOperationConfigInfo {}

/**
 * BUTTON_GROUP核心数据结构定义
 */
export interface IButtonGroupInfo {
  id: string;
  /**
   * 组件类型 = BUTTON_GROUP
   */
  type: string;
  /**
   * 显示顺序：是否反转显示, 默认false, submitAction转按钮，单档多栏和浏览页默认true
   */
  reverse: boolean;
  /**
   * 按钮最多显示的数量，超出的收起展示（预留属性后续完善）
   */
  maxDisplayNumber?: number;
  /**
   * 内边距，值示例：['10px', '10px']
   */
  margin?: [string, string];
  /**
   * 外边距，值示例：['10px', '10px'], submitAction转按钮，默认["12px", "0px"]
   */
  padding?: [string, string];
  /**
   * 展示的位置，submitAction转按钮，默认为“absolute”
   */
  position?: PositionType;
  /**
   * 左侧距离
   */
  left?: string;
  /**
   * 右侧距离
   */
  right?: string;
  /**
   * 顶部距离
   */
  top?: string;
  /**
   * 底部距离，submitAction转按钮，默认为“0px”
   */
  bottom?: string;
  /**
   * 容器属性
   */
  group: IBaseButtonInfo[];
}
