import { t } from 'i18next';
import { Button<PERSON>ttachMode, ButtonCategory, ButtonType } from './enum';

import type { IButtonTypeSelectOption } from './types';

/**
 * 全量提交按钮类型集合
 */
export const SubmitButtonTypeSet: Set<ButtonType> = new Set([
  ButtonType.BUTTON_DATA_DELETE,
  ButtonType.BUTTON_DELETE_TO_RECYCLE,
  ButtonType.BUTTON_RECYCLE_DELETE,
  ButtonType.BUTTON_DATA_DELETEALL,
  ButtonType.BUTTON_DATA_SAVE,
  ButtonType.BUTTON_DATA_COMBINE_SAVE,
  ButtonType.BUTTON_DATA_UPDATE,
  ButtonType.BUTTON_DATA_INVALID,
  ButtonType.BUTTON_DATA_VALID,
  ButtonType.BUTTON_DATA_RESET_VALID,
  ButtonType.BUTTON_DATA_CANCEL_VALID,
  ButtonType.BUTTON_DATA_RECOVER,
  ButtonType.BUTTON_WORKFLOW_INVOKE,
  ButtonType.BUTTON_WORKFLOW_ABORT,
  ButtonType.BUTTON_AGREE,
  ButtonType.BUTTON_DISAGREE,
  ButtonType.BUTTON_ACT_RETURN,
  ButtonType.BUTTON_ACT_ADD,
  ButtonType.BUTTON_WORKFLOW_SUBMIT,
  ButtonType.BUTTON_DATA_TERMINATE,
  ButtonType.BUTTON_REPORT_QUERY,
  ButtonType.BUTTON_TASK_REEXECUTE,
  ButtonType.BUTTON_TASK_TERMINATE,
  ButtonType.BUTTON_TASK_REAPPROVE,
  ButtonType.BUTTON_TASK_ADD,
  ButtonType.BUTTON_TASK_AGREE,
  ButtonType.BUTTON_CLOSE,
  // ButtonType.BUTTON_SUB_PAGE_SAVE,
]);

/**
 * 功能按钮类型集合
 */
export const FunctionButtonTypeSet: Set<ButtonType> = new Set([
  ButtonType.BUTTON_COMBINE_SAVE_DECOUPLE,
  ButtonType.BUTTON_OPENPAGE_ADD_DECOUPLE,
  ButtonType.BUTTON_DATA_DELETE_DECOUPLE,
]);

/**
 * 表格操作按钮集合
 */
export const TableButtonTypeSet: Set<ButtonType> = new Set([
  ButtonType.BUTTON_ADD_ITEM_DECOUPLE,
  ButtonType.BUTTON_OPENPAGE_ADD_DECOUPLE,
  ButtonType.BUTTON_DECOUPLE,
]);

/**
 * 表格行操作按钮集合
 */
export const TableRowButtonTypeSet: Set<ButtonType> = new Set([
  ButtonType.BUTTON_EDIT_ITEM_DECOUPLE,
  ButtonType.BUTTON_DETAIL_ITEM_DECOUPLE,
  ButtonType.BUTTON_DELETE_ITEM_DECOUPLE,
  ButtonType.BUTTON_DECOUPLE,
]);

/**
 * 通用按钮, 目前只有一个，但是统一写法
 */
export const CommonButtonTypeSet: Set<ButtonType> = new Set([ButtonType.BUTTON]);

/**
 * 原整单操作
 * WARN: 注意这里为了定义更清晰，所以保留了双档新增、复制的定义。当然前提是Set有去重能力。。。
 */
export const ToolbarButtonTypeSet: Set<ButtonType> = new Set([
  ButtonType.BUTTON_OPENPAGE_ADD,
  ButtonType.BUTTON_OPENPAGE_COPY,
  ButtonType.BUTTON_TOOLBAR_EDIT,
  ButtonType.BUTTON_TOOLBAR_NEXT,
  ButtonType.BUTTON_TOOLBAR_PREVIOUS,
]);

/**
 * 单档多栏 mode===row时支持的特殊operation类型集合
 */
export const SingleDocumentOperationRowButtonTypeSet: Set<ButtonType> = new Set([
  /**
   * 单档多栏复制
   */
  ButtonType.BUTTON_COPY_ITEM,
]);

/**
 * 单双多档 mode===row时支持的特殊operation类型集合
 */
export const NotSingleDocumentOperationRowEditPageButtonTypeSet: Set<ButtonType> = new Set([
  /**
   * 单档多栏复制
   */
  ButtonType.BUTTON_OPENPAGE_EDIT,
  /**
   * 单双多档复制
   */
  ButtonType.BUTTON_OPENPAGE_COPY,
  ButtonType.BUTTON_COPY_ITEM,
]);

/**
 * 单双多档 mode===row时支持的特殊operation类型集合
 */
export const NotSingleDocumentOperationRowBrowserPageButtonTypeSet: Set<ButtonType> = new Set([
  /**
   * 单双多档维护
   */
  ButtonType.BUTTON_OPENPAGE_EDIT,
  /**
   * 单双多档复制
   */
  ButtonType.BUTTON_OPENPAGE_COPY,
]);

/**
 * 单档多栏 mode===all时支持的特殊operation类型集合
 */
export const SingleDocumentOperationAllButtonTypeSet: Set<ButtonType> = new Set([
  /**
   * 单档多栏新增行
   */
  ButtonType.BUTTON_ADD_ITEM,
]);

/**
 * 单双多档 mode===all时支持的特殊operation类型集合
 */
export const NotSingleDocumentOperationAllEditPageButtonTypeSet: Set<ButtonType> = new Set([
  /**
   * 单档多栏新增行
   */
  ButtonType.BUTTON_ADD_ITEM,
  /**
   * 单双多档新增行
   */
  ButtonType.BUTTON_OPENPAGE_ADD,
]);

/**
 * 单双多档 mode===all时支持的特殊operation类型集合
 */
export const NotSingleDocumentOperationAllBrowserPageButtonTypeSet: Set<ButtonType> = new Set([
  /**
   * 单双多档新增行
   */
  ButtonType.BUTTON_OPENPAGE_ADD,
]);

/**
 * mode===row场景支持的按钮基础集合
 */
export const OperationRowBaseButtonTypeSet: Set<ButtonType> = new Set([
  ButtonType.BUTTON_PRINT,
  ButtonType.BUTTON_DELETE_ITEM,
]);

/**
 * mode===all场景支持的按钮基础集合
 */
export const OperationAllBaseButtonTypeSet: Set<ButtonType> = new Set([
  ButtonType.BUTTON_PRINT,
  ButtonType.BUTTON_BATCH_DELETE_ITEM,
  ButtonType.BUTTON_UPLOAD_FILE,
  ButtonType.BUTTON_DOWNLOAD_TEMPLATE,
  ButtonType.BUTTON_DRAWINGS_DOWNLOAD,
  ButtonType.BUTTON_FRONT_EXPORT,
  ButtonType.BUTTON_BACKEND_EXPORT,
  ButtonType.BUTTON_BATCH_SET_DATE,
  ButtonType.BUTTON_OPERATE_SCRIPT,
  ButtonType.BUTTON_SPLIT_ROW,
  ButtonType.BUTTON_AUTO_SPLIT_ROW,
  ButtonType.BUTTON_IMPORT,
]);

/**
 * 行operation集合定义
 * WARN: 原OperationButtonTypeSet拆分成OperationRowButtonTypeSet和OperationAllButtonTypeSet。为了定义更明确
 */
export const OperationRowButtonTypeSet: Set<ButtonType> = new Set([
  ...OperationRowBaseButtonTypeSet,
  ...SingleDocumentOperationRowButtonTypeSet,
  ...NotSingleDocumentOperationRowBrowserPageButtonTypeSet,
  ...NotSingleDocumentOperationRowEditPageButtonTypeSet,
]);

/**
 * 整单operation集合定义
 */
export const OperationAllButtonTypeSet: Set<ButtonType> = new Set([
  ...OperationAllBaseButtonTypeSet,
  ...SingleDocumentOperationAllButtonTypeSet,
  ...NotSingleDocumentOperationAllBrowserPageButtonTypeSet,
  ...NotSingleDocumentOperationAllEditPageButtonTypeSet,
]);

/**
 * operation按钮全量集合
 */
export const OperationButtonTypeSet: Set<ButtonType> = new Set([
  ...OperationRowButtonTypeSet,
  ...OperationAllButtonTypeSet,
]);

/**
 * 全量功能按钮集合
 */
export const BusinessButtonTypeSet: Set<ButtonType> = new Set([
  ButtonType.BUTTON,
  /**
   * 2025-02-12 PO要求暂时隐藏打印按钮
   * 2025-03-11 新需求开始转换operation&toolabr为动态按钮，operation中的打印沟通后确认复用之前的动态打印按钮
   */
  ButtonType.BUTTON_PRINT,
  ButtonType.BUTTON_SUB_PAGE,
  ...ToolbarButtonTypeSet,
  ...OperationButtonTypeSet,
]);

/**
 * 按钮分类对应的全量可用按钮类型Map定义
 */
export const ButtonTypeMap: Map<ButtonCategory, Set<ButtonType>> = new Map([
  /**
   * 2025-03-11 common按钮回到功能按钮集合中,不再作为一个独立的类目
   */
  // [ButtonCategory.COMMON, CommonButtonTypeSet],
  [ButtonCategory.BUSINESS_BUTTON, BusinessButtonTypeSet],
  [ButtonCategory.SUBMIT_BUTTON, SubmitButtonTypeSet],
  [ButtonCategory.FUNCTION_BUTTON, FunctionButtonTypeSet],
  // 表格按钮
  [ButtonCategory.TABLE_BUTTON, TableButtonTypeSet],
  // 表格列按钮
  [ButtonCategory.TABLE_ROW_BUTTON, TableRowButtonTypeSet],
]);

/**
 * 按钮类型对应类型文案定义
 */
export const ButtonTypeTextMap: Map<ButtonType, string> = new Map([
  [ButtonType.BUTTON, t('dj-通用')],
  [ButtonType.BUTTON_SUB_PAGE, t('dj-打开子页面')],
  // [ButtonType.BUTTON_SUB_PAGE_SAVE, t('dj-子页面保存并关闭')],
  [ButtonType.BUTTON_PRINT, t('dj-打印')],
  [ButtonType.BUTTON_DATA_DELETE, t('dj-删除')],
  [ButtonType.BUTTON_DELETE_TO_RECYCLE, t('dj-删除到回收站')],
  [ButtonType.BUTTON_RECYCLE_DELETE, t('dj-彻底删除')],
  [ButtonType.BUTTON_DATA_DELETEALL, t('dj-删除全部')],
  [ButtonType.BUTTON_DATA_SAVE, t('dj-新建保存')],
  [ButtonType.BUTTON_DATA_COMBINE_SAVE, t('dj-组合式保存')],
  [ButtonType.BUTTON_DATA_UPDATE, t('dj-更新保存')],
  [ButtonType.BUTTON_DATA_INVALID, t('dj-失效')],
  [ButtonType.BUTTON_DATA_VALID, t('dj-生效')],
  [ButtonType.BUTTON_DATA_RESET_VALID, t('dj-重新生效')],
  [ButtonType.BUTTON_DATA_CANCEL_VALID, t('dj-取消生效')],
  [ButtonType.BUTTON_DATA_RECOVER, t('dj-还原')],
  [ButtonType.BUTTON_WORKFLOW_INVOKE, t('dj-送审')],
  [ButtonType.BUTTON_WORKFLOW_ABORT, t('dj-撤审')],
  [ButtonType.BUTTON_AGREE, t('dj-同意')],
  [ButtonType.BUTTON_DISAGREE, t('dj-不同意')],
  [ButtonType.BUTTON_ACT_RETURN, t('dj-退回')],
  [ButtonType.BUTTON_ACT_ADD, t('dj-加签')],
  [ButtonType.BUTTON_WORKFLOW_SUBMIT, t('dj-提交')],
  [ButtonType.BUTTON_DATA_TERMINATE, t('dj-终止')],
  [ButtonType.BUTTON_REPORT_QUERY, t('dj-查询')],
  [ButtonType.BUTTON_TASK_REEXECUTE, t('dj-退回重办')],
  [ButtonType.BUTTON_TASK_TERMINATE, t('dj-终止任务')],
  [ButtonType.BUTTON_TASK_REAPPROVE, t('dj-退回重签')],
  [ButtonType.BUTTON_TASK_ADD, t('dj-加签')],
  [ButtonType.BUTTON_TASK_AGREE, t('dj-同意')],
  /**
   * operations start
   */
  [ButtonType.BUTTON_FRONT_EXPORT, t('dj-前端导出')],
  [ButtonType.BUTTON_BACKEND_EXPORT, t('dj-数据导出')],
  [ButtonType.BUTTON_DRAWINGS_DOWNLOAD, t('dj-图纸下载')],
  [ButtonType.BUTTON_BATCH_SET_DATE, t('dj-批量指定交期')],
  [ButtonType.BUTTON_OPERATE_SCRIPT, t('dj-自动更换')],
  [ButtonType.BUTTON_SPLIT_ROW, t('dj-手动拆行')],
  [ButtonType.BUTTON_AUTO_SPLIT_ROW, t('dj-自动拆分')],
  [ButtonType.BUTTON_ADD_ITEM, t('dj-新增行')],
  [ButtonType.BUTTON_DELETE_ITEM, t('dj-删除')],
  [ButtonType.BUTTON_BATCH_DELETE_ITEM, t('dj-批量删除')],
  [ButtonType.BUTTON_COPY_ITEM, t('dj-复制行')],
  [ButtonType.BUTTON_UPLOAD_FILE, t('dj-文件导入')],
  [ButtonType.BUTTON_DOWNLOAD_TEMPLATE, t('dj-下载模板')],
  [ButtonType.BUTTON_OPENPAGE_ADD, t('dj-新增')],
  [ButtonType.BUTTON_OPENPAGE_EDIT, t('dj-维护')],
  [ButtonType.BUTTON_OPENPAGE_COPY, t('dj-复制')],
  [ButtonType.BUTTON_CLOSE, t('dj-关闭子页面')],
  /**
   * operations end
   * toolbar tart
   */
  [ButtonType.BUTTON_TOOLBAR_EDIT, t('dj-编辑')],
  [ButtonType.BUTTON_TOOLBAR_PREVIOUS, t('dj-上一笔')],
  [ButtonType.BUTTON_TOOLBAR_NEXT, t('dj-下一笔')],
  /**
   * toolbar end
   */
  [ButtonType.BUTTON_IMPORT, t('dj-多模式导入')],
  /**
   * 功能按钮
   */
  [ButtonType.BUTTON_COMBINE_SAVE_DECOUPLE, t('dj-保存')],
  [ButtonType.BUTTON_OPENPAGE_ADD_DECOUPLE, t('dj-新增')],
  [ButtonType.BUTTON_DATA_DELETE_DECOUPLE, t('dj-删除')],
  [ButtonType.BUTTON_EDIT_ITEM_DECOUPLE, t('dj-维护')],
  [ButtonType.BUTTON_DETAIL_ITEM_DECOUPLE, t('dj-详情')],
  [ButtonType.BUTTON_DELETE_ITEM_DECOUPLE, t('dj-删除')],
  [ButtonType.BUTTON_DECOUPLE, t('dj-通用')],
  [ButtonType.BUTTON_ADD_ITEM_DECOUPLE, t('dj-新增行')]
]);

/**
 * 功能按钮类型的所有可选项定义
 */
export const BusinessButtonOptions: IButtonTypeSelectOption[] = [...BusinessButtonTypeSet].map(
  (type) => {
    return {
      label: ButtonTypeTextMap.get(type)!,
      value: type,
    };
  },
);

/**
 * 提交按钮类型的所有可选项定义
 */
export const SubmitButtonOptions: IButtonTypeSelectOption[] = [...SubmitButtonTypeSet].map(
  (type) => {
    return {
      label: ButtonTypeTextMap.get(type)!,
      value: type,
    };
  },
);

/**
 * 功能按钮类型的所有可选项定义
 */
export const FunctionButtonOptions: IButtonTypeSelectOption[] = [...FunctionButtonTypeSet].map(
  (type) => {
    return {
      label: ButtonTypeTextMap.get(type)!,
      value: type,
    };
  },
);

/**
 * 表格操作按钮
 */
export const TableButtonOptions: IButtonTypeSelectOption[] = [...TableButtonTypeSet].map((type) => {
  return {
    label: ButtonTypeTextMap.get(type)!,
    value: type,
  };
});

/**
 * 表格列操作按钮
 */
export const TableRowButtonOptions: IButtonTypeSelectOption[] = [...TableRowButtonTypeSet].map(
  (type) => {
    return {
      label: ButtonTypeTextMap.get(type)!,
      value: type,
    };
  },
);

/**
 * 按钮分类类型的所有可选项定义
 */
export const ButtonTypeOptionsMap: Map<ButtonCategory, IButtonTypeSelectOption[]> = new Map([
  [ButtonCategory.BUSINESS_BUTTON, BusinessButtonOptions],
  [ButtonCategory.SUBMIT_BUTTON, SubmitButtonOptions],
  [ButtonCategory.FUNCTION_BUTTON, FunctionButtonOptions],
  [ButtonCategory.TABLE_BUTTON, TableButtonOptions],
  [ButtonCategory.TABLE_ROW_BUTTON, TableRowButtonOptions],
]);

/**
 * 基础资料 -> 浏览界面, 可用的提交按钮类型集合
 */
export const BrowserPageSubmitButtonSet = new Set<ButtonType>([
  ButtonType.BUTTON_DATA_DELETE,
  ButtonType.BUTTON_DATA_INVALID,
  ButtonType.BUTTON_DATA_VALID,
  ButtonType.BUTTON_DATA_CANCEL_VALID,
  ButtonType.BUTTON_DATA_RESET_VALID,
  ButtonType.BUTTON_DELETE_TO_RECYCLE,
  ButtonType.BUTTON_DATA_RECOVER,
  ButtonType.BUTTON_RECYCLE_DELETE,
  // ButtonType.BUTTON_SUB_PAGE_SAVE,
]);

/**
 * 基础资料 -> 编辑界面, 可用的提交按钮类型集合
 */
export const EditPageSubmitButtonSet = new Set<ButtonType>([
  ButtonType.BUTTON_DATA_SAVE,
  ButtonType.BUTTON_DATA_COMBINE_SAVE,
  ButtonType.BUTTON_DATA_UPDATE,
  ButtonType.BUTTON_DATA_DELETE,
  ButtonType.BUTTON_DATA_INVALID,
  ButtonType.BUTTON_DATA_VALID,
  ButtonType.BUTTON_DATA_RESET_VALID,
  ButtonType.BUTTON_DATA_CANCEL_VALID,
  ButtonType.BUTTON_WORKFLOW_ABORT,
  ButtonType.BUTTON_WORKFLOW_INVOKE,
  // ButtonType.BUTTON_SUB_PAGE_SAVE,
]);

/**
 * 基础资料 -> 单档多栏 -> 界面设计, 可用的提交按钮类型集合
 */
export const SingleDocumentSubmitButtonSet = new Set<ButtonType>([
  ButtonType.BUTTON_DATA_SAVE,
  ButtonType.BUTTON_DATA_COMBINE_SAVE,
  ButtonType.BUTTON_DATA_UPDATE,
  ButtonType.BUTTON_DATA_DELETE,
  ButtonType.BUTTON_DELETE_TO_RECYCLE,
  ButtonType.BUTTON_RECYCLE_DELETE,
  ButtonType.BUTTON_DATA_INVALID,
  ButtonType.BUTTON_DATA_VALID,
  ButtonType.BUTTON_DATA_RESET_VALID,
  ButtonType.BUTTON_DATA_CANCEL_VALID,
  ButtonType.BUTTON_DATA_RECOVER,
  ButtonType.BUTTON_WORKFLOW_ABORT,
  ButtonType.BUTTON_WORKFLOW_INVOKE,
  // ButtonType.BUTTON_SUB_PAGE_SAVE,
]);

/**
 * 基础资料 -> 单档多栏 -> 界面设计->子页面, 可用的提交按钮类型集合
 */
export const SingleDocumentSubPageSubmitButtonSet = new Set<ButtonType>([
  ...SingleDocumentSubmitButtonSet,
  ButtonType.BUTTON_CLOSE,
]);

/**
 * 功能按钮基础可用按钮类型集合(非表单/表格内部的功能按钮集合)
 */
export const BusinessButtonBaseTypeSet = new Set<ButtonType>([
  ButtonType.BUTTON,
  ButtonType.BUTTON_PRINT,
  ButtonType.BUTTON_SUB_PAGE,
  ...ToolbarButtonTypeSet,
]);

/**
 * 功能按钮基础可用operation按钮类型集合(表格/表单内部功能按钮走operation配置)
 */
export const BusinessOperationButtonBaseTypeSet = new Set<ButtonType>([
  ButtonType.BUTTON,
  ButtonType.BUTTON_PRINT,
  ButtonType.BUTTON_SUB_PAGE,
]);

/**
 * 基础资料 -> 浏览界面, 可用的功能按钮类型集合
 * TODO: 配置未定，确认之后会更新
 */
export const BrowserPageOperationButtonMap = new Map<ButtonAttachMode, Set<ButtonType>>([
  [
    ButtonAttachMode.ALL,
    new Set([
      ...BusinessOperationButtonBaseTypeSet,
      ...OperationAllBaseButtonTypeSet,
      ...NotSingleDocumentOperationAllBrowserPageButtonTypeSet,
    ]),
  ],
  [
    ButtonAttachMode.ROW,
    new Set([
      ...BusinessOperationButtonBaseTypeSet,
      ...OperationRowBaseButtonTypeSet,
      ...NotSingleDocumentOperationRowBrowserPageButtonTypeSet,
    ]),
  ],
]);

/**
 * 基础资料 -> 编辑界面, 可用的功能按钮类型集合
 * TODO: 配置未定，确认之后会更新
 */
export const EditPageOperationButtonMap = new Map<ButtonAttachMode, Set<ButtonType>>([
  [
    ButtonAttachMode.ALL,
    new Set([
      ...BusinessOperationButtonBaseTypeSet,
      ...OperationAllBaseButtonTypeSet,
      ...NotSingleDocumentOperationAllEditPageButtonTypeSet,
    ]),
  ],
  [
    ButtonAttachMode.ROW,
    new Set([
      ...BusinessOperationButtonBaseTypeSet,
      ...OperationRowBaseButtonTypeSet,
      ...NotSingleDocumentOperationRowEditPageButtonTypeSet,
    ]),
  ],
]);

/**
 * 基础资料 -> 单档多栏 -> 界面设计, 可用的功能按钮类型集合
 * TODO: 配置未定，确认之后会更新
 */
export const SingleDocumentOperationButtonMap = new Map<ButtonAttachMode, Set<ButtonType>>([
  [
    ButtonAttachMode.ALL,
    new Set([
      ...BusinessOperationButtonBaseTypeSet,
      ...OperationAllBaseButtonTypeSet,
      ...SingleDocumentOperationAllButtonTypeSet,
    ]),
  ],
  [
    ButtonAttachMode.ROW,
    new Set([
      ...BusinessOperationButtonBaseTypeSet,
      ...OperationRowBaseButtonTypeSet,
      ...SingleDocumentOperationRowButtonTypeSet,
    ]),
  ],
]);

/**
 * 基础资料 -> 子页面, 可用的功能按钮类型集合
 * TODO: 配置未定，确认之后会更新
 */
export const SubPageOperationButtonMap = new Map<ButtonAttachMode, Set<ButtonType>>([
  [
    ButtonAttachMode.ALL,
    new Set([
      ...BusinessOperationButtonBaseTypeSet,
      ...OperationAllBaseButtonTypeSet,
      ...SingleDocumentOperationAllButtonTypeSet,
      ...NotSingleDocumentOperationAllEditPageButtonTypeSet,
      ...NotSingleDocumentOperationAllBrowserPageButtonTypeSet,
    ]),
  ],
  [
    ButtonAttachMode.ROW,
    new Set([
      ...BusinessOperationButtonBaseTypeSet,
      ...OperationRowBaseButtonTypeSet,
      ...SingleDocumentOperationRowButtonTypeSet,
      ...NotSingleDocumentOperationRowEditPageButtonTypeSet,
      ...NotSingleDocumentOperationRowBrowserPageButtonTypeSet,
    ]),
  ],
]);
