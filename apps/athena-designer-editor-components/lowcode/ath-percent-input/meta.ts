import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType } from '../common/common.config';
import { AthenaDataType } from '../common/common.type';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  tipSetter,
  commonBasicSetter,
  commonDataTypeSetter,
  commonLibRuleSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const AthPercentInputMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.PERCENT_INPUT,
  title: 'dj-百分比',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonColumn',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          { ...commonBasicSetter.placeholder },
          ...tipSetter,
          { ...commonBasicSetter.status }, // 是否启用
          { ...commonBasicSetter.isFocusDisplay },
          { ...commonDataTypeSetter('PERCENT_INPUT') },
        ],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonLibRuleSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        // parentWhitelist: parentWhitelistMap.get(AthenaComponentType.PERCENT_INPUT) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-百分比',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/PERCENT_INPUT.svg`,

    schema: {
      componentName: AthenaComponentType.PERCENT_INPUT,
      title: 'dj-百分比',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.PERCENT_INPUT,
          headerName: '百分比',
          placeholder: '请输入',
          schema: '',
          path: '',
          disabled: false,
          editable: true,
          isFocusDisplay: false,
          dataType: AthenaDataType.NUMERIC,
          iconType: '',
          tooltipMode: 'normal', // 有注释iconType的都要加
          lang: {
            headerName: {
              zh_CN: '百分比',
              zh_TW: '百分比',
              en_US: 'percent',
            },
            placeholder: {
              zh_CN: '请输入',
              zh_TW: '請輸入',
              en_US: 'please enter',
            },
            extraContent: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
            tooltipTitle: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
          },
        },
      },
    },
  },
];

export default {
  ...AthPercentInputMeta,
  snippets,
};
