import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import { AthenaDataType } from '../common/common.type';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  tipSetter,
  commonBasicSetter,
  commonDataTypeSetter,
  commonLibRuleSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const SelectMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.SELECT,
  title: 'dj-下拉单选',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonColumn',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          { ...commonBasicSetter.placeholder },
          ...tipSetter,
          { ...commonBasicSetter.status }, // 是否启用
          { ...commonBasicSetter.isFocusDisplay },
          { ...commonDataTypeSetter(AthenaComponentType.SELECT) },
          // 选项setter
          { ...commonBasicSetter.libOptions },
          { ...commonBasicSetter.options },
        ],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonLibRuleSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        parentWhitelist: parentWhitelistMap.get(AthenaComponentType.SELECT) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-下拉单选',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/SELECT.svg`,
    schema: {
      componentName: AthenaComponentType.SELECT,
      title: 'dj-下拉单选',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.SELECT,
          headerName: '下拉单选',
          placeholder: '请选择',
          schema: '',
          path: '',
          disabled: false,
          editable: true,
          isFocusDisplay: false,
          dataType: AthenaDataType.STRING,
          tooltipMode: 'normal', // 有注释iconType的都要加
          iconType: '',
          options: [],
          // dictionaryId: null,
          // dictionaryKey: '',
          // enumKey: '',
          lang: {
            headerName: {
              zh_CN: '下拉单选',
              zh_TW: '下拉單選',
              en_US: 'select',
            },
            placeholder: {
              zh_CN: '请选择',
              zh_TW: '請選擇',
              en_US: 'please select',
            },
            extraContent: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
            tooltipTitle: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
          },
        },
      },
    },
  },
];

export default {
  ...SelectMeta,
  snippets,
};
