import {
  IPublicTypeComponentMetadata,
  IPublicTypeSnippet,
  IPublicEnumTransformStage,
  IPublicModelSettingField,
  IPublicTypePropsMap,
  IPublicModelNode,
} from '@alilc/lowcode-types';

import { defaultGroupSettingFieldData } from './config';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonDataSourceNamesSetter,
  commonDataConnectorSetter,
} from '../common/common-meta-info.config';
import {
  AthenaComponentType,
  childWhitelistMap,
  isDynamicComponent,
} from '../common/common.config';
import { envParams } from '@/env';
import { BusinessButtonTypeSet, SubmitButtonTypeSet } from '../button/constant';
import { isEmpty } from 'lodash';
import { getAppHelperUtils } from '@/tools';

const LcdpAthenaTableMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.ATHENA_TABLE,
  title: 'dj-表格',
  group: 'dj-标准组件',
  category: 'dj-容器组件',
  docUrl: '',
  screenshot: '',
  icon: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpAthenaTable',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      // 内容
      {
        title: 'dj-内容',
        type: 'group',
        display: 'accordion',
        items: [
          // 快速编辑
          {
            setValue(target: IPublicModelSettingField, value) {
              // 进行排序
              target.node?.children?.mergeChildren(
                (node) => {
                  if (value.waitDel?.some((item) => item.id === node.id)) return true;
                  return false;
                },
                () => {
                  value.waitAdd?.forEach((item) => {
                    const value = item?.props?.dslInfo;
                    item.componentName = value.type;
                    if (
                      SubmitButtonTypeSet?.has(value.type) ||
                      BusinessButtonTypeSet?.has(value.type)
                    ) {
                      item.componentName = 'BUTTON';
                    }
                    const insertNode = target?.node?.document?.createNode(item);
                    target.node?.children?.insert(insertNode!, 0);
                  });
                  return null;
                },
                (pre: IPublicModelNode, next: IPublicModelNode) => {
                  if (!value.sort?.length) return 0;
                  const searchPath = 'dslInfo.schema';
                  const firstChildren = pre.children?.get(0);
                  const firstValue =
                    firstChildren?.getPropValue(searchPath) ||
                    pre.getPropValue(searchPath) ||
                    pre?.id;
                  const nextChildren = next.children?.get(0);
                  const nextValue =
                    nextChildren?.getPropValue(searchPath) ||
                    next.getPropValue(searchPath) ||
                    next?.id;
                  const indexPre = value.sort.indexOf(firstValue);
                  const indexNext = value.sort.indexOf(nextValue);
                  return indexPre - indexNext;
                },
              );
              // 修改的
              value.waitUpdate?.forEach((item) => {
                const value = item.children[0]?.props?.dslInfo;
                item.children[0].componentName = value.type;
                if (
                  SubmitButtonTypeSet?.has(value.type) ||
                  BusinessButtonTypeSet?.has(value.type)
                ) {
                  item.children[0].componentName = 'BUTTON';
                }
                const val = target.node?.children?.find((x) => x.id === item.id);
                val?.replaceWith({ ...item });
              });
            },
            setter: {
              componentName: 'AthTableFormEditSetter',
              isDynamic: false,
              props: {
                type: 'table',
              },
            },
          },

          // 数据源
          { ...commonDataSourceNamesSetter },
          {
            ...commonDataConnectorSetter,
            setter: {
              componentName: 'LcdpDataConnectorSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-数据源',
                    tooltip: {
                      title:
                        'dj-分页场景下后端API需要支持表头筛选、表头排序、高级查询、多条件排序、统计项等后端功能',
                    },
                  },
                  componentType: 'lang',
                },
              },
            },
          },

          // 标题
          {
            name: 'dslInfo.lang.tableTitle',
            setValue: (target, value) => {
              target?.node?.setPropValue('dslInfo.tableTitle', value['zh_CN']);
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-标题',
                  },
                  componentType: 'lang',
                },
              },
            },
          },

          // 操作列
          {
            condition: (target) => {
              const getConfigByKey = getAppHelperUtils(target.node!, 'getConfigByKey');
              const version = getConfigByKey('AthVersion');
              return version === '2.0';
            },
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return dslInfo;
            },
            setter: {
              isDynamic: false,
              componentName: 'LcdpLibTableOperationSetter',
            },
          },
          {
            condition: (target) => {
              const getConfigByKey = getAppHelperUtils(target.node!, 'getConfigByKey');
              const version = getConfigByKey('AthVersion');
              return version === '1.0';
            },
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return dslInfo;
            },
            setter: {
              isDynamic: false,
              componentName: 'LcdpTableOperationSetter',
              props: {
                componentProps: {
                  columnsTitle: 'dj-表格行操作',
                  showColumns: true, // 是否显示列操作
                  allTitle: 'dj-表格操作',
                  showAll: false, // 是否整体操作
                },
              },
            },
          },

          // 合计行
          {
            name: 'dslInfo.setting.groupSummary.options',
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return dslInfo?.setting?.groupSummary?.options ?? [];
            },
            // setValue: (target, value) => {
            //   target?.node?.setPropValue('dslInfo.setting.groupSummary.options', value);
            // },
            setter: {
              componentName: 'AthGroupSummarySetter',
              isDynamic: false,
            },
          },
          {
            name: 'dslInfo.setting.groupSummary.summaryMode',
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return dslInfo?.setting?.groupSummary?.summaryMode ?? 0;
            },
            setter: {
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-合计模式',
                  },
                  componentType: 'select',
                  componentProps: {
                    options: [
                      {
                        label: '同行合计',
                        value: 0,
                      },
                      {
                        label: '各列分别合计',
                        value: 1,
                      },
                    ],
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.setting.groupSummary.summaryText',
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return dslInfo?.setting?.groupSummary?.summaryText ?? '';
            },
            // setter: {
            //   componentName: 'StringSetter',
            //   initialValue: '',
            // },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-合计文本',
                  },
                },
              },
            },
          },
          {
            setter: {
              componentName: 'AthMonacoEditorSetter',
              isDynamic: false,
              props: {
                options: {
                  showText: '配置',
                  // buttonProps: {
                  //   type: 'primary',
                  //   size: 'small',
                  // },
                  monacoEditorProps: {
                    type: 'json',
                    title: '配置</>',
                  },
                },
              },
            },
            name: 'dslInfo.setting.groupSummary',
          },

          // 隐藏新增空白行
          {
            name: 'dslInfo.suppressAutoAddRow',
            condition: (target) => {
              const getConfigByKey = getAppHelperUtils(target.node!, 'getConfigByKey');
              const version = getConfigByKey('AthVersion');
              return version === '1.0';
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-隐藏新增空白行',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
        ],
      },

      // 关联字段
      { ...commonAthAssociationFieldSetter },

      // 基础设置
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          // 表格分组
          {
            condition: (target) => {
              const getConfigByKey = getAppHelperUtils(target.node!, 'getConfigByKey');
              const version = getConfigByKey('AthVersion');
              return version === '1.0';
            },
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              const groupOption =
                dslInfo?.setting?.options?.find((option) => {
                  return option.type === 'group';
                }) ?? defaultGroupSettingFieldData;

              return groupOption;
            },
            setValue: (target, value) => {
              const options =
                target?.node
                  ?.getPropValue('dslInfo.setting.options')
                  ?.filter((item) => item.type !== 'group') || [];
              options.push(value);
              target?.node?.setPropValue('dslInfo.setting.options', options);
            },
            setter: {
              componentName: 'AthGroupSettingFieldSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-分组字段',
                  },
                },
              },
              isDynamic: false,
            },
          },
          {
            setter: {
              componentName: 'AthMonacoEditorSetter',
              isDynamic: false,
              props: {
                options: {
                  showText: '摘要',
                  // buttonProps: {
                  //   type: 'primary',
                  //   size: 'small',
                  // },
                  monacoEditorProps: {
                    type: 'json',
                    title: '摘要</>',
                  },
                },
              },
            },
            condition: (target: IPublicModelSettingField) => {
              const getConfigByKey = getAppHelperUtils(target.node!, 'getConfigByKey');
              const version = getConfigByKey('AthVersion');
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return (
                !!dslInfo?.setting?.options?.find((option) => {
                  return option.type === 'group';
                }) && version === '1.0'
              );
            },

            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              const groupOption =
                (dslInfo?.setting?.options ?? []).find((option) => {
                  return option.type === 'group';
                }) ?? {};

              return groupOption.summaryTypes ?? [];
            },
            setValue: (target, value) => {
              const options =
                target?.node?.getPropValue('dslInfo.setting.options')?.map((option) => {
                  if (option.type === 'group') {
                    return {
                      ...option,
                      summaryTypes: value,
                    };
                  }
                  return option;
                }) ?? [];
              target?.node?.setPropValue('dslInfo.setting.options', options);
            },
          },

          // 勾选列
          {
            name: 'dslInfo.checkbox',
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-勾选列',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },

          // 序号列
          {
            name: 'dslInfo.rowIndex',
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-序号列',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },

          // 删除行
          {
            name: 'dslInfo.rowDelete',
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-删除行',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },

          // 状态列
          {
            condition: (target: IPublicModelSettingField) => {
              const getConfigByKey = getAppHelperUtils(target.node!, 'getConfigByKey');
              const version = getConfigByKey('AthVersion');

              const dslInfo = target?.node?.getPropValue('dslInfo');
              const athLowCodeConfig = target?.node?.document?.root?.getPropValue(
                'athLowCodeConfig',
              );
              const { path = '', schema = '' } = dslInfo;
              const { AthFieldTree, AthStatusInfo } = athLowCodeConfig;
              const { path: rootPath, data_name: rootSchema } = AthFieldTree?.[0] || {};

              return (
                !isEmpty(AthStatusInfo) &&
                path === rootPath &&
                schema === rootSchema &&
                version === '1.0'
              );
            },
            setter: {
              isDynamic: false,
              componentName: 'AthManageStatusSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-状态列',
                  },
                },
              },
            },
          },

          // 合并单元格
          {
            name: 'dslInfo.rowSpanTree',
            condition: (target) => {
              const getConfigByKey = getAppHelperUtils(target.node!, 'getConfigByKey');
              const version = getConfigByKey('AthVersion');
              return version === '1.0';
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-合并单元格',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },

          // 分页设置
          {
            getValue: (target) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return !!dslInfo.queryInfo.pageInfo;
            },
            setValue: (target, value) => {
              target?.node?.setPropValue(
                'dslInfo.queryInfo',
                value
                  ? {
                      pageInfo: {
                        pageSize: 50,
                      },
                    }
                  : {},
              );
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-后端分页',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.queryInfo.pageInfo.pageSize',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return !!dslInfo?.queryInfo?.pageInfo;
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  componentType: 'number',
                  titleProps: {
                    setterTitle: 'dj-每页条数',
                  },
                  componentProps: {
                    disabled: false,
                    addonAfter: '条',
                    max: 100,
                    min: 10,
                    step: 10,
                  },
                  formItemRules: [
                    {
                      type: 'number',
                      min: 10,
                      max: 100,
                    },
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
              // initialValue: 50,
            },
          },
        ],
      },

      // 工具栏
      {
        title: 'dj-工具栏',
        type: 'group',
        display: 'accordion',
        items: [
          // 表格操作
          {
            condition: (target) => {
              const getConfigByKey = getAppHelperUtils(target.node!, 'getConfigByKey');
              const version = getConfigByKey('AthVersion');
              return version === '2.0';
            },
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return dslInfo;
            },
            setter: {
              isDynamic: false,
              componentName: 'LcdpLibTableToolsSetter',
            },
          },
          {
            condition: (target) => {
              const getConfigByKey = getAppHelperUtils(target.node!, 'getConfigByKey');
              const version = getConfigByKey('AthVersion');
              return version === '1.0';
            },
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return dslInfo;
            },
            setter: {
              isDynamic: false,
              componentName: 'LcdpTableOperationSetter',
              props: {
                componentProps: {
                  columnsTitle: 'dj-表格行操作',
                  showColumns: false, // 是否显示列操作
                  allTitle: 'dj-表格操作',
                  showAll: true, // 是否整体操作
                },
              },
            },
          },

          // 高级查询
          {
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return dslInfo;
            },
            setter: {
              componentName: 'AthSearchInfoSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-高级查询',
                  },
                },
              },
            },
          },

          // 多条件排序
          {
            name: 'dslInfo.isSort',
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-多条件排序',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return dslInfo?.setting?.orderFields ?? [];
            },
            setValue: (target, value) => {
              target?.node?.setPropValue('dslInfo.setting.orderFields', value);
            },
            setter: {
              componentName: 'AthOrderFieldsSetter',
              isDynamic: false,
              props: {
                options: {
                  propName: 'orderFields',
                  titleProps: {
                    setterTitle: 'dj-二次排序',
                  },
                },
              },
            },
          },

          // 行高设定
          {
            name: 'dslInfo.openRowHeight',
            condition: (target) => {
              const getConfigByKey = getAppHelperUtils(target.node!, 'getConfigByKey');
              const version = getConfigByKey('AthVersion');
              return version === '1.0';
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-行高设定',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            condition: (target) => {
              const getConfigByKey = getAppHelperUtils(target.node!, 'getConfigByKey');
              const version = getConfigByKey('AthVersion');
              return version === '2.0';
            },
            getValue: (target) => {
              const hideDefaultToolbar = target?.node?.getPropValue(
                'dslInfo.setting.hideDefaultToolbar',
              );
              if (hideDefaultToolbar?.length && hideDefaultToolbar.includes('row-height-tool')) {
                return false;
              } else {
                return true;
              }
            },
            setValue: (target, value) => {
              const hideDefaultToolbar =
                target?.node?.getPropValue('dslInfo.setting.hideDefaultToolbar') || [];
              if (!value) {
                hideDefaultToolbar.push('row-height-tool');
                target?.node?.setPropValue(
                  'dslInfo.setting.hideDefaultToolbar',
                  hideDefaultToolbar,
                );
              } else {
                const index = hideDefaultToolbar.findIndex((item) => item === 'row-height-tool');
                hideDefaultToolbar.splice(index, 1);
                target?.node?.setPropValue(
                  'dslInfo.setting.hideDefaultToolbar',
                  hideDefaultToolbar,
                );
              }
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-行高设定',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },

          // 列宽自适应
          {
            condition: (target) => {
              const getConfigByKey = getAppHelperUtils(target.node!, 'getConfigByKey');
              const version = getConfigByKey('AthVersion');
              return version === '2.0';
            },
            getValue: (target) => {
              const hideDefaultToolbar = target?.node?.getPropValue(
                'dslInfo.setting.hideDefaultToolbar',
              );
              if (hideDefaultToolbar?.length && hideDefaultToolbar.includes('layout-setting')) {
                return false;
              } else {
                return true;
              }
            },
            setValue: (target, value) => {
              const hideDefaultToolbar =
                target?.node?.getPropValue('dslInfo.setting.hideDefaultToolbar') || [];
              if (!value) {
                hideDefaultToolbar.push('layout-setting');
                target?.node?.setPropValue(
                  'dslInfo.setting.hideDefaultToolbar',
                  hideDefaultToolbar,
                );
              } else {
                const index = hideDefaultToolbar.findIndex((item) => item === 'layout-setting');
                hideDefaultToolbar.splice(index, 1);
                target?.node?.setPropValue(
                  'dslInfo.setting.hideDefaultToolbar',
                  hideDefaultToolbar,
                );
              }
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-列宽自适应',
                    layout: 'horizontal',
                    tooltip: { title: 'dj-与行高设定合并一同展示，可按表格字段类型自适应列宽' },
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },

          // 表格设定
          {
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return !dslInfo?.setting?.hideDefaultToolbar?.find(() => 'setting');
            },
            setValue: (target, value) => {
              const hideDefaultToolbar =
                target?.node
                  ?.getPropValue('dslInfo.setting.hideDefaultToolbar')
                  ?.filter((item) => item !== 'setting') ?? [];
              if (!value) hideDefaultToolbar.push('setting');
              target?.node?.setPropValue('dslInfo.setting.hideDefaultToolbar', hideDefaultToolbar);
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-表格设定',
                    layout: 'horizontal',
                    tooltip: { title: 'dj-用户可设置表头字段显隐/顺序/冻结' },
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
        ],
      },

      // 尺寸
      {
        title: 'dj-尺寸',
        type: 'group',
        display: 'accordion',
        items: [
          // 表格高度
          {
            name: 'dslInfo.adaptiveModel',
            setValue: (target, value) => {
              target?.node?.setPropValue('dslInfo.adaptiveModel', value);
              if (value === 'fixedHigh') {
                target?.node?.setPropValue('dslInfo.height', 200);
              } else {
                const { height, ...rest } = target.node?.getPropValue('dslInfo');
                target.node?.setPropValue('dslInfo', rest);
              }
            },
            setter: {
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-表格高度',
                  },
                  componentType: 'select',
                  componentProps: {
                    options: [
                      { label: 'dj-填充容器', value: 'default' },
                      { label: 'dj-固定高度', value: 'fixedHigh' },
                      { label: 'dj-适应内容', value: 'rowSize' },
                    ],
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.height',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return dslInfo?.adaptiveModel === 'fixedHigh';
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-固定高度',
                  },
                  componentType: 'number',
                  componentProps: {
                    disabled: false,
                    addonAfter: 'px',
                    max: 800,
                    min: 200,
                    step: 10,
                  },
                  formItemRules: [
                    {
                      type: 'number',
                      min: 200,
                      max: 800,
                    },
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },

          // 默认行高
          {
            name: 'dslInfo.rowHeight',
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  componentType: 'number',
                  titleProps: {
                    setterTitle: 'dj-默认行高',
                  },
                  componentProps: {
                    disabled: false,
                    addonAfter: 'px',
                    max: 200,
                    min: 40,
                    step: 10,
                  },
                  formItemRules: [
                    {
                      type: 'number',
                      min: 40,
                      max: 200,
                    },
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },

          // 保存用户列宽
          {
            name: 'dslInfo.saveColumnsWidth',
            condition: (target) => {
              const getConfigByKey = getAppHelperUtils(target.node!, 'getConfigByKey');
              const version = getConfigByKey('AthVersion');
              return version === '1.0';
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-保存用户列宽',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
        ],
      },

      // 全局设置
      { ...commonAthMonacoEditorSetter },

      // {
      //   title: 'dj-操作设置（关联属性）',
      //   display: 'accordion',
      //   getValue: (target) => {
      //     const dslInfo = target?.node?.getPropValue('dslInfo');
      //     return dslInfo;
      //     // return target.node?.propsData;
      //   },
      //   setter: {
      //     isDynamic: false,
      //     componentName: 'AthOperationsSetter',
      //     props: {
      //       eventSourceType: 'operation',
      //     },
      //   },
      // },
      // {
      //   title: 'dj-操作设置（关联属性）',
      //   display: 'accordion',
      //   getValue: (target) => {
      //     const dslInfo = target?.node?.getPropValue('dslInfo');
      //     return dslInfo;
      //   },
      //   setter: {
      //     isDynamic: false,
      //     componentName: 'LcdpTableOperationSetter',
      //     props: {
      //       componentProps: {
      //         columnsTitle: 'dj-表格行操作',
      //         showColumns: true, // 是否显示列操作
      //         allTitle: 'dj-表格操作',
      //         showAll: true, // 是否整体操作
      //       },
      //     },
      //   },
      // },
      // {
      //   title: 'dj-状态列（关联属性）',
      //   display: 'accordion',
      //   condition: (target: IPublicModelSettingField) => {
      //     const dslInfo = target?.node?.getPropValue('dslInfo');
      //     const athLowCodeConfig = target?.node?.document?.root?.getPropValue('athLowCodeConfig');
      //     const { path = '', schema = '' } = dslInfo;
      //     const { AthFieldTree, AthStatusInfo } = athLowCodeConfig;
      //     const { path: rootPath, data_name: rootSchema } = AthFieldTree?.[0];
      //     return !isEmpty(AthStatusInfo) && path === rootPath && schema === rootSchema;
      //   },
      //   setter: {
      //     isDynamic: false,
      //     componentName: 'AthManageStatusSetter',
      //   },
      // },
    ],
    component: {
      isContainer: true,
      nestingRule: {
        childWhitelist: childWhitelistMap.get(AthenaComponentType.ATHENA_TABLE) ?? [],
      },
    },
    advanced: {
      initialChildren: [
        {
          componentName: AthenaComponentType.DYNAMIC_OPERATION,
          title: '动态操作',
          props: {
            dslInfo: {
              type: AthenaComponentType.DYNAMIC_OPERATION,
              select: 'slot-top-right',
              height: 140,
              group: [],
            },
          },
        },
      ],
      callbacks: {
        onNodeAdd: (addedNode, currentNode) => {
          // console.log('currentNode:', currentNode);
          // addedNode?.setPropValue('type', 'normal');
          if (!addedNode) return;
          const addedNodeSchema = addedNode?.exportSchema(IPublicEnumTransformStage.Save);
          if (!addedNodeSchema) return;

          const dslInfo = addedNode.getPropValue('dslInfo');

          let layoutPNode = null;
          if (addedNode.componentName !== AthenaComponentType.DYNAMIC_OPERATION) {
            // 拖入的组件是否为操作列
            const isOperation: boolean = isDynamicComponent(dslInfo?.type);

            // 为目标元素包裹一层P
            layoutPNode = currentNode?.document?.createNode({
              componentName: 'TABLE_GROUP',
              props: {
                dslInfo: {
                  headerName: isOperation ? '操作' : dslInfo?.headerName ?? '分组new',
                  width: 160,
                  path: dslInfo.path,
                  level: 0,
                  lang: {
                    headerName: isOperation
                      ? { zh_CN: '操作', zh_TW: '操作', en_US: 'operation' }
                      : dslInfo?.lang?.headerName,
                  },
                },
              },
              children: [addedNodeSchema],
            });
            // 需要setTimeout处理(官方demo...)
            setTimeout(() => {
              currentNode?.replaceChild(
                addedNode,
                layoutPNode!.exportSchema(IPublicEnumTransformStage.Save),
              );
            }, 1);
          }
        },
      },
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-表格',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/TABLE.png`,
    schema: {
      componentName: AthenaComponentType.ATHENA_TABLE,
      props: {
        dslInfo: {
          tableTitle: '表格',
          lang: {
            tableTitle: {
              zh_CN: '表格',
              zh_TW: '表格',
              en_US: 'table',
            },
          },
          type: AthenaComponentType.ATHENA_TABLE,
          schema: '',
          path: '',
          isSort: true,
          checkbox: false,
          rowDelete: false,
          rowIndex: true,
          id: '',
          saveColumnsWidth: true,
          suppressAutoAddRow: false,
          // checkboxOperation: false,
          // height: 200,
          rowHeight: 100,
          adaptiveModel: 'default',
          rowSpanTree: false,
          queryInfo: {
            pageInfo: {
              pageSize: 50,
            },
            dataFilter: {
              dataSourceNames: [],
              apiCondition: {},
            },
            isAsync: true,
          },
          setting: {
            hideDefaultToolbar: [],
            options: [],
          },
        },
      },
    },
  },
];

export default {
  ...LcdpAthenaTableMeta,
  snippets,
};
