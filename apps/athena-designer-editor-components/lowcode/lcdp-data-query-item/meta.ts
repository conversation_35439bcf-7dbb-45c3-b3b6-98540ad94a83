import {
  IPublicModelSettingField,
  IPublicTypeComponentMetadata,
  IPublicTypeSnippet,
} from '@alilc/lowcode-types';
import { LcdpComponentType } from '../common/common.config';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonBasicSetter,
} from '../common/common-meta-info.config';
import { getAppHelperUtils } from "@/tools";

const dataQueryItemAssociationFieldSetter = {
  ...commonAthAssociationFieldSetter,
  title: 'dj-统计项关联字段',
  condition: (target: IPublicModelSettingField) => {
    const parentDslInfo = target.node?.parent?.getPropValue('dslInfo');
    return !!parentDslInfo?.showAccount;
  },
};

const LcdpDataQueryItemMeta: IPublicTypeComponentMetadata = {
  componentName: LcdpComponentType.DATA_QUERY_ITEM,
  title: 'dj-数据查询页签',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpDataQueryItem',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      {
        name: 'dslInfo.lang.viewName',
        setValue: (target, value) => {
          target?.node?.setPropValue('dslInfo.viewName', value['zh_CN']);
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-标题',
              },
              componentType: 'lang',
            },
          },
        },
      },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          {
            getValue: (target) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return dslInfo?.outletConfig?.pageDefine;
            },
            setValue: (target, value) => {
              const { pageCode, code } = value;
              target?.node?.setPropValue('dslInfo.outletConfig.pageDefine', {
                pageCode,
                code,
              });
            },
            setter: {
              componentName: 'LcdpSubpageSelectorSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-关联子页面',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.outletConfig',
            setter: {
              componentName: 'LcdpOutletSelectorSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-目标出口',
                    tooltip: {
                      title: 'dj-当前页签关联的子页面通过目标出口显示',
                    },
                  },
                },
              },
            },
          },
          {
            condition: (target: IPublicModelSettingField) => {
              const parentDslInfo = target.node?.parent?.getPropValue('dslInfo');
              return parentDslInfo.viewType === 'card';
            },
            getValue: (target) => {
              const getConfigByKey = getAppHelperUtils(target.node!, 'getConfigByKey');
              const version = getConfigByKey('AthVersion');
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return { src: dslInfo?.image?.src, useDMC: version === '1.0' };
            },
            setValue: (target, value) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              target?.node?.setPropValue('dslInfo.image', {
                src: value,
                title: dslInfo.viewName,
              });
            },
            setter: {
              isDynamic: false,
              componentName: 'LcdpImageSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-卡片图标',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.isDefault',
            setValue: (target, value) => {
              if (value) {
                target.node?.parent?.children?.forEach((child) => {
                  child.setPropValue('dslInfo.isDefault', false);
                });
              }
              target?.node?.setPropValue('dslInfo.isDefault', value);
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-默认项',
                    layout: 'horizontal',
                    tooltip: {
                      title: 'dj-即当前数据查询组件下默认选中的页签',
                    },
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
        ],
      },
      { ...dataQueryItemAssociationFieldSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {},
    },
    advanced: {},
  },
};
export const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-数据查询页签',
    screenshot: '',
    schema: {
      componentName: LcdpComponentType.DATA_QUERY_ITEM,
      title: 'dj-数据查询页签',
      props: {
        dslInfo: {
          id: '',
          schema: '',
          path: '',
          viewCode: '',
          viewName: '页签',
          lang: {
            viewName: {
              zh_CN: '页签',
              zh_TW: '頁簽',
              en_US: 'tab',
            },
          },
          isDefault: false,
          isShow: true,
          outletConfig: {
            targetId: '',
            url: 'subpage/show',
            pageDefine: {
              pageCode: 'sub-page',
              code: '',
            },
          },
          image: {
            src:
              'https://dmc-test.digiwincloud.com.cn/api/dmc/v2/file/Athena/share/9e5947d3-828f-4749-934c-df7bb49ff303',
            title: 'tab',
          },
        },
      },
    },
  },
];

export default {
  ...LcdpDataQueryItemMeta,
  snippets,
};
