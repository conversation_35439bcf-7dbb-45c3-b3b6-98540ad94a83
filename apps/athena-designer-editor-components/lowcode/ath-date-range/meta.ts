import {
  IPublicModelSettingField,
  IPublicTypeComponentMetadata,
  IPublicTypeSnippet,
} from '@alilc/lowcode-types';
import { AthenaComponentType } from '../common/common.config';
import { AthenaDataType } from '../common/common.type';
import {
  commonAthMonacoEditorSetter,
  commonAthRuleSetter,
  tipSetter,
  commonBasicSetter,
  commonDataTypeSetter,
  commonLibRuleSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const AthDateRangeMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.DATE_RANGE,
  title: 'dj-日期区间',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonColumn',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      {
        title: 'dj-关联字段',
        type: 'group',
        display: 'accordion',
        items: [
          {
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              const { schemas } = dslInfo;
              return !schemas ? 'simple' : 'minute';
            },
            setValue: (target, value) => {
              if (value === 'simple') {
                target?.node?.setPropValue('dslInfo.schema', '');
                target?.node?.setPropValue('dslInfo.schemas', null);
                target?.node?.setPropValue('dslInfo.path', '');
              } else {
                target?.node?.setPropValue('dslInfo.schema', '');
                target?.node?.setPropValue('dslInfo.schemas', []);
                target?.node?.setPropValue('dslInfo.path', '');
              }
            },
            setter: {
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-日期保存方式',
                    tooltip: {
                      title:
                        'dj-控制日期区间组件使用单字段或双字段保存开始时间和结束时间，合并保存时需要数组类型',
                    },
                  },
                  componentType: 'select',
                  componentProps: {
                    options: [
                      { label: 'dj-起止时间合并保存', value: 'simple' },
                      { label: 'dj-起止时间分开保存', value: 'minute' },
                    ],
                  },
                },
              },
            },
          },
          // 单schema时显示配置
          {
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              const { schema, path } = dslInfo;
              return { schema, path };
            },
            setValue: (target, value) => {
              const { schema, path } = value;
              target?.node?.setPropValue('dslInfo.schema', schema);
              target?.node?.setPropValue('dslInfo.path', path);
            },
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return !dslInfo?.schemas;
            },
            setter: {
              componentName: 'AthSelectAssociationFieldSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-关联字段',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.schema',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return !dslInfo?.schemas;
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'schema',
                  },
                  formItemRules: [
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
          // 多schema时显示配置
          {
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              const { schemas, path } = dslInfo;
              return { schema: schemas?.[0], path };
            },
            setValue: (target, value) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              const schemas = dslInfo?.schemas;
              const { schema, path } = value;
              target?.node?.setPropValue('dslInfo.schema', schema);
              schemas[0] = schema;
              target?.node?.setPropValue('dslInfo.schemas', schemas);
              target?.node?.setPropValue('dslInfo.path', path);
            },
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return !!dslInfo?.schemas;
            },
            setter: {
              componentName: 'AthSelectAssociationFieldSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-开始时间关联字段',
                  },
                  selectProps: {
                    controlLevel: true,
                  },
                },
              },
            },
          },
          {
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              const { schemas } = dslInfo;
              return schemas?.[0];
            },
            setValue: (target, value) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              const schemas = dslInfo?.schemas;
              target?.node?.setPropValue('dslInfo.schema', value);
              schemas[0] = value;
              target?.node?.setPropValue('dslInfo.schemas', schemas);
            },
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return !!dslInfo?.schemas;
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-开始时间schema',
                  },
                  formItemRules: [
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
          {
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              const { schemas, path } = dslInfo;
              return { schema: schemas?.[1], path };
            },
            setValue: (target, value) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              const schemas = dslInfo?.schemas;
              const { schema, path } = value;
              schemas[1] = schema;
              target?.node?.setPropValue('dslInfo.schemas', schemas);
              target?.node?.setPropValue('dslInfo.path', path);
            },
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return !!dslInfo?.schemas;
            },
            setter: {
              componentName: 'AthSelectAssociationFieldSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-结束时间关联字段',
                  },
                  selectProps: {
                    controlLevel: true,
                  },
                },
              },
            },
          },
          {
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              const { schemas } = dslInfo;
              return schemas?.[1];
            },
            setValue: (target, value) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              const { schemas } = dslInfo;
              schemas[1] = value;
              target?.node?.setPropValue('dslInfo.schemas', schemas);
            },
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return !!dslInfo?.schemas;
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-结束时间schema',
                  },
                  formItemRules: [
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
          {
            name: 'dslInfo.path',
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'path',
                  },
                },
              },
            },
          },
        ],
      },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          { ...commonBasicSetter.placeholder }, // 占位提示
          ...tipSetter, // 辅助提示+注释说明+注释图标
          { ...commonBasicSetter.status }, // 是否启用
          { ...commonBasicSetter.isFocusDisplay }, // 启用标题在内
          {
            name: 'dslInfo.dataType',
            setValue: (target, value) => {
              console.log('value', value);
              if (value === AthenaDataType.DATE) {
                target?.node?.setPropValue('dslInfo.mode', 'date');
                target?.node?.setPropValue('dslInfo.formatConfig', {
                  type: 'yyyy/MM/dd',
                  value: 'yyyy/MM/dd',
                });
                target?.node?.setPropValue('dslInfo.format', 'yyyy/MM/dd');
              } else if (value === AthenaDataType.DATETIME) {
                target?.node?.setPropValue('dslInfo.mode', 'date-second');
                target?.node?.setPropValue('dslInfo.formatConfig', {
                  type: 'yyyy/MM/dd HH:mm:ss',
                  value: 'yyyy/MM/dd HH:mm:ss',
                });
                target?.node?.setPropValue('dslInfo.format', 'yyyy/MM/dd HH:mm:ss');
              }
              target?.node?.setPropValue('dslInfo.dataType', value);
            },
            setter: {
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-数据类型',
                  },
                  componentType: 'select',
                  componentProps: {
                    options: [
                      { label: AthenaDataType.DATE, value: AthenaDataType.DATE },
                      { label: AthenaDataType.DATETIME, value: AthenaDataType.DATETIME },
                    ],
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.mode',
            getValue(target) {
              const { dataType, mode } = target?.node?.getPropValue('dslInfo');
              return { dataType, mode };
            },
            setValue: (target, value) => {
              const formatConfig = target?.node?.getPropValue('dslInfo.formatConfig');
              const defaultFormatValue = {
                year: 'yyyy',
                month: 'yyyy/MM',
                week: 'yyyy/ww',
                date: 'yyyy/MM/dd',
                'date-minute': 'yyyy/MM/dd HH:mm',
                'date-second': 'yyyy/MM/dd HH:mm:ss',
              };
              const defaultValue = defaultFormatValue[value];
              target?.node?.setPropValue('dslInfo.format', defaultValue);
              target?.node?.setPropValue('dslInfo.formatConfig', {
                ...formatConfig,
                value: defaultValue,
                type: defaultValue,
              });
              // 非日期型的，隐藏快捷选项,
              if (!['date', 'date-minute', 'date-second']?.includes(value)) {
                target?.node?.setPropValue('dslInfo.showPickerOptions', false);
                target?.node?.setPropValue('dslInfo.pickerOptions', []);
                target?.node?.setPropValue('dslInfo.disabledDate', {
                  type: 'none',
                });
              }
            },
            setter: {
              componentName: 'LcdpDateModeSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-日期格式',
                  },
                  componentType: 'select',
                  componentProps: {},
                },
              },
            },
          },
          {
            getValue(target) {
              const { formatConfig, mode } = target?.node?.getPropValue('dslInfo');
              return { ...formatConfig, mode };
            },
            setValue: (target, value) => {
              target?.node?.setPropValue('dslInfo.formatConfig', value);
              target?.node?.setPropValue('dslInfo.format', value?.value);
            },
            setter: {
              componentName: 'AthPickerDisplaySetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-展示样式',
                  },
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            getValue(target) {
              const { disabledDate, mode } = target?.node?.getPropValue('dslInfo');
              return { ...disabledDate, mode };
            },
            name: 'dslInfo.disabledDate',
            setter: {
              componentName: 'AthPickerDisabledSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-可选日期范围',
                  },
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.showPickerOptions',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return ['date', 'date-minute', 'date-second']?.includes(dslInfo?.mode);
            },
            setValue: (target, value) => {
              // 默认值 最近7天
              let pickerOptions: string[] = [];
              const mode = target?.node?.getPropValue('dslInfo.mode');
              if (value && ['date', 'date-minute', 'date-second']?.includes(mode)) {
                pickerOptions = ['lastSevenDays'];
              }
              target?.node?.setPropValue('dslInfo.pickerOptions', pickerOptions);
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-快捷选项',
                    layout: 'horizontal',
                    tooltip: { title: 'dj-启用后，用户页面可快捷选择' },
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.pickerOptions',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return !!dslInfo?.showPickerOptions;
            },
            setter: {
              componentName: 'AthPickerOptionsSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {},
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
        ],
      },
      { ...commonLibRuleSetter },
      { ...commonAthRuleSetter },
      { ...commonAthMonacoEditorSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        // parentWhitelist: parentWhitelistMap.get(AthenaComponentType.DATE_RANGE) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-日期区间',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/DATE_RANGE.svg`,
    schema: {
      componentName: AthenaComponentType.DATE_RANGE,
      title: 'dj-日期区间',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.DATE_RANGE,
          headerName: '日期区间',
          placeholder: 'yyyyMMdd',
          schema: '',
          schemas: ['', ''],
          path: '',
          disabled: false,
          editable: true,
          isFocusDisplay: false,
          fieldType: 'datetime',
          dataType: AthenaDataType.DATE,
          tooltipMode: 'normal', // 有注释iconType的都要加
          iconType: 'wenhao',
          weekStartsOn: 1, // 周日为一周的第一天，0表示周日，1表示周一，需要传给运行态
          mode: 'date',
          format: 'yyyy/MM/dd',
          formatConfig: {
            type: 'yyyy/MM/dd',
            value: 'yyyy/MM/dd',
          },
          showPickerOptions: false,
          pickerOptions: [],
          disabledDate: {
            type: 'none',
          },
          rules: { value: [] },
          lang: {
            headerName: {
              zh_CN: '日期区间',
              zh_TW: '日期區間',
              en_US: 'Date Range',
            },
            placeholder: {
              zh_CN: 'yyyyMMdd',
              zh_TW: 'yyyyMMdd',
              en_US: 'yyyyMMdd',
            },
            extraContent: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
            tooltipTitle: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
          },
        },
      },
    },
  },
];

export default {
  ...AthDateRangeMeta,
  snippets,
};
