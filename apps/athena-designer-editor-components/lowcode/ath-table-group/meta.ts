import {
  IPublicTypeComponentMetadata,
  IPublicTypeSnippet,
  IPublicEnumTransformStage,
} from '@alilc/lowcode-types';
import { AthenaComponentType, childWhitelistMap } from '../common/common.config';

const AthTableGroupMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.TABLE_GROUP,
  title: 'dj-ath表格分组',
  group: 'dj-标准组件',
  category: 'dj-容器组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'AthTableGroup',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
      // condition: true,
    },
    props: [
      {
        title: { type: 'i18n', 'zh-CN': '标题', 'en-US': 'title' },
        name: 'dslInfo.lang.headerName',
        setValue: (target, value) => {
          target?.node?.setPropValue('dslInfo.headerName', value['zh_CN']);
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-标题',
              },
              componentType: 'lang',
            },
          },
        },
      },
      {
        title: {
          label: {
            type: 'i18n',
            'en-US': 'path',
            'zh-CN': 'path',
          },
        },
        name: 'dslInfo.path',
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'path',
              },
            },
          },
        },
      },
      {
        title: { type: 'i18n', 'zh-CN': '宽度', 'en-US': 'width' },
        name: 'dslInfo.width',
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              componentType: 'number',
              titleProps: {
                setterTitle: 'dj-宽度',
              },
              componentProps: {
                disabled: false,
                addonAfter: 'px',
                max: 600,
                min: 10,
                step: 10,
              },
              formItemRules: [
                {
                  type: 'number',
                  max: 600,
                  min: 10,
                },
                {
                  required: true,
                  message: '不可为空',
                },
              ],
            },
          },
        },
      },
      {
        title: { type: 'i18n', 'zh-CN': '禁用自动填充', 'en-US': 'suppressFillHandle' },
        name: 'dslInfo.suppressFillHandle',
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-禁用自动填充',
              },
              componentType: 'switch',
              componentProps: {
                size: 'small',
              },
            },
          },
        },
      },
      {
        title: { type: 'i18n', 'zh-CN': '设为冻结列', 'en-US': 'setFrozenColumn' },
        name: 'dslInfo.pinned',
        setValue: (target, value) => {
          target?.node?.setPropValue('dslInfo.pinned', value);
          const tableNode = target.parent.node.parent.children.find(
            (item) => item.id === target.node.id,
          );
          tableNode.replaceWith(target.node.exportSchema(IPublicEnumTransformStage.Save));
        },
        setter: {
          componentName: 'LcdpLibTablePinnedSetter',
          isDynamic: false,
        },
      },
    ],
    component: {
      isContainer: true,
      nestingRule: {
        childWhitelist: childWhitelistMap.get(AthenaComponentType.TABLE_GROUP) ?? [],
      },
    },
    advanced: {},
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'ath表格分组',
    screenshot: '',
    schema: {
      ignore: true,
      componentName: 'TABLE_GROUP',
      props: {
        dslInfo: {
          headerName: 'ath表格分组',
          width: 160,
          path: '',
          level: 0,
          suppressFillHandle: false,
        },
      },
    },
  },
];

export default {
  ...AthTableGroupMeta,
  snippets,
};
