import {
  IPublicModelSettingField,
  IPublicTypeComponentMetadata,
  IPublicTypeSnippet,
} from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSchemaNotRequiredSetter,
  commonAthRuleSetter,
  commonBasicSetter,
  commonLibRuleSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const AthTextMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.TEXT,
  title: 'dj-文本',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'AthText',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSchemaNotRequiredSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          {
            name: 'dslInfo.lang.textContent',
            setValue: (target, value) => {
              target?.node?.setPropValue('dslInfo.textContent', value['zh_CN']);
            },
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-文本内容',
                    tooltip: { title: 'dj-固定文本内容，未选择关联字段时生效' },
                  },
                  layout: 'vertical',
                  componentType: 'lang',
                  componentProps: {
                    isTextArea: true,
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.ellipsis',
            setValue: (target, value) => {
              if (value) {
                target?.node?.setPropValue('dslInfo.lineClamp', 3);
              } else {
                target?.node?.setPropValue('dslInfo.lineClamp', null);
              }
            },
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-省略模式',
                    tooltip: { title: 'dj-开启后，可设置文本内容最大显示行数' },
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.lineClamp',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return dslInfo?.ellipsis;
            },
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-显示行数',
                  },
                  layout: 'vertical',
                  componentType: 'number',
                  componentProps: {
                    disabled: false,
                    addonAfter: '行',
                    max: 100,
                    min: 1,
                    step: 1,
                    defaultValue: 3,
                  },
                  formItemRules: [
                    {
                      type: 'number',
                      min: 1,
                      max: 100,
                    },
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
        ],
      },
      {
        title: 'dj-文字样式',
        type: 'group',
        display: 'accordion',
        items: [
          {
            name: 'dslInfo.fontSize',
            getValue(target) {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return Number(dslInfo?.fontSize.replace(/px$/, ''));
            },
            setValue(target, value) {
              target?.node?.setPropValue('dslInfo.fontSize', value ? `${value}px` : '');
            },
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-字号',
                  },
                  layout: 'vertical',
                  componentType: 'number',
                  componentProps: {
                    disabled: false,
                    addonAfter: 'px',
                    max: 100,
                    min: 6,
                    step: 1,
                    defaultValue: 14,
                  },
                  formItemRules: [
                    {
                      type: 'number',
                      min: 6,
                      max: 100,
                    },
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
          {
            // name: 'dslInfo.lineHeight',
            getValue(target) {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return Number(dslInfo?.lineHeight.replace(/px$/, ''));
            },
            setValue(target, value) {
              target?.node?.setPropValue('dslInfo.lineHeight', value ? `${value}px` : '');
            },
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-显示行高',
                  },
                  layout: 'vertical',
                  componentType: 'number',
                  componentProps: {
                    disabled: false,
                    addonAfter: 'px',
                    max: 500,
                    min: 6,
                    step: 1,
                    defaultValue: 20,
                  },
                  formItemRules: [
                    {
                      type: 'number',
                      min: 6,
                      max: 500,
                    },
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
          {
            name: 'dslInfo.color',
            setter: {
              isDynamic: false,
              componentName: 'AthColorSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-文字颜色',
                  },
                  layout: 'vertical',
                  optionList: [
                    '#3c3dc1',
                    '#1c1e26',
                    '#45495d',
                    '#7b7f91',
                    '#ffffff',
                    '#e74334',
                    '#f4770f',
                    '#36a834',
                  ],
                },
              },
            },
          },
          {
            name: 'dslInfo.textAlign',
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-对齐',
                  },
                  componentType: 'radioGroup',
                  componentProps: {
                    block: true,
                    buttonStyle: 'outline',
                    optionType: 'button',
                    options: [
                      {
                        value: 'left',
                        tooltip: 'dj-左对齐',
                        icon: 'iconzuoduiqi1',
                      },
                      {
                        value: 'center',
                        tooltip: 'dj-居中对齐',
                        icon: 'iconjuzhongduiqi1',
                      },
                      {
                        value: 'right',
                        tooltip: 'dj-右对齐',
                        icon: 'iconyouduiqi1',
                      },
                      {
                        value: 'justify',
                        tooltip: 'dj-两端对齐',
                        icon: 'iconliangduanduiqi1',
                      },
                    ],
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.underline',
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-下划线',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.lineThrough',
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-删除线',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.fontWeight',
            getValue(target) {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return dslInfo?.fontWeight === 'bold';
            },
            setValue(target, value) {
              target?.node?.setPropValue('dslInfo.fontWeight', value ? 'bold' : 'normal');
            },
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-加粗',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
        ],
      },
      {
        title: 'dj-布局',
        type: 'group',
        display: 'accordion',
        items: [
          {
            name: 'dslInfo.domDisplay',
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-布局模式',
                  },
                  componentType: 'radioGroup',
                  componentProps: {
                    block: true,
                    buttonStyle: 'outline',
                    optionType: 'button',
                    options: [
                      {
                        value: 'inline',
                        tooltip: 'dj-inline:内联显示',
                        icon: 'iconneilianbuju',
                      },
                      {
                        value: 'block',
                        tooltip: 'dj-block:块级显示',
                        icon: 'iconkuaijibuju',
                      },
                    ],
                  },
                },
              },
            },
          },
        ],
      },
      { ...commonLibRuleSetter },
      { ...commonAthRuleSetter },
      { ...commonAthMonacoEditorSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        // parentWhitelist: parentWhitelistMap.get(AthenaComponentType.TEXT) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-文本',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/TEXT.svg`,
    schema: {
      componentName: AthenaComponentType.TEXT,
      title: 'dj-文本',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.TEXT,
          headerName: '文本',
          schema: '',
          path: '',
          textContent: '文本内容显示在此',
          ellipsis: false,
          lineClamp: 3,
          fontSize: '14px',
          lineHeight: '20px',
          color: '#1c1e26',
          textAlign: 'left',
          underline: false,
          lineThrough: false,
          fontWeight: 'normal',
          domDisplay: 'inline',
          lang: {
            headerName: {
              zh_CN: '文本',
              zh_TW: '',
              en_US: 'text',
            },
            textContent: {
              zh_CN: '文本内容显示在此',
              zh_TW: '文本內容顯示在此',
              en_US: 'The text content is displayed here',
            },
          },
        },
      },
    },
  },
];

export default {
  ...AthTextMeta,
  snippets,
};
