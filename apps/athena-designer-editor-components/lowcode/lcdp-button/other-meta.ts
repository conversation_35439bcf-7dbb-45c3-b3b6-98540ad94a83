import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';

export const OtherButtonMeta: { [key: string]: IPublicTypeFieldConfig } = {
  returnText: {
    name: 'dslInfo.action.lang.returnText',
    getValue: (target) => {
      const dslInfo = target.node?.getPropValue('dslInfo');
      return dslInfo?.action?.lang?.returnText ?? [];
    },
    setValue: (target, value) => {
      target?.node?.setPropValue('dslInfo.action.returnText', value?.zh_CN);
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-成功之后的签章文本',
          },
          componentType: 'lang',
        },
      },
    },
  },
  confirm: {
    name: 'dslInfo.confirm.lang.content',
    getValue: (target) => {
      const dslInfo = target.node?.getPropValue('dslInfo');
      return dslInfo?.confirm?.lang?.content ?? [];
    },
    setValue: (target, value) => {
      const defaultInfo = {
        enable: true,
        title: '操作前提示语',
        lang: {
          title: {
            zh_TW: '操作前提示语',
            zh_CN: '操作前提示语',
          },
          content: {
            zh_CN: '',
            zh_TW: '',
            en_US: '',
          },
        },
        content: '',
      };
      defaultInfo.content = value?.zh_CN;
      defaultInfo.lang.content = value;
      target?.node?.setPropValue('dslInfo.confirm', defaultInfo);
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-操作前提示语',
            layout: 'horizontal',
          },
          componentType: 'lang',
        },
      },
    },
  },
  condition: {
    title: '条件',
    type: 'group',
    display: 'accordion',
    items: [
      {
        name: 'dslInfo.condition.script',
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.condition?.script ?? '';
        },
        setValue: (target, value) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          target.node?.setPropValue('dslInfo', {
            ...(dslInfo ?? {}),
            condition: {
              ...(dslInfo?.condition ?? {}),
              script: value,
            },
          });
        },
        setter: {
          componentName: 'LcdpInputEditorSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-脚本',
                tooltip: {
                  title: 'dj-脚本执行结果为true可点击',
                },
              },
            },
            editorOptions: {
              title: 'dj-设置',
              type: 'javascript',
            },
          },
        },
      },
      {
        name: 'dslInfo.condition.trigger',
        setter: {
          isDynamic: false,
          componentName: 'AthCommonSetter',
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-触发时机',
                tooltip: {
                  title: 'dj-脚本触发时机',
                },
              },
              layout: 'vertical',
              componentType: 'select',
              componentProps: {
                size: 'small',
                mode: 'multiple',
                customClass: 'auto-height',
                placeholder: 'dj-请选择',
                options: [
                  {
                    label: 'dj-初始化',
                    value: 'init',
                  },
                  {
                    label: 'dj-数据变动时',
                    value: 'dataChanged',
                  },
                  {
                    label: 'dj-提交后',
                    value: 'inSubmit',
                  },
                ],
              },
            },
          },
        },
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.condition?.trigger;
        },
        setValue: (target, value) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          target.node?.setPropValue('dslInfo', {
            ...(dslInfo ?? {}),
            condition: {
              ...(dslInfo?.condition ?? {}),
              trigger: value,
            },
          });
        },
      },
      {
        name: 'dslInfo.condition.relateValidators',
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.condition?.relateValidators ?? false;
        },
        setValue: (target, value) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          target.node?.setPropValue('dslInfo', {
            ...(dslInfo ?? {}),
            condition: {
              ...(dslInfo?.condition ?? {}),
              relateValidators: value,
            },
          });
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-关联校验状态',
                layout: 'horizontal',
                tooltip: {
                  title: 'dj-开启时校验通过可点击',
                },
              },
              componentType: 'switch',
              componentProps: {
                size: 'small',
              },
            },
          },
        },
      },
      {
        name: 'dslInfo.condition.unchangedForbidClick',
        getValue: (target) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          return dslInfo?.condition?.unchangedForbidClick ?? false;
        },
        setValue: (target, value) => {
          const dslInfo = target.node?.getPropValue('dslInfo');
          target.node?.setPropValue('dslInfo', {
            ...(dslInfo ?? {}),
            condition: {
              ...(dslInfo?.condition ?? {}),
              unchangedForbidClick: value,
            },
          });
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-是否监听数据变化',
                layout: 'horizontal',
                tooltip: {
                  title: 'dj-是否监听数据变化tip',
                },
              },
              componentType: 'switch',
              componentProps: {
                size: 'small',
              },
            },
          },
        },
      },
    ],
  },
  hiddenConfig: {
    name: 'dslInfo.hiddenConfig',
    setter: {
      componentName: 'LcdpInputEditorSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: '隐藏',
            layout: 'horizontal',
          },
        },
        editorOptions: {
          title: 'dj-设置',
        },
      },
    },
  },
};
