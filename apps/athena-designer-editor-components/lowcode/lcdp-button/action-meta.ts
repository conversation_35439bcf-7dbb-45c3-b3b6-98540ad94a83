import { IPublicTypeFieldConfig } from '@alilc/lowcode-types';
import { ButtonApiType, ButtonCategory, ButtonWorkflowService } from './enum';
import { OtherButtonMeta } from './other-meta';
import { SupportSubActionsButtonTypeSet } from './constant';
import { getCategoryByType, getServiceName } from './tools';
import { getAppHelperUtils } from '../../src/tools';

export const ServiceMeta: { [key: string]: IPublicTypeFieldConfig } = {
  esp: {
    name: 'dslInfo.action.actionId',
    getValue: (target) => {
      const dslInfo = target.node?.getPropValue('dslInfo');
      return dslInfo?.action?.actionId;
    },
    condition: (target) => {
      const dslInfo = target.node?.getPropValue('dslInfo');
      return dslInfo?.action?.type === ButtonApiType.ESP;
    },
    setter: {
      isDynamic: false, // 处理Minified React error #321, 自定义setter最好设置isDynamic为false
      componentName: 'LcdpActionSetter',
      props: {
        labelType: 'EspAction',
        options: {
          titleProps: {
            setterTitle: 'dj-服务',
          },
        },
      },
    },
    setValue: (target, value) => {
      const action = target.node?.getPropValue('dslInfo.action') ?? {};
      const { actionInfo } = value ?? {};
      target.node?.setPropValue('dslInfo.action', {
        ...action,
        actionId: actionInfo?.actionId,
        serviceName: getServiceName(actionInfo?.actionId),
      });
    },
  },
  sd: {
    name: 'dslInfo.action.actionId',
    getValue: (target) => {
      const dslInfo = target.node?.getPropValue('dslInfo');
      return dslInfo?.action?.actionId;
    },
    condition: (target) => {
      const dslInfo = target.node?.getPropValue('dslInfo');
      return [ButtonApiType.SD, ButtonApiType.TM].includes(dslInfo?.action?.type);
    },
    setter: {
      isDynamic: false, // 处理Minified React error #321, 自定义setter最好设置isDynamic为false
      componentName: 'LcdpActionSetter',
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-服务',
          },
        },
      },
    },
    setValue: (target, value) => {
      const action = target.node?.getPropValue('dslInfo.action') ?? {};
      const { actionInfo } = value ?? {};
      target.node?.setPropValue('dslInfo.action', {
        ...action,
        actionId: actionInfo?.actionId,
        serviceName: actionInfo?.actionId,
      });
    },
  },
  workflowInvoke: {
    name: 'dslInfo.action.paras.processId',
    getValue: (target) => {
      const dslInfo = target.node?.getPropValue('dslInfo');
      return dslInfo?.action?.paras?.processId;
    },
    setValue: (target, value) => {
      const dslInfo = target.node?.getPropValue('dslInfo');
      target.node?.setPropValue('dslInfo', {
        ...(dslInfo ?? {}),
        action: {
          ...(dslInfo?.action ?? {}),
          paras: {
            processId: value,
          },
        },
      });
    },
    condition: (target) => {
      const dslInfo = target.node?.getPropValue('dslInfo');
      // return dslInfo?.action?.type === ButtonApiType.WORKFLOW && dslInfo?.action?.serviceName === ButtonWorkflowService.WORKFLOW_INVOKE;
      return dslInfo?.action?.serviceName === ButtonWorkflowService.WORKFLOW_INVOKE;
    },
    setter: {
      componentName: 'LcdpBindWorkflowSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-绑定业务流',
          },
        },
      },
    },
  },
};

export const SubmitTypeMeta: IPublicTypeFieldConfig[] = [
  {
    name: 'dslInfo.action.submitType.schema',
    getValue: (target) => {
      const dslInfo = target.node?.getPropValue('dslInfo');
      return dslInfo?.action?.submitType?.schema ?? '';
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-提交数据',
          },
          componentType: 'text',
        },
      },
    },
  },
  {
    name: 'dslInfo.action.submitType.isBatch',
    getValue: (target) => {
      const dslInfo = target.node?.getPropValue('dslInfo');
      return dslInfo?.action?.submitType?.isBatch;
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-是否分批',
            layout: 'horizontal',
          },
          componentType: 'switch',
          componentProps: {
            size: 'small',
          },
        },
      },
    },
  },
  {
    name: 'dslInfo.action.submitType.submitAll',
    getValue: (target) => {
      const dslInfo = target.node?.getPropValue('dslInfo');
      return dslInfo?.action?.submitType?.submitAll;
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-是否提交所有数据',
            layout: 'horizontal',
          },
          componentType: 'switch',
          componentProps: {
            size: 'small',
          },
        },
      },
    },
  },
  {
    name: 'dslInfo.trailingAction',
    condition: (target) => {
      const getConfigByKey = getAppHelperUtils(target.node!, 'getConfigByKey');
      const dynamicWorkDesignInfo: any = getConfigByKey('AthDynamicWorkDesignInfo');
      return dynamicWorkDesignInfo?.pageCode === 'sub-page';
    },
    getValue: (target) => {
      const dslInfo = target.node?.getPropValue('dslInfo');
      return !!dslInfo?.trailingAction;
    },
    setValue: (target, value) => {
      const dslInfo = target.node?.getPropValue('dslInfo');
      const { trailingAction, ...rest } = dslInfo ?? {};
      const newDslInfo = value
        ? {
            ...(rest ?? {}),
            trailingAction: 'close-page',
          }
        : {
            ...(rest ?? {}),
          };
      target.node?.setPropValue('dslInfo', newDslInfo);
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-提交后关闭子页面',
            layout: 'horizontal',
          },
          componentType: 'switch',
          componentProps: {
            size: 'small',
          },
        },
      },
    },
  },
];

export const ActionParamsMeta: IPublicTypeFieldConfig = {
  name: 'dslInfo.action.actionParams',
  getValue: (target) => {
    const dslInfo = target.node?.getPropValue('dslInfo');
    return {
      actionParams: dslInfo?.action?.actionParams ?? [],
      actionId: dslInfo?.action?.actionId ?? '',
      type: dslInfo?.action?.type ?? '',
    };
  },
  condition: (target) => {
    const dslInfo = target.node?.getPropValue('dslInfo');
    return !['BUTTON_WORKFLOW_ABORT', 'BUTTON_WORKFLOW_INVOKE'].includes(dslInfo?.type);
  },
  setter: {
    componentName: 'LcdpActionParamsSetter',
    isDynamic: false,
    props: {
      options: {
        titleProps: {
          setterTitle: 'dj-参数',
          layout: 'horizontal',
        },
      },
    },
  },
};

export const ActionMeta: IPublicTypeFieldConfig = {
  title: 'Action',
  type: 'group',
  display: 'accordion',
  condition: (target) => {
    const dslInfo = target.node?.getPropValue('dslInfo');
    const category = getCategoryByType(dslInfo.type);
    /**
     * 子页面关闭并保存是特殊情况，设计时它是属于功能按钮，但是它又走的是普通保存按钮
     */
    return category === ButtonCategory.FUNCTION_BUTTON;
  },
  items: [
    { ...ServiceMeta.esp },
    { ...ServiceMeta.sd },
    { ...ServiceMeta.workflowInvoke },
    {
      name: 'dslInfo.action.combineAttachActions',
      condition: (target) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        return SupportSubActionsButtonTypeSet.has(dslInfo?.type);
      },
      getValue: (target) => {
        const dslInfo = target.node?.getPropValue('dslInfo');
        const actions = dslInfo?.action?.combineAttachActions ?? [];
        return {
          actions,
        };
      },
      setter: {
        isDynamic: false,
        componentName: 'LcdpButtonActionsSetter',
      },
    },
    ...SubmitTypeMeta,
    OtherButtonMeta.condition,
    { ...OtherButtonMeta.hiddenConfig },
    { ...OtherButtonMeta.confirm },
    { ...OtherButtonMeta.returnText },
    { ...ActionParamsMeta },
  ],
};
