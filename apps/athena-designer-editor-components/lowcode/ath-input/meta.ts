import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import { AthenaDataType } from '../common/common.type';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  tipSetter,
  commonBasicSetter,
  commonDataTypeSetter,
  numberSetter,
  commonLibRuleSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const AthInputMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.INPUT,
  title: 'dj-文本输入',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonColumn',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          { ...commonBasicSetter.placeholder },
          ...tipSetter,
          { ...commonBasicSetter.status }, // 是否启用
          { ...commonBasicSetter.isFocusDisplay },
          { ...commonDataTypeSetter('AthenaDataType') },
          ...numberSetter,
          { ...commonBasicSetter.enableTrim },
        ],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonLibRuleSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        parentWhitelist: parentWhitelistMap.get(AthenaComponentType.INPUT) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-文本输入',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/INPUT.png`,
    schema: {
      componentName: AthenaComponentType.INPUT,
      title: 'dj-文本输入',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.INPUT,
          headerName: '文本输入',
          placeholder: '请输入',
          schema: '',
          path: '',
          disabled: false,
          editable: true,
          isFocusDisplay: false,
          dataType: AthenaDataType.STRING,
          tooltipMode: 'normal', // 有注释iconType的都要加
          iconType: '',
          lang: {
            headerName: {
              zh_CN: '文本输入',
              zh_TW: '文本输入',
              en_US: 'Input',
            },
            placeholder: {
              zh_CN: '请输入',
              zh_TW: '請輸入',
              en_US: 'please enter',
            },
            extraContent: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
            tooltipTitle: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
          },
        },
      },
    },
  },
  {
    title: 'dj-数字输入',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/INPUT_NUMBER.png`,
    schema: {
      componentName: AthenaComponentType.INPUT,
      title: 'dj-数字输入',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.INPUT,
          headerName: '数字输入',
          placeholder: '请输入',
          schema: '',
          path: '',
          disabled: false,
          editable: true,
          isFocusDisplay: false,
          dataType: AthenaDataType.NUMERIC,
          tooltipMode: 'normal', // 有注释iconType的都要加
          iconType: '',
          dataPrecision: {
            length: '',
            place: '',
          },
          max: '',
          min: '',
          step: 1,
          lang: {
            headerName: {
              zh_CN: '数字输入',
              zh_TW: '數字輸入',
              en_US: 'Input',
            },
            placeholder: {
              zh_CN: '请输入',
              zh_TW: '請輸入',
              en_US: 'please enter',
            },
            extraContent: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
            tooltipTitle: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
          },
        },
      },
    },
  },
];

export default {
  ...AthInputMeta,
  snippets,
};
