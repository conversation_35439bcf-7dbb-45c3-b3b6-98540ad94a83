import {
  IPublicModelSettingField,
  IPublicTypeComponentMetadata,
  IPublicTypeSnippet,
} from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import { AthenaDataType } from '../common/common.type';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  tipSetter,
  commonBasicSetter,
  commonDataTypeSetter,
  commonLibRuleSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const AthDatepickerMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.DATEPICKER,
  title: 'dj-日期选择',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonColumn',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          { ...commonBasicSetter.placeholder }, // 占位提示
          ...tipSetter, // 辅助提示+注释说明+注释图标
          { ...commonBasicSetter.status }, // 是否启用
          { ...commonBasicSetter.isFocusDisplay }, // 启用标题在内
          {
            name: 'dslInfo.dataType',
            setValue: (target, value) => {
              if (value === AthenaDataType.DATE) {
                target?.node?.setPropValue('dslInfo.mode', 'date');
                target?.node?.setPropValue('dslInfo.formatConfig', {
                  type: 'yyyy/MM/dd',
                  value: 'yyyy/MM/dd',
                });
                target?.node?.setPropValue('dslInfo.format', 'yyyy/MM/dd');
              } else if (value === AthenaDataType.DATETIME) {
                target?.node?.setPropValue('dslInfo.mode', 'date-second');
                target?.node?.setPropValue('dslInfo.formatConfig', {
                  type: 'yyyy/MM/dd HH:mm:ss',
                  value: 'yyyy/MM/dd HH:mm:ss',
                });
                target?.node?.setPropValue('dslInfo.format', 'yyyy/MM/dd HH:mm:ss');
              }
              target?.node?.setPropValue('dslInfo.dataType', value);
            },
            setter: {
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-数据类型',
                  },
                  componentType: 'select',
                  componentProps: {
                    options: [
                      { label: AthenaDataType.DATE, value: AthenaDataType.DATE },
                      { label: AthenaDataType.DATETIME, value: AthenaDataType.DATETIME },
                    ],
                  },
                },
              },
            },
          }, // 数据类型
          {
            name: 'dslInfo.mode',
            getValue(target) {
              const { dataType, mode } = target?.node?.getPropValue('dslInfo');
              return { dataType, mode };
            },
            setValue: (target, value) => {
              const formatConfig = target?.node?.getPropValue('dslInfo.formatConfig');
              const defaultFormatValue = {
                year: 'yyyy',
                month: 'yyyy/MM',
                week: 'yyyy/ww',
                date: 'yyyy/MM/dd',
                'date-minute': 'yyyy/MM/dd HH:mm',
                'date-second': 'yyyy/MM/dd HH:mm:ss',
              };
              const defaultValue = defaultFormatValue[value];
              target?.node?.setPropValue('dslInfo.format', defaultValue);
              target?.node?.setPropValue('dslInfo.formatConfig', {
                ...formatConfig,
                value: defaultValue,
                type: defaultValue,
              });
              // 非日期型的，隐藏快捷选项,
              if (!['date', 'date-minute', 'date-second']?.includes(value)) {
                target?.node?.setPropValue('dslInfo.showPickerOptions', false);
                target?.node?.setPropValue('dslInfo.pickerOptions', []);
                target?.node?.setPropValue('dslInfo.disabledDate', {
                  type: 'none',
                });
              }
            },
            setter: {
              componentName: 'LcdpDateModeSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-日期格式',
                  },
                  componentType: 'select',
                  componentProps: {},
                },
              },
            },
          },
          {
            getValue(target) {
              const { formatConfig, mode } = target?.node?.getPropValue('dslInfo');
              return { ...formatConfig, mode };
            },
            setValue: (target, value) => {
              target?.node?.setPropValue('dslInfo.formatConfig', value);
              target?.node?.setPropValue('dslInfo.format', value?.value);
            },
            setter: {
              componentName: 'AthPickerDisplaySetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-展示样式',
                  },
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            getValue(target) {
              const { disabledDate, mode } = target?.node?.getPropValue('dslInfo');
              return { ...disabledDate, mode };
            },
            name: 'dslInfo.disabledDate',
            setter: {
              componentName: 'AthPickerDisabledSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-可选日期范围',
                  },
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.showPickerOptions',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return ['date', 'date-minute', 'date-second']?.includes(dslInfo?.mode);
            },
            setValue: (target, value) => {
              // 默认值 最近7天
              let pickerOptions: string[] = [];
              const mode = target?.node?.getPropValue('dslInfo.mode');
              if (value && ['date', 'date-minute', 'date-second']?.includes(mode)) {
                pickerOptions = ['lastSevenDays'];
              }
              target?.node?.setPropValue('dslInfo.pickerOptions', pickerOptions);
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-快捷选项',
                    layout: 'horizontal',
                    tooltip: { title: 'dj-启用后，用户页面可快捷选择' },
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.pickerOptions',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return !!dslInfo?.showPickerOptions;
            },
            setter: {
              componentName: 'AthPickerOptionsSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {},
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
        ],
      },
      { ...commonLibRuleSetter },
      { ...commonAthRuleSetter },
      { ...commonAthMonacoEditorSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        // parentWhitelist: parentWhitelistMap.get(AthenaComponentType.DATEPICKER) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-日期选择',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/DATEPICKER.png`,
    schema: {
      componentName: AthenaComponentType.DATEPICKER,
      title: 'dj-日期选择',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.DATEPICKER,
          headerName: '日期选择',
          placeholder: 'yyyyMMdd ',
          schema: '',
          path: '',
          disabled: false,
          editable: true,
          isFocusDisplay: false,
          fieldType: 'datetime',
          dataType: AthenaDataType.DATE,
          tooltipMode: 'normal', // 有注释iconType的都要加
          iconType: 'wenhao',
          weekStartsOn: 1, // 周日为一周的第一天，0表示周日，1表示周一，需要传给运行态
          mode: 'date',
          format: 'yyyy/MM/dd',
          formatConfig: {
            type: 'yyyy/MM/dd',
            value: 'yyyy/MM/dd',
          },
          showPickerOptions: false,
          pickerOptions: [],
          disabledDate: {
            type: 'none',
          },
          rules: { value: [] },
          lang: {
            headerName: {
              zh_CN: '日期选择',
              zh_TW: '日期選擇',
              en_US: 'DatePicker',
            },
            placeholder: {
              zh_CN: 'yyyyMMdd',
              zh_TW: 'yyyyMMdd',
              en_US: 'yyyyMMdd',
            },
            extraContent: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
            tooltipTitle: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
          },
        },
      },
    },
  },
];

export default {
  ...AthDatepickerMeta,
  snippets,
};
