import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  commonBasicSetter,
  commonLibRuleSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const LcdpAddressMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.ADDRESS,
  title: 'dj-收货地址',
  group: 'dj-标准组件',
  category: 'dj-功能组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonShow',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      { ...commonAthMonacoEditorSetter },
      { ...commonLibRuleSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        parentWhitelist: parentWhitelistMap.get(AthenaComponentType.ADDRESS) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-收货地址',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/ADDRESS.svg`,
    schema: {
      componentName: AthenaComponentType.ADDRESS,
      title: 'dj-收货地址',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.ADDRESS,
          headerName: '收货地址',
          schema: '',
          path: '',
          lang: {
            headerName: {
              zh_CN: '收货地址',
              zh_TW: '收貨地址',
              en_US: 'Receiving Address',
            },
          },
        },
      },
    },
  },
];

export default {
  ...LcdpAddressMeta,
  snippets,
};
