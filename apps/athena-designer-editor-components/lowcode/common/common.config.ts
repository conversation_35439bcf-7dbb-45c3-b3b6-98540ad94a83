import { ButtonType } from '../../../athena-designer-editor/src/plugins/plugin-ath-setter/components/Button/enum';
import { AthenaDataType } from './common.type';
import { IPublicTypeNestingFilter } from '@alilc/lowcode-types';
import { BusinessButtonTypeSet } from '../button/constant';

// 组件数据类型映射
export const DataTypeList = {
  AthenaDataType: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.NUMERIC, value: AthenaDataType.NUMERIC },
    { label: AthenaDataType.DATE, value: AthenaDataType.DATE },
    { label: AthenaDataType.BOOLEAN, value: AthenaDataType.BOOLEAN },
    { label: AthenaDataType.DATETIME, value: AthenaDataType.DATETIME },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
    { label: AthenaDataType.OBJECT, value: AthenaDataType.OBJECT },
    { label: AthenaDataType.ARRAY, value: AthenaDataType.ARRAY },
  ],
  PERCENT_INPUT: [{ label: AthenaDataType.NUMERIC, value: AthenaDataType.NUMERIC }],
  AMOUNT_INPUT: [{ label: AthenaDataType.NUMERIC, value: AthenaDataType.NUMERIC }],
  CURRENT_ACCOUNT: [{ label: AthenaDataType.NUMERIC, value: AthenaDataType.NUMERIC }],
  EOC_USER_SELECT: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
  ],
  EOC_USER_SELECT_NEW: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
  ],
  MEASURE: [{ label: AthenaDataType.NUMERIC, value: AthenaDataType.NUMERIC }],
  DATEPICKER: [
    { label: AthenaDataType.DATE, value: AthenaDataType.DATE },
    { label: AthenaDataType.DATETIME, value: AthenaDataType.DATETIME },
  ],
  DATE_RANGE: [
    { label: AthenaDataType.DATE, value: AthenaDataType.DATE },
    { label: AthenaDataType.DATETIME, value: AthenaDataType.DATETIME },
  ],
  EOC_SELECT: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
  ],
  EOC_SELECT_NEW: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
  ],
  EOC_MULTI_SELECT: [{ label: AthenaDataType.ARRAY, value: AthenaDataType.ARRAY }],
  EOC_MULTI_SELECT_NEW: [{ label: AthenaDataType.ARRAY, value: AthenaDataType.ARRAY }],
  TIMEPICKER: [{ label: AthenaDataType.TIME, value: AthenaDataType.TIME }],
  TIME_RANGE: [{ label: AthenaDataType.ARRAY, value: AthenaDataType.ARRAY }],
  LABEL: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
  ],
  TEXTAREA: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
  ],
  CHECKBOX: [{ label: AthenaDataType.BOOLEAN, value: AthenaDataType.BOOLEAN }],
  PERSON_SELECT: [{ label: AthenaDataType.ARRAY, value: AthenaDataType.ARRAY }],
  PERSON_SELECT_NEW: [{ label: AthenaDataType.ARRAY, value: AthenaDataType.ARRAY }],
  SELECT_MULTIPLE: [{ label: AthenaDataType.ARRAY, value: AthenaDataType.ARRAY }],
  SELECT: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
  ],
  PLAN_SELECT: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
  ],
  ADD_DOCUMENTID_CONTROL: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
  ],
  TASK_PROGRESS_STATUS: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
  ],
  SIGN_OFF_PROGRESS: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
  ],
  WORKFLOW_PROGRESS: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
  ],
  ACTIVITY_TITLE: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
  ],
  DELIVERY_REPLY_DESCRIPTION: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
  ],
  DELIVERY_REPLY_TITLE: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
  ],
  SIGN_OFF_PROGRESS_LINK: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
  ],
  DYNAMIC_GRAPH_VIEWER: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
  ],
  RADIO_GROUP: [
    { label: AthenaDataType.STRING, value: AthenaDataType.STRING },
    { label: AthenaDataType.TIME, value: AthenaDataType.TIME },
  ],
};

// 下拉图标库
export const IconSelectList = [
  { value: 'wenhao', label: 'wenhao' },
  { value: 'gantanhao', label: 'gantanhao' },
  { value: 'daohang', label: 'daohang' },
  { value: 'fangdajing', label: 'fangdajing' },
  { value: 'dengpao', label: 'dengpao' },
  { value: 'shijian', label: 'shijian' },
  { value: 'yuechi', label: 'yuechi' },
  { value: 'dunpai', label: 'dunpai' },
  { value: 'jingshipai', label: 'jingshipai' },
];

// 组件类型枚举（平台标准组件）
export enum AthenaComponentType {
  ATHENA_TABLE = 'ATHENA_TABLE',
  TABLE_GROUP = 'TABLE_GROUP',
  INPUT = 'INPUT',
  AMOUNT_INPUT = 'AMOUNT_INPUT',
  PERCENT_INPUT = 'PERCENT_INPUT',
  NAME_CODE_COMPONENT = 'NAME_CODE_COMPONENT',
  NEW_OLD_COMPONENT = 'NEW_OLD_COMPONENT',
  AthLayout = 'LAYOUT',
  AthLayoutChild = 'AthLayoutChild',
  AthCollapse = 'COLLAPSE',
  AthCollapseItem = 'AthCollapseItem',
  AthCurrentAccount = 'CURRENT_ACCOUNT',
  AthTreeData = 'TREEDATA',
  AthFlexibleBox = 'FLEXIBLE_BOX',
  AthGridster = 'GRIDSTER',
  AthGridsterChild = 'AthGridsterChild',
  AthTabs = 'TABS',
  FORM_UPLOAD = 'FORM_UPLOAD',
  FILE_UPLOAD = 'FILE_UPLOAD',
  FORM_LIST = 'FORM_LIST',
  ATHMODAL = 'MODAL',
  MEASURE = 'MEASURE',
  DATEPICKER = 'DATEPICKER',
  EOC_USER_SELECT = 'EOC_USER_SELECT',
  EOC_USER_SELECT_NEW = 'EOC_USER_SELECT_NEW',
  EOC_SELECT = 'EOC_SELECT',
  EOC_SELECT_NEW = 'EOC_SELECT_NEW',
  EOC_MULTI_SELECT = 'EOC_MULTI_SELECT',
  EOC_MULTI_SELECT_NEW = 'EOC_MULTI_SELECT_NEW',
  DATE_RANGE = 'DATE_RANGE',
  APPROVAL_DESCRIPTION = 'APPROVAL_DESCRIPTION',
  TIMEPICKER = 'TIMEPICKER',
  TIME_RANGE = 'TIME_RANGE',
  ATH_TAG = 'ATH_TAG',
  OPERATION_EDITOR = 'OPERATION_EDITOR',
  FORM_OPERATION_EDITOR = 'FORM_OPERATION_EDITOR',
  BUTTON_GROUP = 'BUTTON_GROUP',
  BUTTON = 'BUTTON',
  BUTTON_DECOUPLE = 'BUTTON_DECOUPLE',
  DIFFERENCE_CALCULATION = 'DIFFERENCE_CALCULATION',
  TEXTAREA = 'TEXTAREA',
  ADDRESS = 'ADDRESS',
  CONTACT = 'CONTACT',
  TEXT = 'TEXT',
  LABEL = 'LABEL',
  CHECKBOX = 'CHECKBOX',
  PERSON_SELECT = 'PERSON_SELECT',
  PERSON_SELECT_NEW = 'PERSON_SELECT_NEW',
  SELECT = 'SELECT',
  SELECT_MULTIPLE = 'SELECT_MULTIPLE',
  PLAN_SELECT = 'PLAN_SELECT',
  ADD_DOCUMENTID_CONTROL = 'ADD_DOCUMENTID_CONTROL',
  TASK_PROGRESS_STATUS = 'TASK_PROGRESS_STATUS',
  SIGN_OFF_PROGRESS = 'SIGN_OFF_PROGRESS',
  WORKFLOW_PROGRESS = 'WORKFLOW_PROGRESS',
  ACTIVITY_TITLE = 'ACTIVITY_TITLE',
  DELIVERY_REPLY_DESCRIPTION = 'DELIVERY_REPLY_DESCRIPTION',
  DELIVERY_REPLY_TITLE = 'DELIVERY_REPLY_TITLE',
  SIGN_OFF_PROGRESS_LINK = 'SIGN_OFF_PROGRESS_LINK',
  DYNAMIC_GRAPH_VIEWER = 'DYNAMIC_GRAPH_VIEWER',

  RADIO_GROUP = 'RADIO_GROUP',
  LIST = 'LIST',
  ICON = 'ICON',
  IMAGE = 'IMAGE',
  DIVIDER = 'DIVIDER',
  TOOLBAR = 'TOOLBAR',
  FDCELL = 'FDCell',
  FLEX_ITEM = 'FLEX_ITEM',

  OUTLET = 'OUTLET',
  DATA_QUERY = 'DATA_QUERY',

  DYNAMIC_OPERATION = 'DYNAMIC_OPERATION', // 动态操作

  FLEX = 'FLEX',
  FILE_UPLOAD_DECOUPLE = 'FILE_UPLOAD_DECOUPLE',
  FORM_UPLOAD_DECOUPLE = 'FORM_UPLOAD_DECOUPLE'
}

// 组件类型枚举（开发平台专用组件类型）
export enum LcdpComponentType {
  COMMON = 'COMMON',
  DATA_QUERY_ITEM = 'DATA_QUERY_ITEM',
  DATA_QUERY_LIB = 'DATA_QUERY_LIB',
  DATA_QUERY_ITEM_LIB = 'DATA_QUERY_ITEM_LIB',
}

// 栏位组件List
const columnTypeList = [
  AthenaComponentType.FORM_UPLOAD,
  AthenaComponentType.INPUT,
  AthenaComponentType.PERCENT_INPUT,
  AthenaComponentType.TABLE_GROUP,
  AthenaComponentType.AMOUNT_INPUT,
  AthenaComponentType.NAME_CODE_COMPONENT,
  AthenaComponentType.NEW_OLD_COMPONENT,
  AthenaComponentType.FILE_UPLOAD,
  AthenaComponentType.FORM_UPLOAD_DECOUPLE,
  AthenaComponentType.FILE_UPLOAD_DECOUPLE,
  AthenaComponentType.MEASURE,
  AthenaComponentType.OPERATION_EDITOR,
  AthenaComponentType.FORM_OPERATION_EDITOR,
  AthenaComponentType.DIFFERENCE_CALCULATION,
  AthenaComponentType.EOC_SELECT,
  AthenaComponentType.EOC_SELECT_NEW,
  AthenaComponentType.EOC_MULTI_SELECT,
  AthenaComponentType.EOC_MULTI_SELECT_NEW,
  AthenaComponentType.TEXTAREA,
  AthenaComponentType.ADDRESS,
  AthenaComponentType.CONTACT,
  AthenaComponentType.LABEL,
  AthenaComponentType.CHECKBOX,
  AthenaComponentType.SELECT_MULTIPLE,
  AthenaComponentType.SELECT,
  AthenaComponentType.RADIO_GROUP,
  AthenaComponentType.PLAN_SELECT,
  LcdpComponentType.COMMON,
  // 标签类控件
  AthenaComponentType.ATH_TAG,
  // 日期类控件
  AthenaComponentType.DATEPICKER,
  AthenaComponentType.DATE_RANGE,
  AthenaComponentType.TIMEPICKER,
  AthenaComponentType.TIME_RANGE,
  // 文本类控件
  AthenaComponentType.TEXT,
  AthenaComponentType.AthFlexibleBox,
  AthenaComponentType.ICON, // 图标类控件
  AthenaComponentType.IMAGE, // 图片类控件
  AthenaComponentType.DIVIDER, // 分割线类控件
  AthenaComponentType.ADD_DOCUMENTID_CONTROL, // 单号
  AthenaComponentType.TASK_PROGRESS_STATUS, // 任务进展状态
  AthenaComponentType.SIGN_OFF_PROGRESS, // 签核历程弹窗
  AthenaComponentType.WORKFLOW_PROGRESS, // 流程进度
  AthenaComponentType.ACTIVITY_TITLE, // 任务卡头部标题
  AthenaComponentType.DELIVERY_REPLY_DESCRIPTION, // 任务卡回复简介
  AthenaComponentType.DELIVERY_REPLY_TITLE, // 任务卡回复标题
  AthenaComponentType.SIGN_OFF_PROGRESS_LINK, // 查看签核进度链接
  AthenaComponentType.DYNAMIC_GRAPH_VIEWER, // 图纸
];

export const dynamicComponent = [AthenaComponentType.BUTTON_GROUP];

export const isDynamicComponent = (type) => {
  console.log(type, 'isDynamicComponent1111111111');
  return [...dynamicComponent].includes(type) || BusinessButtonTypeSet.has(type);
};

// 通用栏位组件父容器的白名单（这里暂时用在表格和表单）
const commonColumnContainerNestingFilter: IPublicTypeNestingFilter = (testNode, currentNode) => {
  const testNodeDslInfo = testNode?.getPropValue('dslInfo');
  const isvCustomType = testNodeDslInfo?.isvCustomType;

  return (
    [...columnTypeList, ...dynamicComponent].includes(testNode?.componentName) ||
    !!isvCustomType ||
    BusinessButtonTypeSet.has(testNodeDslInfo?.type)
  );
};

// 子组件白名单
export const childWhitelistMap = new Map<
  AthenaComponentType | LcdpComponentType,
  string | RegExp | string[] | IPublicTypeNestingFilter | undefined
>([
  [AthenaComponentType.ATHENA_TABLE, commonColumnContainerNestingFilter],
  [AthenaComponentType.FORM_LIST, commonColumnContainerNestingFilter],
  [AthenaComponentType.TABLE_GROUP, [...columnTypeList, ...dynamicComponent]],
  [AthenaComponentType.AthCollapse, [AthenaComponentType.AthCollapseItem]],
  [AthenaComponentType.AthLayout, [AthenaComponentType.AthLayoutChild]],
  [
    AthenaComponentType.AthFlexibleBox,
    (testNode, currentNode) => {
      const disableTypes = [
        AthenaComponentType.AthGridster,
        AthenaComponentType.ATHENA_TABLE,
        AthenaComponentType.AthTabs,
        AthenaComponentType.AthLayout,
        AthenaComponentType.AthFlexibleBox,
        AthenaComponentType.TABLE_GROUP,
      ];
      const isFull = currentNode?.getChildren()?.size >= 6;
      return !disableTypes.includes(testNode?.componentName) && !isFull;
    },
  ],
  [
    AthenaComponentType.BUTTON_GROUP,
    (testNode, currentNode) => {
      const parentNode = currentNode?.parent;
      if (parentNode?.componentName === AthenaComponentType.DYNAMIC_OPERATION) {
        const testNodeDslInfo = testNode?.getPropValue('dslInfo');
        return BusinessButtonTypeSet.has(testNodeDslInfo?.type);
      } else {
        return [ButtonType.BUTTON, LcdpComponentType.COMMON].includes(testNode?.componentName);
      }
    },
  ],
  [AthenaComponentType.AthGridster, [AthenaComponentType.AthGridsterChild]],
  [
    AthenaComponentType.AthGridsterChild,
    (testNode, currentNode) => {
      const disableTypes = [
        AthenaComponentType.AthLayout,
        AthenaComponentType.TOOLBAR,
        AthenaComponentType.AthGridster,
      ];
      const isFull = currentNode?.getChildren()?.size >= 1;
      return !isFull && !disableTypes.includes(testNode?.componentName);
    },
  ],
  [AthenaComponentType.AthTabs, ['TAB_PANEL']],
  [AthenaComponentType.DATA_QUERY, [LcdpComponentType.DATA_QUERY_ITEM]],
  [LcdpComponentType.DATA_QUERY_LIB, [LcdpComponentType.DATA_QUERY_ITEM_LIB]],
  [LcdpComponentType.DATA_QUERY_ITEM_LIB, [AthenaComponentType.FLEX]],
  // [
  //   AthenaComponentType.DYNAMIC_OPERATION,
  //   (testNode) => {
  //     const testNodeDslInfo = testNode?.getPropValue('dslInfo');
  //     return (
  //       BusinessButtonTypeSet.has(testNodeDslInfo?.type) ||
  //       dynamicComponent.includes(testNode?.componentName)
  //     );
  //   },
  // ],
]);

// 父组件白名单
export const parentWhitelistMap = new Map<
  AthenaComponentType,
  string | RegExp | string[] | IPublicTypeNestingFilter | undefined
>([
  [AthenaComponentType.AthCollapseItem, [AthenaComponentType.AthCollapse]],
  [
    AthenaComponentType.AthLayout,
    (testNode) => {
      const disableTypes = [AthenaComponentType.AthFlexibleBox];
      return !disableTypes.includes(testNode?.componentName);
    },
  ],
  [
    AthenaComponentType.AthLayoutChild,
    (testNode) => {
      const disableTypes = [AthenaComponentType.AthFlexibleBox];
      return !disableTypes.includes(testNode?.componentName);
    },
  ],
  [
    AthenaComponentType.AthFlexibleBox,
    (testNode) => {
      const disableTypes = [
        AthenaComponentType.ATHENA_TABLE,
        AthenaComponentType.AthTabs,
        AthenaComponentType.AthLayout,
        AthenaComponentType.NAME_CODE_COMPONENT,
        AthenaComponentType.TABLE_GROUP,
        AthenaComponentType.AthFlexibleBox,
      ];
      return !disableTypes.includes(testNode?.componentName);
    },
  ],
  [
    AthenaComponentType.FILE_UPLOAD,
    [AthenaComponentType.TABLE_GROUP, AthenaComponentType.ATHENA_TABLE],
  ],
  [AthenaComponentType.FORM_UPLOAD, [AthenaComponentType.FORM_LIST]],
  [AthenaComponentType.AthGridsterChild, [AthenaComponentType.AthGridster]],
  [
    AthenaComponentType.DYNAMIC_OPERATION,
    [AthenaComponentType.ATHENA_TABLE, AthenaComponentType.FORM_LIST],
  ],
  [
    AthenaComponentType.FILE_UPLOAD_DECOUPLE,
    [AthenaComponentType.TABLE_GROUP, AthenaComponentType.ATHENA_TABLE],
  ],
]);

// 可使用词汇枚举的组件（选项类型的组件: 下拉、多选下拉、方案选择）
export const VocabularyComponents = [
  AthenaComponentType.SELECT,
  AthenaComponentType.SELECT_MULTIPLE,
  AthenaComponentType.PLAN_SELECT,
  AthenaComponentType.RADIO_GROUP,
];

// 可使用词汇枚举的组件（选项类型的组件: 下拉、多选下拉、方案选择）
export const UseApproveOptionComponents = [AthenaComponentType.PLAN_SELECT];

// 根据版本隐藏组件列表
export const hideComponentByVersion = {
  '1.0': [
    // 2.0新增控件
    'MODAL',
    'FORM_UPLOAD_DECOUPLE',
    'FILE_UPLOAD_DECOUPLE',
    'BUTTON_DECOUPLE',
    'BUTTON_ADD_ITEM_DECOUPLE',
    'BUTTON_EDIT_ITEM_DECOUPLE',
    'BUTTON_DETAIL_ITEM_DECOUPLE',
    'BUTTON_DELETE_ITEM_DECOUPLE',
    'BUTTON_OPENPAGE_ADD_DECOUPLE',
    'BUTTON_COMBINE_SAVE_DECOUPLE',
    'BUTTON_DATA_DELETE_DECOUPLE'
  ],
  '2.0': [
    // 基础组件
    'AMOUNT_INPUT', // 金额
    'MEASURE', // 计量
    'DYNAMIC_GRAPH_VIEWER', // 图纸
    // 功能组件
    'CURRENT_ACCOUNT', // 登录用户
    'ADDRESS', // 收获地址
    'CONTACT', // 采购员
    'PERSON_SELECT', // 人员选择
    'PERSON_SELECT_NEW', // 新人员选择
    'PLAN_SELECT', // 方案选择
    'EOC_USER_SELECT', // EOC员工选择
    'EOC_USER_SELECT_NEW', // 新EOC员工选择
    'EOC_SELECT', // 运营单元单选（EOC）
    'EOC_SELECT_NEW', // 新运营单元单选（EOC）
    'EOC_MULTI_SELECT', // 运营单元多选（EOC）
    'EOC_MULTI_SELECT_NEW', // 新运营单元多选（EOC）
    'APPROVAL_DESCRIPTION', // 签核摘要
    'TOOLBAR', // 整单操作
    'ADD_DOCUMENTID_CONTROL', // 单号
    'TASK_PROGRESS_STATUS', // 任务进展状态
    'SIGN_OFF_PROGRESS', // 签核历程弹窗
    'WORKFLOW_PROGRESS', // 流程进度
    'ACTIVITY_TITLE', // 任务卡头部标题
    'DELIVERY_REPLY_DESCRIPTION', // 任务卡回复简介
    'DELIVERY_REPLY_TITLE', // 任务卡回复标题
    'SIGN_OFF_PROGRESS_LINK', // 查看签核进度链接
    // 废弃组件
    'LAYOUT', // 布局组件
    'FLEXIBLE_BOX', // 行布局
    'TREEDATA', // 树选择
    'OUTLET', // 出口组件
    'COMMON', // 自定义组件，原定制栏位组件
    // 历史按钮
    'BUTTON',
    // 隐藏原附件组件
    'FORM_UPLOAD', // 表格附件
    'FILE_UPLOAD', // 表单附件
  ]
};
