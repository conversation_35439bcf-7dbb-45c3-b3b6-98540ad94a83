import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import { AthenaDataType } from '../common/common.type';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  tipSetter,
  commonBasicSetter,
  commonDataTypeSetter,
  textareaSetter,
  commonLibRuleSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const TextareaMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.TEXTAREA,
  title: 'dj-文本域',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonColumn',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          { ...commonBasicSetter.placeholder },
          ...tipSetter,
          { ...commonBasicSetter.status }, // 是否启用
          { ...commonBasicSetter.isFocusDisplay },
          { ...commonDataTypeSetter('TEXTAREA') },
          ...textareaSetter,
        ],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonLibRuleSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        parentWhitelist: parentWhitelistMap.get(AthenaComponentType.TEXTAREA) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-文本域',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/TEXTAREA.png`,
    schema: {
      componentName: AthenaComponentType.TEXTAREA,
      title: 'dj-文本域',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.TEXTAREA,
          headerName: '文本域',
          placeholder: '请输入',
          schema: '',
          path: '',
          disabled: false,
          editable: true,
          isFocusDisplay: false,
          dataType: AthenaDataType.STRING,
          tooltipMode: 'normal', // 有注释iconType的都要加
          iconType: '',
          dataPrecision: {
            length: 300,
          },
          maxRows: 3,
          minRows: 3,
          lang: {
            headerName: {
              zh_CN: '文本域',
              zh_TW: '文字域',
              en_US: 'Input',
            },
            placeholder: {
              zh_CN: '请输入',
              zh_TW: '請輸入',
              en_US: 'please enter',
            },
            extraContent: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
            tooltipTitle: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
          },
        },
      },
    },
  },
];

export default {
  ...TextareaMeta,
  snippets,
};
