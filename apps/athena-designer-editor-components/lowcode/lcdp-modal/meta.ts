import {
  IPublicModelSettingField,
  IPublicTypeComponentMetadata,
  IPublicTypeSnippet,
} from '@alilc/lowcode-types';

import {
  commonAthMonacoEditorSetter,
  commonDataSourceNamesSetter,
} from '../common/common-meta-info.config';
import { AthenaComponentType, childWhitelistMap } from '../common/common.config';
import { envParams } from '@/env';
import AthIconComponentMeta from '../ath-modal-icon/meta';
import ButtonGroupMeta from '../footer-button-group/meta';

const LcdpAthModalMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.ATHMODAL,
  title: 'dj-弹窗',
  group: 'dj-标准组件',
  category: 'dj-容器组件',
  docUrl: '',
  screenshot: '',
  icon: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'AthModal',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonDataSourceNamesSetter },
      {
        name: 'dslInfo.lang.title',
        setValue: (target, value) => {
          target?.node?.setPropValue('dslInfo.title', value['zh_CN']);
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-标题',
              },
              componentType: 'lang',
            },
          },
        },
      },
      {
              name: 'titleIcon',
              condition: (target: IPublicModelSettingField) => {
                const dslInfo = target.node?.getPropValue('dslInfo');
                return !!dslInfo?.isGrid;
              },
              getValue: (target) => {
                const { titleIcon } = target.node?.getPropValue('dslInfo') ?? {};
                return titleIcon;
              },
              setValue: (target, value) => {
                target.node?.setPropValue('dslInfo.titleIcon', value);
              },
              setter: {
                componentName: 'AthCommonSetter',
                isDynamic: false,
                props: {
                  options: {
                    titleProps: {
                      setterTitle: '前缀图标',
                      layout: 'horizontal',
                    },
                    componentType: 'select',
                    componentProps: {
                      options: [
                        { label: '默认', value: 'default' },
                        { label: '警告', value: 'warning' },
                        { label: '成功', value: 'success' },
                        { label: '失败', value: 'failure' },
                      ],
                    },
                  },
                },
              },
            },
              {
            name: 'dslInfo.lang.textContent',
            condition: (target: IPublicModelSettingField) => {
                const dslInfo = target.node?.getPropValue('dslInfo');
                return !!dslInfo?.iscontent;
              },
            setValue: (target, value) => {
              target?.node?.setPropValue('dslInfo.textContent', value['zh_CN']);
            },
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-文本内容',
                    tooltip: { title: 'dj-固定文本内容，未选择关联字段时生效' },
                  },
                  layout: 'vertical',
                  componentType: 'lang',
                  componentProps: {
                    isTextArea: true,
                  },   
                },
              },
            },
          },
             {
              name: 'dslInfo.fontIcon',
              condition: (target: IPublicModelSettingField) => {
                const dslInfo = target.node?.getPropValue('dslInfo');
                return !!dslInfo?.isFontIcon;
              },
              setter: {
                componentName: 'AthCommonSetter',
                props: {
                  options: {
                    titleProps: {
                      setterTitle: '前缀图标',
                      layout: 'horizontal',
                    },
                    componentType: 'switch',
                    componentProps: {
                      size: 'small',
                    },
                  },
                },
              },
              defaultValue: false, 
            },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
         {
              name: 'size',
              getValue: (target) => {
                const { size } = target.node?.getPropValue('dslInfo') ?? {};
                return size || 'medium';
              },
              setValue: (target, value) => {
                target.node?.setPropValue('dslInfo.size', value);
              },
              setter: {
                componentName: 'AthModalSizeSetter',
                isDynamic: false,
                props: {
                  options: {
                    titleProps: {
                      setterTitle: '弹窗大小',
                    },
                    componentType: 'button-group',
                    componentProps: {
                      options: [
                        { label: '小', value: 'small' },
                        { label: '中', value: 'medium' },
                        { label: '大', value: 'large' },
                        { label: '超大', value: 'xlarge' },
                      ],
                    },
                  },
                },
              },
              defaultValue: 'medium',
            },
        {
          name: 'dslInfo.mask',
          setter: {
            componentName: 'AthCommonSetter',
            props: {
              options: {
                titleProps: {
                  setterTitle: '是否显示遮罩',
                  layout: 'horizontal',
                },
                componentType: 'switch',
                 componentProps: {
                  size: 'small',
                },
              },
            },
          },
          defaultValue: true, // 默认显示遮罩
        },
        {
          name: 'dslInfo.maskClosable',
          setter: {
            componentName: 'AthCommonSetter',
            props: {
              options: {
                titleProps: {
                  setterTitle: '点击蒙层关闭',
                  layout: 'horizontal'
                },
                componentType: 'switch',
                componentProps: {
                  size: 'small',
                },
              },
            },
          },
          defaultValue: false, // 默认不关闭
        },
        {
          title: '样式',
          type: 'group',
          display: 'accordion',
          items: [
            {
              getValue: (target) => {
                const { padding } = target.node?.getPropValue('dslInfo') ?? {};
                const paddingList = padding?.replace(/\s+/g, ' ')?.split(' ') ?? [];
                const [top, right, bottom, left] = paddingList;
                return {
                  top,
                  right: right ?? top,
                  bottom: bottom ?? top,
                  left: left ?? right,
                };
              },
              setValue: (target, value = {}) => {
                let { top, right, bottom, left } = value;
                const padding = [top || '24', right || '24', bottom || '24', left || '24'].join(' ');
                target.node?.setPropValue('dslInfo.padding', padding);
              },
              setter: {
                componentName: 'LcdpPaddingSetter',
                isDynamic: false,
                props: {
                  options: {
                    titleProps: {
                      setterTitle: '边距',
                    },
                  },
                },
              },
            },
          ],
        },
        ],
      },
      { ...commonAthMonacoEditorSetter },
      {
        title: 'dj-操作设置（关联属性）',
        display: 'accordion',
        getValue: (target) => {
          const dslInfo = target?.node?.getPropValue('dslInfo');
          return dslInfo;
        },
        setter: {
          isDynamic: false,
          componentName: 'LcdpTableOperationSetter',
          props: {
            componentProps: {
              allTitle: 'dj-弹窗操作',
              showAll: true, // 是否整体操作
            },
          },
        },
      },
    ],
    component: {
      isContainer: true,
      nestingRule: {
        childWhitelist: childWhitelistMap.get(AthenaComponentType.ATHMODAL) ?? [],
      },
    },
    advanced: {
      initialChildren: [
        {
          componentName: AthenaComponentType.BUTTON_GROUP,
          // props: ButtonGroupMeta.snippets[0].schema.props,
           props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.BUTTON_GROUP,
          headerName: '按钮组',
          verticalAlignment: 'center',
          gap: '12px',
          lang: {
            headerName: {
              zh_CN: '按钮组',
              zh_TW: '按鈕組',
              en_US: 'Button Group',
            },
          },
          moreButtonConfig: {
            enable: false,
          },
          group: [
            {
                   type: "BUTTON_DATA_COMBINE_SAVE",
                   title: "保存",
                   styleMode: 'primary',  
                   size: 'large',
                   action: {
                    title: '保存',
                    actionType: 'basic-data-combine-save',
                    category: 'ESP',
                    type: 'COMBINE',
                    id: 'save-button',
                    dispatch: false,
                    defaultAction: true,
                    submitType: {
                      isBatch: true,
                      schema: 'work_report_info',
                    },
                    combineActions: [],
                  },
                }
          ],
        },
      }, 
        },
         {
          componentName: AthenaComponentType.ICON,
          props: AthIconComponentMeta.snippets[0].schema.props,
        },
      ],
      callbacks: {
        onNodeAdd: (addedNode, currentNode) => {},
      },
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-弹窗',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/ACTIVITY_TITLE.svg`,
    schema: {
      componentName: AthenaComponentType.ATHMODAL,
      props: {
        dslInfo: {
          id: '',
          title: '弹窗',
          fontIcon: false,
          dataType: 'object',
          lang: {
            title: {
              zh_CN: '弹窗',
              en_US: 'modal',
              zh_TW: '弹窗',
            },
          },
          isGrid: false,
          iscontent: false,
          isFontIcon: true,
          mask: true, //是否展示遮罩层,默认显示
          maskStyle: { "backgroundColor": "transparent" }, //遮罩层样式
          maskClosable: false, //点击蒙层是否允许关闭,默认false
          draggable: true, //开启拖拽
          keyboard: true, //开启键盘esc关闭弹窗
          modalType: "customDsl", // 弹窗类型
          size: 'medium', // 弹窗大小
          bodyGroup: [{}]
      ,
        footerGroup: 
          [{
            id: "454630f779924ef6b59856845c442c50",
            type: "BUTTON_GROUP",
            operations: [],
            reverse: true,
            padding: ["12px", "0px"],
            position: "absolute",
            bottom: "0px",
            group: [
              {
                id: "797d6505-5c26-8743-5eca-4c03b81c5286",
                type: "BUTTON_DATA_COMBINE_SAVE",
                title: "保存",
                disabled: false,
                debounce: false,
                debounceTime: 100,
                styleMode: "primary",
                size: "large",
                ghost: false,
                danger: false,
                block: false,
                async: true,
                showLoading: true,
                trailingAction: "close-page",
                condition: {
                  relateValidators: true,
                  unchangedForbidClick: true
                },
                action: {
                  title: "保存",
                  actionType: "basic-data-combine-save",
                  category: "ESP",
                  type: "COMBINE",
                  id: "797d6505-5c26-8743-5eca-4c03b81c5286",
                  dispatch: false,
                  defaultAction: true,
                  submitType: {
                    isBatch: true,
                    schema: "work_report_info"
                  },
                  combineActions: []
                }
              }
            ]
          }
        ],
          direction: 'ROW',
          type: AthenaComponentType.ATHMODAL,
          schema: '',
          path: '',
          collapse: false,
        },
      },
    },
  },
  {
    title: '反馈弹窗',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/DELIVERY_REPLY_DESCRIPTION.svg`,
    schema: {
      componentName: AthenaComponentType.ATHMODAL,
      props: {
        dslInfo: {
          id: '',
          title: '反馈弹窗',
          titleIcon: 'default',
          dataType: 'object',
          lang: {
            title: {
              zh_CN: '提示',
              en_US: 'modal',
              zh_TW: '提示',
            },
            textContent: {
              zh_CN: '文本内容显示在此',
              zh_TW: '文本內容顯示在此',
              en_US: 'The text content is displayed here',
            },
          },
          isGrid: true,
          iscontent: true,
          isFontIcon: false,
          textContent: '文本内容显示在此',
          mask: true, //是否展示遮罩层,默认显示
          maskStyle: { "backgroundColor": "transparent" }, //遮罩层样式
          maskClosable: false, //点击蒙层是否允许关闭,默认false
          draggable: true, //开启拖拽
          keyboard: true, //开启键盘esc关闭弹窗
          modalType: "customDsl", // 弹窗类型
          size: 'medium', // 弹窗大小
          bodyGroup: 
        [{
          icon: 'ath-icon-bm-mian1', //取自平台组件库Icon
          title: '警告',
          content: '这是一个警告弹窗',
        }]
      ,
        footerGroup: 
          [{
            id: "454630f779924ef6b59856845c442c50",
            type: "BUTTON_GROUP",
            operations: [],
            reverse: true,
            padding: ["12px", "0px"],
            position: "absolute",
            bottom: "0px",
            group: [
              {
                id: "797d6505-5c26-8743-5eca-4c03b81c5286",
                type: "BUTTON_DATA_COMBINE_SAVE",
                title: "保存",
                disabled: false,
                debounce: false,
                debounceTime: 100,
                styleMode: "primary",
                size: "large",
                ghost: false,
                danger: false,
                block: false,
                async: true,
                showLoading: true,
                trailingAction: "close-page",
                condition: {
                  relateValidators: true,
                  unchangedForbidClick: true
                },
                action: {
                  title: "保存",
                  actionType: "basic-data-combine-save",
                  category: "ESP",
                  type: "COMBINE",
                  id: "797d6505-5c26-8743-5eca-4c03b81c5286",
                  dispatch: false,
                  defaultAction: true,
                  submitType: {
                    isBatch: true,
                    schema: "work_report_info"
                  },
                  combineActions: []
                }
              }
            ]
          }
        ],

          direction: 'ROW',
          type: AthenaComponentType.ATHMODAL,
          schema: '',
          path: '',
          collapse: false
        },
      },
    },
  },
];

export default {
  ...LcdpAthModalMeta,
  snippets,
};
