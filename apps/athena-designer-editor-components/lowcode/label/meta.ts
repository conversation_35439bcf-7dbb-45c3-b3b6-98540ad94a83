import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  commonBasicSetter,
  commonDataTypeSetter,
  commonLibRuleSetter,
} from '../common/common-meta-info.config';
import { AthenaDataType } from '../common/common.type';
import { envParams } from '@/env';

const LabelMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.LABEL,
  title: 'dj-文本',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonShow',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          // { ...commonBasicSetter.status }, // 是否启用
          { ...commonBasicSetter.isFocusDisplay },
          { ...commonBasicSetter.important },
          { ...commonBasicSetter.className },
          { ...commonDataTypeSetter('LABEL') },
        ],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonLibRuleSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        parentWhitelist: parentWhitelistMap.get(AthenaComponentType.LABEL) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-文本',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/LABEL.svg`,
    schema: {
      componentName: AthenaComponentType.LABEL,
      title: 'dj-文本',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.LABEL,
          headerName: '文本',
          schema: '',
          path: '',
          dataType: AthenaDataType.STRING,
          lang: {
            headerName: {
              zh_CN: '文本',
              zh_TW: '文本',
              en_US: 'Text',
            },
          },
        },
      },
    },
  },
];

export default {
  ...LabelMeta,
  snippets,
};
