import {
  IPublicModelSettingField,
  IPublicTypeComponentMetadata,
  IPublicTypeSnippet,
} from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  commonBasicSetter,
  commonLibRuleSetter,
} from '../common/common-meta-info.config';
import { sizeTypes } from '../common/common.type';
import { envParams } from '@/env';

const AthIconMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.ICON,
  title: 'dj-图标',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'AthIcon',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          {
            name: 'dslInfo.iconType',
            setValue: (target, value) => {
              const { iconType, name } = value;
              target?.node?.setPropValue('dslInfo.iconType', iconType);
              target?.node?.setPropValue('dslInfo.name', name);
            },
            setter: {
              isDynamic: false,
              componentName: 'AthIconSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-图标选择',
                  },
                  layout: 'vertical',
                },
              },
            },
          },
          {
            name: 'dslInfo.sizeType',
            setValue: (target, value) => {
              if (sizeTypes[value]) {
                target.node?.setPropValue('dslInfo.size', sizeTypes[value]);
              } else {
                target.node?.setPropValue('dslInfo.size', '16px'); // 默认值
              }
            },
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-图标尺寸',
                  },
                  layout: 'vertical',
                  componentType: 'select',
                  componentProps: {
                    options: [
                      { label: 'dj-超小', value: 'extraSmall' },
                      { label: 'dj-小', value: 'small' },
                      { label: 'dj-中', value: 'middle' },
                      { label: 'dj-大', value: 'large' },
                      { label: 'dj-超大', value: 'extraLarge' },
                      { label: 'dj-自定义', value: 'self' },
                    ],
                  },
                },
              },
            },
          },
          {
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return dslInfo?.sizeType === 'self';
            },
            getValue: (target) => {
              const { size } = target.node?.getPropValue('dslInfo') || {};
              return size?.replace('px', '');
            },
            setValue: (target, value) => {
              target.node?.setPropValue('dslInfo.size', `${value}px`);
            },
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-图标尺寸',
                  },
                  layout: 'vertical',
                  componentType: 'number',
                  componentProps: {
                    disabled: false,
                    addonAfter: 'px',
                    max: 100,
                    min: 8,
                    step: 1,
                  },
                  formItemRules: [
                    {
                      type: 'number',
                      min: 8,
                      max: 100,
                    },
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
          {
            name: 'dslInfo.color',
            setter: {
              isDynamic: false,
              componentName: 'AthColorPanelSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-图标颜色',
                  },
                  layout: 'vertical',
                },
              },
            },
          },
        ],
      },
      {
        title: 'dj-高级设置',
        type: 'group',
        display: 'accordion',
        items: [
          {
            name: 'dslInfo.spin',
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-旋转动画',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.rotate',
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-旋转角度',
                  },
                  componentType: 'number',
                  componentProps: {
                    step: 1,
                  },
                  formItemRules: [
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
        ],
      },
      {
        title: 'dj-布局',
        type: 'group',
        display: 'accordion',
        items: [
          {
            name: 'dslInfo.padding',
            getValue: (target) => {
              const { padding } = target.node?.getPropValue('dslInfo') ?? {};
              let listPaddingList = padding?.replace(/\s+/g, ' ')?.split(' ') ?? [];
              let [top, right, bottom, left] = listPaddingList;
              right = right ?? top;
              bottom = bottom ?? top;
              left = left ?? right;

              return {
                top,
                right,
                bottom,
                left,
              };
            },
            setValue: (target, value = {}) => {
              let { top, right, bottom, left } = value;
              const padding = [top || '0', right || '0', bottom || '0', left || '0'].join(' ');
              target.node?.setPropValue('dslInfo.padding', padding);
            },
            setter: {
              componentName: 'LcdpPaddingSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-内边距',
                  },
                },
              },
            },
          },
        ],
      },
      { ...commonLibRuleSetter },
      { ...commonAthRuleSetter },
      { ...commonAthMonacoEditorSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        // parentWhitelist: parentWhitelistMap.get(AthenaComponentType.ICON) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-图标',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/ICON.svg`,
    schema: {
      componentName: AthenaComponentType.ICON,
      title: 'dj-图标',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.ICON,
          headerName: 'dj-图标',
          schema: '',
          path: '',
          enableIconfont: true, // 运行态需要 enableIconfont 字段判断当前 iconType 为 iconfont 图标还是 ng-zorro 图标
          iconType: 'ath-icon-APP-outline',
          name: '',
          sizeType: 'small',
          size: '14px',
          color: '#000',
          padding: '0',
          spin: false,
          rotate: 0,
          lang: {
            headerName: {
              zh_CN: '图标',
              zh_TW: '',
              en_US: 'text',
            },
          },
        },
      },
    },
  },
];

export default {
  ...AthIconMeta,
  snippets,
};
