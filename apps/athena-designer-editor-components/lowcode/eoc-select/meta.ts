import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType } from '../common/common.config';
import {
  commonAthMonacoEditorSetter,
  commonAthRuleSetter,
  commonDataTypeSetter,
  commonBasicSetter,
  tipSetter,
  commonLibRuleSetter,
} from '../common/common-meta-info.config';
import { AthenaDataType } from '../common/common.type';
import { envParams } from '@/env';
import { flatTreeData, getAppHelperUtils } from '../../src/tools';

const EocSelectMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.EOC_SELECT,
  title: 'dj-运营单元单选',
  group: 'dj-标准组件',
  category: 'dj-功能组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonColumn',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      {
        title: 'dj-关联字段',
        type: 'group',
        display: 'accordion',
        items: [
          {
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              const { schema, path } = dslInfo;
              return { schema, path };
            },
            setValue: (target, value) => {
              const { schema, path } = value;
              target?.node?.setPropValue('dslInfo.schema', schema);
              target?.node?.setPropValue('dslInfo.path', path);
              const newValue = `${path}.${schema}` === schema ? '' : path;
              const pathArray = newValue.split('.') || [];
              const _path = pathArray[pathArray.length - 1] || '';
              const getConfigByKey = getAppHelperUtils(target.node!, 'getConfigByKey');
              const fieldTree: any[] = getConfigByKey('AthFieldTree');
              const flatFieldTree = flatTreeData(fieldTree);
              const childFields = flatFieldTree?.get(_path)?.children;
              const schemas = ['eoc_company_id', 'eoc_site_id', 'eoc_region_id'];
              const actualSchemas = schemas.filter((d) => {
                return childFields?.find(({ data_name }) => data_name === d);
              });
              target.node?.setPropValue('dslInfo.schemas', actualSchemas);
            },
            setter: {
              componentName: 'AthSelectAssociationFieldSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-关联字段',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.schema',
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'schema',
                  },
                  formItemRules: [
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
          {
            name: 'dslInfo.path',
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'path',
                  },
                },
              },
            },
          },
        ],
      },
      {
        type: 'group',
        title: 'dj-基础设置',
        display: 'accordion',
        items: [
          { ...commonBasicSetter.placeholder },
          ...tipSetter,
          { ...commonBasicSetter.isFocusDisplay },
          { ...commonBasicSetter.status },
          { ...commonDataTypeSetter(AthenaComponentType.EOC_SELECT) },
          { ...commonBasicSetter.schemas },
        ],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonLibRuleSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
    },
    advanced: {
      initialChildren: [],
    },
  },
};

const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-运营单元单选',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/EOC_SELECT.svg`,
    schema: {
      componentName: AthenaComponentType.EOC_SELECT,
      title: 'dj-运营单元单选',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.EOC_SELECT,
          schema: '',
          path: '',
          label: '运营单元单选',
          headerName: '运营单元单选',
          placeholder: '请选择',
          disabled: false,
          editable: true,
          sortable: true,
          filterable: true,
          rowGroupable: false,
          isFocusDisplay: false,
          extraContent: '',
          tooltipTitle: '',
          iconType: 'wenhao',
          tooltipMode: 'normal',
          schemas: [],
          dataType: AthenaDataType.STRING,
          lang: {
            headerName: {
              zh_CN: '运营单元单选',
              zh_TW: '運營單元單選',
              en_US: 'Please selec',
            },
            placeholder: {
              zh_CN: '请选择',
              zh_TW: '請選擇',
              en_US: 'Please select',
            },
            label: {
              zh_CN: '运营单元单选',
              zh_TW: '運營單元單選',
              en_US: 'Please selec',
            },
            extraContent: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
            tooltipTitle: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
          },
        },
      },
    },
  },
];

export default {
  ...EocSelectMeta,
  snippets,
};
