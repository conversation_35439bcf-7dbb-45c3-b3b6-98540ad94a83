import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import { AthenaDataType } from '../common/common.type';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  commonBasicSetter,
  commonDataTypeSetter,
  commonLibRuleSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const AthTimeRangeMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.TIME_RANGE,
  title: 'dj-时间区间',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonColumn',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          // { ...commonBasicSetter.placeholder }, // 占位提示
          // ...tipSetter, // 辅助提示+注释说明+注释图标
          { ...commonBasicSetter.status }, // 是否启用
          { ...commonBasicSetter.isFocusDisplay }, // 启用标题在内
          { ...commonDataTypeSetter('TIME_RANGE') }, // 数据类型
        ],
      },
      { ...commonLibRuleSetter },
      { ...commonAthRuleSetter },
      { ...commonAthMonacoEditorSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        // parentWhitelist: parentWhitelistMap.get(AthenaComponentType.TIME_RANGE) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-时间区间',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/TIME_RANGE.svg`,
    schema: {
      componentName: AthenaComponentType.TIME_RANGE,
      title: 'dj-时间区间',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.TIME_RANGE,
          headerName: '时间区间',
          placeholder: 'hhmmss',
          schema: '',
          path: '',
          disabled: false,
          editable: true,
          isFocusDisplay: false,
          fieldType: 'json',
          dataType: AthenaDataType.ARRAY,
          rules: { value: [] },
          lang: {
            headerName: {
              zh_CN: '时间区间',
              zh_TW: '時間區間',
              en_US: 'Time Range',
            },
          },
        },
      },
    },
  },
];

export default {
  ...AthTimeRangeMeta,
  snippets,
};
