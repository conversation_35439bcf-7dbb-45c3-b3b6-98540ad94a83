import {
  IPublicModelSettingField,
  IPublicTypeComponentMetadata,
  IPublicTypeSnippet,
} from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSchemaNotRequiredSetter,
  commonAthRuleSetter,
  commonBasicSetter,
  commonLibRuleSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';
import { getAppHelperUtils } from "@/tools";

const LcdpImageMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.IMAGE,
  title: 'dj-图片',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpImage',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSchemaNotRequiredSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          {
            getValue: (target) => {
              const getConfigByKey = getAppHelperUtils(target.node!, 'getConfigByKey');
              const version = getConfigByKey('AthVersion');
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return { src: dslInfo?.src, useDMC: version === '1.0' };
            },
            setValue: (target, value) => {
              target?.node?.setPropValue('dslInfo.src', value);
            },

            setter: {
              isDynamic: false,
              componentName: 'LcdpImageSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-图片上传',
                    tooltip: {
                      title: 'dj-固定图片，未选择关联字段时生效',
                    },
                  },
                  layout: 'vertical',
                },
              },
            },
          },
          {
            name: 'dslInfo.fixedSize',
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-固定尺寸',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.height',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return !!dslInfo?.fixedSize;
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-高度',
                  },
                  componentType: 'number',
                  componentProps: {
                    step: 1,
                  },
                  formItemRules: [
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
          {
            name: 'dslInfo.width',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return !!dslInfo?.fixedSize;
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-宽度',
                  },
                  componentType: 'number',
                  componentProps: {
                    step: 1,
                  },
                  formItemRules: [
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
          {
            name: 'dslInfo.objectFit',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return !!dslInfo?.fixedSize;
            },
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-填充方式',
                  },
                  layout: 'vertical',
                  componentType: 'select',
                  componentProps: {
                    options: [
                      { label: 'dj-铺满', value: 'fill' },
                      { label: 'dj-等比例铺满', value: 'cover' },
                      { label: 'dj-等比例缩放', value: 'contain' },
                      { label: 'dj-实际尺寸', value: 'none' },
                    ],
                  },
                },
              },
            },
          },
        ],
      },
      { ...commonLibRuleSetter },
      { ...commonAthRuleSetter },
      { ...commonAthMonacoEditorSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        // parentWhitelist: parentWhitelistMap.get(AthenaComponentType.IMAGE) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-图片',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/IMAGE.svg`,
    schema: {
      componentName: AthenaComponentType.IMAGE,
      title: 'dj-图片',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.IMAGE,
          headerName: '图片',
          schema: '',
          path: '',
          // srcType: 'upload', // 'upload' 手动上传 'link' 图片链接
          src: '',
          fixedSize: false,
          width: '',
          height: '',
          objectFit: 'contain',
          lang: {
            headerName: {
              zh_CN: '图片',
              zh_TW: '',
              en_US: 'text',
            },
          },
        },
      },
    },
  },
];

export default {
  ...LcdpImageMeta,
  snippets,
};
