import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType } from '../common/common.config';
import { AthenaDataType } from '../common/common.type';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  commonUploadSetter,
  tipSetter,
  commonBasicSetter,
  commonUploadContentSetter,
  commonLibRuleSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const AthFormUploadMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.FORM_UPLOAD_DECOUPLE,
  title: 'dj-附件上传',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonColumn',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      {
        title: 'dj-内容',
        type: 'group',
        display: 'accordion',
        items: [
          { ...commonBasicSetter.headerName },
          ...tipSetter,
          { ...commonBasicSetter.isFocusDisplay },
          ...commonUploadContentSetter
        ],
      },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [...commonUploadSetter],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonLibRuleSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
      // nestingRule: {
      //   parentWhitelist: parentWhitelistMap.get(AthenaComponentType.FORM_UPLOAD_DECOUPLE) ?? [],
      // },
    },
    advanced: {},
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-附件上传',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/FORM_UPLOAD.svg`,
    schema: {
      componentName: AthenaComponentType.FORM_UPLOAD_DECOUPLE,
      title: 'dj-附件上传',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.FORM_UPLOAD_DECOUPLE,
          headerName: '附件上传',
          placeholder: '',
          schema: '',
          path: '',
          isFocusDisplay: false,
          dataType: AthenaDataType.OBJECT,
          lang: {
            headerName: {
              zh_CN: '附件上传',
              zh_TW: '附檔上傳',
              en_US: 'upload file',
            },
            extraContent: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
            tooltipTitle: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
          },
          iconType: '',
          tooltipMode: 'normal', // 有注释iconType的都要加
          buckets: '',
          apiMode: 'dataConnector',
          attribute: {
            uploadEnable: false,
            uploadCategory: '',
            fileExtensions: [],
            fileCount: '',
            fileMaxSize: '',
            draggable: false,
            disableAam: true,
            enableEffectAfterSubmit: false,
            onlyDeleteByOwner: false,
          },
        },
      },
    },
  },
];

export default {
  ...AthFormUploadMeta,
  snippets,
};
