import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  commonBasicSetter,
  commonLibRuleSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const AthTagMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.ATH_TAG,
  title: 'dj-标签类控件',
  group: 'dj-标准组件',
  category: 'dj-标签组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'AthTag',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          {
            name: 'dslInfo.size',
            setter: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo') || {};
              const { tagType } = dslInfo;
              return {
                isDynamic: false,
                componentName: 'AthCommonSetter',
                props: {
                  options: {
                    titleProps: {
                      setterTitle: 'dj-标签尺寸',
                    },
                    layout: 'vertical',
                    componentType: 'select',
                    componentProps:
                      tagType === 'stamp'
                        ? {
                            size: 'small',
                            options: [
                              { label: 'dj-超大74px', value: 'big' },
                              { label: 'dj-大62px', value: 'large' },
                              { label: 'dj-中52px', value: 'default' },
                              { label: 'dj-小40px', value: 'small' },
                            ],
                          }
                        : {
                            size: 'small',
                            options: [
                              { label: '超大28px', value: 'big' },
                              { label: 'dj-大20px', value: 'large' },
                              { label: 'dj-中16px', value: 'default' },
                              { label: 'dj-小14px', value: 'small' },
                            ],
                          },
                  },
                },
              };
            },
          },
          {
            name: 'dslInfo.stateConfig',
            getValue: (target) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              const { schema, stateConfig, tagType } = dslInfo;
              return { schema, stateConfig, tagType };
            },
            setter: {
              isDynamic: false,
              componentName: 'AthTagConfigSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-标签管理',
                  },
                  layout: 'vertical',
                },
              },
            },
          },
        ],
      },
      { ...commonAthRuleSetter },
      { ...commonLibRuleSetter },
      { ...commonAthMonacoEditorSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        // parentWhitelist: parentWhitelistMap.get(AthenaComponentType.ATH_TAG) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const faceSnippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-面性标签',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/ATH_TAG.png`,
    schema: {
      componentName: AthenaComponentType.ATH_TAG,
      title: 'dj-面性标签',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.ATH_TAG,
          headerName: '面性标签',
          schema: '',
          path: '',
          size: 'large',
          tagType: 'face', // face line color
          stateConfig: [],
          lang: {
            headerName: {
              zh_CN: '面性标签',
              zh_TW: '面性標籤',
              en_US: 'Facial Tag',
            },
          },
        },
      },
    },
  },
];

const lineSnippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-线性标签',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/ATH_TAG222.svg`,
    schema: {
      componentName: AthenaComponentType.ATH_TAG,
      title: 'dj-线性标签',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.ATH_TAG,
          headerName: '线性标签',
          schema: '',
          path: '',
          size: 'large',
          tagType: 'line', // face line color
          stateConfig: [],
          lang: {
            headerName: {
              zh_CN: '线性标签',
              zh_TW: '線性標籤',
              en_US: 'Line Tag',
            },
          },
        },
      },
    },
  },
];

const colorSnippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-深色标签',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/ATH_TAG333.svg`,
    schema: {
      componentName: AthenaComponentType.ATH_TAG,
      title: 'dj-深色标签',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.ATH_TAG,
          headerName: '深色标签',
          schema: '',
          path: '',
          size: 'large',
          tagType: 'color', // face line color
          stateConfig: [],
          lang: {
            headerName: {
              zh_CN: '深色标签',
              zh_TW: '深色標籤',
              en_US: 'Dark Tag',
            },
          },
        },
      },
    },
  },
];

const stampSnippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-戳形标签',
    // screenshot: 'http://localhost:3008/img/64.svg',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/ATH_TAG444.svg`,
    schema: {
      componentName: AthenaComponentType.ATH_TAG,
      title: 'dj-戳形标签',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.ATH_TAG,
          headerName: '戳形标签',
          schema: '',
          path: '',
          size: 'large',
          tagType: 'stamp', // face line color stamp
          stateConfig: [],
          lang: {
            headerName: {
              zh_CN: '戳形标签',
              zh_TW: '戳形標籤',
              en_US: 'Stamp Tag',
            },
          },
        },
      },
    },
  },
];

export default {
  ...AthTagMeta,
  snippets: [...faceSnippets, ...lineSnippets, ...colorSnippets, ...stampSnippets],
};
