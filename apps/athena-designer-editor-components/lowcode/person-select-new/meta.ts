import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import { AthenaDataType } from '../common/common.type';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  commonBasicSetter,
  commonDataTypeSetter,
  commonLibRuleSetter,
} from '../common/common-meta-info.config';
import { envParams } from "@/env";

const PersonSelectNewMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.PERSON_SELECT_NEW,
  title: 'dj-人员选择（新）',
  group: 'dj-标准组件',
  category: 'dj-功能组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'PersonSelect',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          { ...commonBasicSetter.status }, // 是否启用
          { ...commonBasicSetter.enableSearch },
          { ...commonBasicSetter.expandAll },
          { ...commonDataTypeSetter('PERSON_SELECT_NEW') },
        ],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonLibRuleSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        parentWhitelist: parentWhitelistMap.get(AthenaComponentType.PERSON_SELECT_NEW) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-人员选择（新）',
    screenshot:
      `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/PERSON_SELECT.svg`,
    schema: {
      componentName: AthenaComponentType.PERSON_SELECT_NEW,
      title: 'dj-人员选择（新）',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.PERSON_SELECT_NEW,
          headerName: '人员选择（新）',
          schema: '',
          path: '',
          disabled: false,
          editable: true,
          dataType: AthenaDataType.ARRAY,
          expandAll: true,
          enableSearch: true,
          lang: {
            headerName: {
              zh_CN: '人员选择（新）',
              zh_TW: '人員選擇（新）',
              en_US: 'person select（new）',
            },
          },
        },
      },
    },
  },
];

export default {
  ...PersonSelectNewMeta,
  snippets,
};
