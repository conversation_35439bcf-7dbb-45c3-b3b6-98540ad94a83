import React, { createElement, Fragment, useMemo } from 'react';
import { SearchOutlined, SortAscendingOutlined, SettingOutlined } from '@ant-design/icons';
import { Checkbox, Tooltip } from 'antd';
import './index.scss';
import { getAppHelperUtils, getI18n } from '../../tools';
import { AthenaComponentType } from '../../../lowcode/common/common.config';
import { isEmpty } from 'lodash';
import { DeleteTwoTone } from '@ant-design/icons';

export interface LcdpAthenaTableProps {
  children: any;
  dslInfo: any;
  _leaf: any;
}

const LcdpAthenaTable: React.FC<LcdpAthenaTableProps> = (props: LcdpAthenaTableProps) => {
  const { dslInfo, children, _leaf } = props;
  const { t, language } = getI18n(_leaf);
  const { athLowCodeConfig = {}, gridSettings } = props._leaf?.document?.root.propsData;
  const { AthStatusInfo, AthFieldTree } = athLowCodeConfig;
  const getConfigByKey = getAppHelperUtils(_leaf, 'getConfigByKey');
  const version = getConfigByKey('AthVersion');
  const AthFieldTreeMap = getConfigByKey('AthFieldTreeMap') || new Map();
  const treeData = AthFieldTreeMap?.get(dslInfo?.queryInfo?.dataConnectorId);
  const actualTree = version === '2.0' ? treeData : AthFieldTree;
  // 业务逻辑，是否为主表
  const isMainTable = useMemo(() => {
    const { path = '', schema = '' } = dslInfo;
    if (!path && !schema) {
      return false;
    }
    const { path: rootPath = '', data_name: rootSchema = '' } = actualTree?.[0] || {};
    if (!rootPath && !rootSchema) {
      return false;
    }
    return path === rootPath && schema === rootSchema;
  }, [dslInfo]);

  // 是否开启了高级查询
  const isAdvancedSearch = useMemo(() => {
    const { path = '', schema = '' } = dslInfo;
    const gridSetting = gridSettings.find((gridSetting) => {
      return gridSetting.gridPath === path && gridSetting.gridSchema === schema;
    });
    return gridSetting?.searchInfo?.length > 0;
  }, [gridSettings, dslInfo]);

  // 是否可能表格设置
  const canSetting = useMemo(() => {
    return !dslInfo?.setting?.hideDefaultToolbar?.find(() => 'setting');
  }, [dslInfo]);

  const isShowStatus = useMemo(() => {
    return version !== '2.0' && !isEmpty(AthStatusInfo) && isMainTable;
  }, [version, AthStatusInfo, isMainTable]);

  const childInfo = useMemo(() => {
    const { slots, columnDefs, leftPinnedColumnDefs, rightPinnedColumnDefs } = (
      children || []
    ).reduce(
      (acc, cur) => {
        if (cur.props?.dslInfo?.type === AthenaComponentType.DYNAMIC_OPERATION) {
          acc.slots.push(cur);
        } else if (cur.props?.dslInfo.pinned === 'left') {
          acc.leftPinnedColumnDefs.push(cur);
        } else if (cur.props?.dslInfo.pinned === 'right') {
          acc.rightPinnedColumnDefs.push(cur);
        } else {
          acc.columnDefs.push(cur);
        }
        return acc;
      },
      { slots: [], columnDefs: [], leftPinnedColumnDefs: [], rightPinnedColumnDefs: [] },
    );
    return { slots, columnDefs, leftPinnedColumnDefs, rightPinnedColumnDefs };
  }, [children, dslInfo]);

  const title = dslInfo?.lang?.tableTitle?.[language] ?? dslInfo?.tableTitle;

  const { rowDelete = false } = dslInfo;

  return (
    <div className="ath-athena-table">
      {/* {title && <div className="title-bar">{title}</div>} */}
      {isMainTable && (
        <div className="title-bar">
          <Tooltip title={t('dj-关联字段是当前数据源根节点的表格就是主表表格')}>
            {t('dj-主表')}
          </Tooltip>
        </div>
      )}
      <div className="tool-bar">
        <div className="label-group">{title && <div className="label-item">{title}</div>}</div>
        <div className="operation-group">{childInfo.slots}</div>
        <div className="icon-group">
          {isAdvancedSearch && <SearchOutlined />}
          {dslInfo.isSort && <SortAscendingOutlined />}
          {canSetting && <SettingOutlined />}
        </div>
      </div>
      <div className="content">
        {dslInfo.checkbox && (
          <div className="base-item ath-checkbox">
            <div className="header">
              <Checkbox></Checkbox>
            </div>
            <div className="content">
              <Checkbox></Checkbox>
              {rowDelete && (
                <div>
                  <DeleteTwoTone />
                </div>
              )}
            </div>
          </div>
        )}
        {dslInfo.rowIndex && (
          <div className="base-item ath-row-index">
            <div className="header">{t('dj-序号')}</div>
            <div className="content">
              <span>1</span>
            </div>
          </div>
        )}
        {!childInfo.columnDefs?.length &&
        !childInfo.leftPinnedColumnDefs?.length &&
        !childInfo.rightPinnedColumnDefs?.length ? (
          <div className="empty-table-container-placeholder  lc-container-placeholder">
            {t('dj-拖拽组件或模板到这里')}
          </div>
        ) : (
          <Fragment>
            {childInfo.leftPinnedColumnDefs?.length > 0 && (
              <div className="column-pinned">{childInfo.leftPinnedColumnDefs}</div>
            )}
            {childInfo.columnDefs?.length > 0 && (
              <div className="column-normal">{childInfo.columnDefs}</div>
            )}
            {childInfo.rightPinnedColumnDefs?.length > 0 && (
              <div className="column-pinned">{childInfo.rightPinnedColumnDefs}</div>
            )}
          </Fragment>
        )}
        {isShowStatus && (
          <div className="base-item ath-status">
            <div className="header">{t('dj-状态')}</div>
            <div className="content"></div>
          </div>
        )}
      </div>
    </div>
  );
};

LcdpAthenaTable.displayName = 'LcdpAthenaTable';
export default LcdpAthenaTable;
