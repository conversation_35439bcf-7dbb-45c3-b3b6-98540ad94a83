.ath-modal {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;

  .ath-modal-mask {
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 1;
  }

  .ath-modal-content {
    position: relative;
    z-index: 2;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.18);
    min-width: 400px;
    min-height: 200px;
    padding: 24px;
    display: flex;
    flex-direction: column;
  }
}
.content {
  height: 100%;
}
.feedback-content {
  color: #666;
  font-size: 16px;
  padding: 32px 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.title-bar {
  display: flex;
  align-items: center;
  font-size: 14px;
  margin-bottom: 7px;
}