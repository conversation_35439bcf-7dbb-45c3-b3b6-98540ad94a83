import React, { createElement, useMemo, useState } from 'react';
import './index.scss';
import { getI18n } from '../../tools';
import { AthenaComponentType } from '../../../lowcode/common/common.config';
import { InfoCircleOutlined,WarningOutlined,CheckCircleTwoTone ,CloseCircleOutlined } from '@ant-design/icons';

export interface AthModalProps {
  children: any;
  dslInfo: any;
  _leaf: any;
}

const AthModal: React.FC<AthModalProps> = (props: AthModalProps) => {
  const { dslInfo, _leaf, children } = props;
  const { t, language } = getI18n(_leaf);
  console.log(children,555555566);
  // 分类 children
  const childInfo = useMemo(() => {
    const { slots, columnDefs } = (children || []).reduce(
      (acc, cur) => {
        const type = cur.props?.dslInfo?.type;
         if (type === AthenaComponentType.DYNAMIC_OPERATION || type === AthenaComponentType.ICON || type === AthenaComponentType.BUTTON_GROUP) {
          acc.slots.push(cur);
        } else {
          acc.columnDefs.push(cur);
        }
        return acc;
      },
      { slots: [], columnDefs: [] }
    );

    const wrapperChildren = React.Children.map(columnDefs, (child, index) => {
      return React.createElement('div', { key: index, className: 'wrapped-child' }, child);
    });

    return { slots, wrapperChildren};
  }, [children]);

  const title = dslInfo?.lang?.title?.[language] ?? dslInfo?.title;
  const titleIcon = dslInfo?.titleIcon;
  const fontIcon = dslInfo?.fontIcon ?? false;
  const textContent = dslInfo?.textContent;
  const mask = dslInfo?.mask ?? true;
  const maskClosable = dslInfo?.maskClosable ?? false;

  const [visible, setVisible] = useState(true);
  const handleMaskClick = () => {
    if (maskClosable) setVisible(false);
  };
  if (!visible) return null;

  const size = dslInfo?.size || 'medium';
  const sizeWidthMap = {
    small: 480,
    medium: 720,
    large: 960,
    xlarge: 1200,
  };
  const sizeHeightMap = {
    small: 240,
    medium: 360,
    large: 480,
    xlarge: 600,
  };
  const modalWidth = sizeWidthMap[size] || 720;
  const modalHeight = sizeHeightMap[size] || 360;
  const padding = dslInfo?.padding || '24px';

  const renderTitleIcon = () => {
    if (titleIcon) {
      if (titleIcon === 'default') {
        return <InfoCircleOutlined style={{ color: '#1677ff' }} />;
      }
      if (titleIcon === 'warning') {
        return <WarningOutlined style={{ color: '#ffc107' }} />;
      }
      if (titleIcon === 'success') {
        return <CheckCircleTwoTone style={{ color: '#4CAF50' }} />;
      }
      if (titleIcon === 'failure') {
        return <CloseCircleOutlined style={{ color: '#f44336' }} />;
      }
      return null;
    } else if (fontIcon) {
      // return <InfoCircleOutlined style={{ color: '#1677ff' }} />;
     return <div className="tool-bar">{[childInfo.slots[1]]}</div>
    }
    return null;
  };

  return (
    <div className="ath-modal">
      {mask && <div className="ath-modal-mask" onClick={handleMaskClick} />}
      
      <div className="ath-modal-content" style={{ width: modalWidth, height: modalHeight }}>
        {/* 头部 */}
        {title && (
          <div className='title-bar'>
            {renderTitleIcon()}
            <span style={{ marginLeft: renderTitleIcon() ? 8 : 0 }}>{title}</span>
          </div>
        )}
        {/* 内容区 */}
        <div className="content" style={{ padding }}>
          {textContent
            ? <div className="feedback-content">{textContent}</div>
            : (!childInfo.wrapperChildren?.length ? (
                <div className="empty-form-list-container-placeholder lc-container-placeholder">
                  {t('dj-拖拽组件或模板到这里')}
                </div>
              ) : (
                childInfo.wrapperChildren
              ))
          }
        </div>
        {/* 底部栏 */}
        <div className="modal-footer">{childInfo.slots[0]}</div>
      </div>
    </div>
  );
};

AthModal.displayName = 'AthModal';
export default AthModal;
